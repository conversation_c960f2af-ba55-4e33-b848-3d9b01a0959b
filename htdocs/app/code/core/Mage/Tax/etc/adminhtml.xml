<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Tax
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <menu>
        <sales>
            <children>
                <tax translate="title" module="tax">
                    <title>Tax</title>
                    <sort_order>500</sort_order>
                    <children>
                        <rules translate="title" module="tax">
                            <title>Manage Tax Rules</title>
                            <action>adminhtml/tax_rule</action>
                        </rules>
                        <rates translate="title" module="tax">
                            <title>Manage Tax Zones &amp; Rates</title>
                            <action>adminhtml/tax_rate</action>
                        </rates>
                        <import_export translate="title" module="tax">
                            <title>Import / Export Tax Rates</title>
                            <action>adminhtml/tax_rate/importExport</action>
                        </import_export>
                        <classes_customer translate="title" module="tax">
                            <title>Customer Tax Classes</title>
                            <action>adminhtml/tax_class_customer</action>
                        </classes_customer>
                        <classes_product translate="title" module="tax">
                            <title>Product Tax Classes</title>
                            <action>adminhtml/tax_class_product</action>
                        </classes_product>
                    </children>
                </tax>
            </children>
        </sales>
    </menu>
    <acl>
        <resources>
            <admin>
                <children>
                    <sales>
                        <children>
                            <tax translate="title" module="tax">
                                <title>Tax</title>
                                <sort_order>500</sort_order>
                                <children>
                                    <classes_customer translate="title">
                                        <title>Customer Tax Classes</title>
                                        <sort_order>0</sort_order>
                                    </classes_customer>
                                    <classes_product translate="title">
                                        <title>Product Tax Classes</title>
                                        <sort_order>10</sort_order>
                                    </classes_product>
                                    <import_export translate="title">
                                        <title>Import / Export Tax Rates</title>
                                        <sort_order>20</sort_order>
                                    </import_export>
                                    <rates translate="title">
                                        <title>Manage Tax Zones &amp; Rates</title>
                                        <sort_order>30</sort_order>
                                    </rates>
                                    <rules translate="title">
                                        <title>Manage Tax Rules</title>
                                        <sort_order>40</sort_order>
                                    </rules>
                                </children>
                            </tax>
                        </children>
                    </sales>
                    <system>
                        <children>
                            <config>
                                <children>
                                    <tax translate="title">
                                        <title>Tax Section</title>
                                        <sort_order>55</sort_order>
                                    </tax>
                                </children>
                            </config>
                        </children>
                    </system>
                </children>
            </admin>
        </resources>
    </acl>
</config>
