<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Log
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <sections>
        <system>
            <groups>
                <log translate="label" module="log">
                    <label>Log</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>200</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>0</show_in_website>
                    <show_in_store>0</show_in_store>
                    <fields>
                        <enable_log translate="label">
                            <label>Enable Log</label>
                            <frontend_type>select</frontend_type>
                            <source_model>log/adminhtml_system_config_source_loglevel</source_model>
                            <sort_order>1</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>If set to "No" this option disables recently viewed products and compare products functionality for not logged in customers.</comment>
                        </enable_log>
                        <clean_after_day translate="label">
                            <label>Save Log, Days</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>2</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </clean_after_day>
                        <enabled translate="label">
                            <label>Enable Log Cleaning</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>3</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </enabled>
                        <time translate="label">
                            <label>Start Time</label>
                            <frontend_type>time</frontend_type>
                            <sort_order>4</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </time>
                        <frequency translate="label">
                            <label>Frequency</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_cron_frequency</source_model>
                            <backend_model>adminhtml/system_config_backend_log_cron</backend_model>
                            <sort_order>5</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </frequency>
                        <error_email translate="label">
                            <label>Error Email Recipient</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-email</validate>
                            <sort_order>6</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </error_email>
                        <error_email_identity translate="label">
                            <label>Error Email Sender</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_email_identity</source_model>
                            <sort_order>7</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </error_email_identity>
                        <error_email_template translate="label">
                            <label>Error Email Template</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_email_template</source_model>
                            <sort_order>8</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </error_email_template>
                    </fields>
                </log>
            </groups>
        </system>
    </sections>
</config>
