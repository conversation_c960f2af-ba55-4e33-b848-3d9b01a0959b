<?php
$store = Mage::app()->getStore();
$code = $store->getCode();
$aspect_ratio = Mage::getStoreConfig("porto_settings/category/aspect_ratio", $code);
$ratio_width = Mage::getStoreConfig("porto_settings/category/ratio_width", $code);
$ratio_height = Mage::getStoreConfig("porto_settings/category/ratio_height", $code);
$add_upsell_background = Mage::getStoreConfig("porto_settings/product_view/add_upsell_background", $code);
$is_logged_user = Mage::getSingleton('customer/session')->isLoggedIn();

if (!$ratio_width)
    $ratio_width = 300;
if (!$ratio_height)
    $ratio_height = 400;
?>
<?php if ($this->getItems()->getSize()): ?>
    <?php if ($add_upsell_background): ?>
        <div class="container">
    <?php endif; ?>
    <div class="box-collateral box-up-sell category-products owl-top-narrow">
        <h2><span><?php echo $this->__('Related Products') ?></span></h2>
        <?php $_columnCount = Mage::getStoreConfig("porto_settings/product_view/upsell_columns", $code); ?>
        <?php
        if (!$_columnCount)
            $_columnCount = 4;
        ?>
        <div class="owl-carousel owl-theme" id="block-related">
            <?php foreach ($this->getItems() as $_product): ?>
                <?php $product = Mage::getModel('catalog/product')->load($_product->getId()); ?>
                <div class="item">
                    <div class="item-area">
                        <div class="product-image-area">
                            <a href="<?php echo $_product->getProductUrl() ?>"
                               title="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?>"
                               class="product-image">
                                <?php echo Mage::helper('onsale')->getProductLabelHtml($_product); ?>
                                <?php
                                if (Mage::getStoreConfig("porto_settings/category/alternative_image", $code)) {
                                    ?>
                                    <img class="defaultImage"
                                         src="<?php if ($aspect_ratio): ?><?php echo $this->helper('catalog/image')->init($_product, 'small_image')->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($ratio_width); ?><?php else: ?><?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize($ratio_width, $ratio_height)->keepAspectRatio(TRUE); ?><?php endif; ?>"
                                         alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?>"/>
                                    <img class="hoverImage"
                                         src="<?php if (Mage::getStoreConfig("porto_settings/category/aspect_ratio", $code)): ?><?php echo $this->helper('catalog/image')->init($_product, 'thumbnail')->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($ratio_width); ?><?php else: ?><?php echo $this->helper('catalog/image')->init($_product, 'thumbnail')->resize($ratio_width, $ratio_height)->keepAspectRatio(TRUE); ?><?php endif; ?>"
                                         alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'thumbnail'), null, true) ?>"/>
                                    <?php
                                } else {
                                    ?>
                                    <img
                                        src="<?php if ($aspect_ratio): ?><?php echo $this->helper('catalog/image')->init($_product, 'small_image')->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($ratio_width); ?><?php else: ?><?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize($ratio_width, $ratio_height)->keepAspectRatio(TRUE); ?><?php endif; ?>"
                                        alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?>"/>
                                    <?php
                                }
                                ?>
                            </a>
                            <?php

                            $sale_label = false;


                            // Get the Special Price
                            $specialprice = $product->getFinalPrice();
                            $orgprice = $product->getPrice();
                            // Get the Special Price FROM date
                            $now = date("Y-m-d");
                            $specialPriceFromDate = substr($product->getSpecialFromDate(), 0, 10);
                            // Get the Special Price TO date
                            $specialPriceToDate = substr($product->getSpecialToDate(), 0, 10);
                            // Get Current date
                            $today = time();

                            if ($specialprice < $orgprice) {
                                $save_percent = 100 - round(($specialprice / $orgprice) * 100);
                                if ($specialPriceToDate != '' || $specialPriceFromDate != '') {
                                    if (($specialPriceToDate != '' && $specialPriceFromDate != '' && $now >= $specialPriceFromDate && $now <= $specialPriceToDate) || ($specialPriceToDate == '' && $now >= $specialPriceFromDate) || ($specialPriceFromDate == '' && $now <= $specialPriceToDate)) {
                                        if (Mage::getStoreConfig("porto_settings/product_label/sale", $code)) {
                                            $sale_label = true;
                                            ?>
                                            <div class="product-label" style="right: 10px;"><span
                                                    class="sale-product-icon"><?php if (Mage::getStoreConfig("porto_settings/product_label/sale_label_type", $code)): ?><?php echo "-" . $save_percent . "%"; ?><?php else: ?><?php echo Mage::getStoreConfig("porto_settings/product_label/sale_label_text", $code); ?><?php endif; ?></span>
                                            </div>
                                            <?php
                                        }
                                    }
                                }
                            }
                            ?>
                            <?php
                            $now = date("Y-m-d");
                            $newsFrom = substr($_product->getData('news_from_date'), 0, 10);
                            $newsTo = substr($_product->getData('news_to_date'), 0, 10);
                            if ($newsTo != '' || $newsFrom != '') {
                                if (($newsTo != '' && $newsFrom != '' && $now >= $newsFrom && $now <= $newsTo) || ($newsTo == '' && $now >= $newsFrom) || ($newsFrom == '' && $now <= $newsTo)) {
                                    if (Mage::getStoreConfig("porto_settings/product_label/new", $code)) {
                                        ?>
                                        <div class="product-label"
                                             style="right: 10px; <?php echo ($sale_label) ? "top: 40px;" : ""; ?>"><span
                                                class="new-product-icon"><?php echo Mage::getStoreConfig("porto_settings/product_label/new_label_text", $code); ?></span>
                                        </div>
                                        <?php
                                    }
                                }
                            }
                            ?>
                        </div>
                        <div class="details-area">
                            <h2 class="product-name"><a href="<?php echo $_product->getProductUrl() ?>"
                                                        title="<?php echo $this->stripTags($_product->getName(), null, true) ?>"><?php echo $_product->getName() ?></a>
                            </h2>
                            <?php if (Mage::getStoreConfig("porto_settings/category/rating_star", $code)): ?>
                                <?php if (Mage::helper('catalog')->isModuleEnabled('Mage_Review')): ?>
                                    <?php
                                    $review_html = $this->getReviewsSummaryHtml($product, 'short');
                                    if ($review_html) {
                                        echo $review_html;
                                    } else {
                                        ?>
                                        <div class="ratings">
                                            <div class="rating-box">
                                                <div class="rating" style="width:0"></div>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                <?php else: ?>
                                    <div class="ratings">
                                        <div class="rating-box">
                                            <div class="rating" style="width:0"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if (Mage::getStoreConfig("porto_settings/category/product_price", $code) && $is_logged_user): ?>
                                <?php echo $this->getPriceHtml($_product, true) ?>
                            <?php endif; ?>
                            <?php if (Mage::getStoreConfig("porto_settings/category/actions", $code)): ?>
                                <div class="actions">
                                    <?php if (Mage::getStoreConfig("porto_settings/category/qty_field", $code) && Mage::getSingleton('customer/session')->isLoggedIn()): ?>
                                        <?php if ($product->isSaleable()): ?>
                                            <?php if (!($product->getTypeInstance(true)->hasOptions($product) || $product->isGrouped())) : ?>
                                                <?php if (!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)): ?>
                                                    <form id="addtocart_form_<?php echo $_product->getId(); ?>" action="<?php echo $this->getAddToCartUrl($_product) ?>" method="post">
                                                    <input type="hidden" name="form_key"
                                                           value="<?php echo Mage::getSingleton('core/session')->getFormKey(); ?>"/>
                                                <?php endif; ?>
                                                <div class="qty-field">
                                                    <label for="qty"><?php echo $this->__('Qty:') ?></label>
                                                    <div class="qty-holder">
                                                        <input type="text" name="qty"
                                                               id="qty_<?php echo $_product->getId(); ?>" maxlength="12"
                                                               value="<?php echo $product->getProductDefaultQty() * 1 ?>"
                                                               title="<?php echo $this->__('Qty') ?>"
                                                               class="input-text qty"/>
                                                        <div class="qty-changer">
                                                            <a href="javascript:void(0)" class="qty_inc"><i
                                                                    class="icon-up-dir"></i></a>
                                                            <a href="javascript:void(0)" class="qty_dec"><i
                                                                    class="icon-down-dir"></i></a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php if (!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)): ?>
                                                    </form>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if (Mage::getStoreConfig("porto_settings/category_grid/show_addtolinks", $code) && $this->helper('wishlist')->isAllow()) : ?>
                                        <a href="<?php if (Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)): ?>javascript:void(0)<?php else: ?><?php echo $this->helper('wishlist')->getAddUrl($_product) ?><?php endif; ?>"
                                           <?php if (Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)): ?>onclick="ajaxWishlist(this,'<?php echo $this->helper('wishlist')->getAddUrl($_product) ?>','<?php echo $_product->getId() ?>');"<?php endif; ?>
                                           class="addtowishlist" title="<?php echo $this->__('Add to Wishlist') ?>"><i
                                                class="icon-wishlist"></i></a>
                                    <?php endif; ?>
                                    <?php if ($product->isSaleable()): ?>
                                        <?php if (Mage::getStoreConfig("porto_settings/category_grid/show_addtocart", $code)): ?>
                                            <?php if (!($product->getData('has_options') || $product->isGrouped())) : ?>
                                                <a href="javascript:void(0)" class="addtocart"
                                                   title="<?php echo $this->__('Add to Cart') ?>"
                                                   <?php if (!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)): ?>onclick="document.getElementById('addtocart_form_<?php echo $_product->getId(); ?>').submit()"
                                                   <?php else: ?>onclick="setLocationAjax(this,'<?php echo $this->getAddToCartUrl($_product) ?>','<?php echo $_product->getId(); ?>')"<?php endif; ?>><i
                                                        class="icon-cart"></i><span>&nbsp;<?php echo $this->__('Add to Cart') ?></span></a>
                                            <?php else : ?>
                                                <a href="<?php if (Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)): ?>javascript:showOptions('<?php echo $_product->getId() ?>')<?php else: ?><?php echo $this->getAddToCartUrl($_product) ?><?php endif; ?>"
                                                   class="addtocart" title="<?php echo $this->__('Add to Cart') ?>"><i
                                                        class="icon-cart"></i><span>&nbsp;<?php echo $this->__('Add to Cart') ?></span></a>
                                                <a href='<?php echo $this->getUrl('ajaxcart/index/options', array('product_id' => $_product->getId())); ?>'
                                                   class='fancybox' id='fancybox<?php echo $_product->getId() ?>'
                                                   style='display:none'>Options</a>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <a href="javascript:void(0);" class="addtocart outofstock"
                                           title="<?php echo $this->__('Out of stock') ?>"><?php echo $this->__('Out of stock') ?></span></a>
                                    <?php endif; ?>
                                    <?php if (Mage::getStoreConfig("porto_settings/category_grid/show_addtolinks", $code) && Mage::getStoreConfig("porto_settings/category/compare", $code) && $_compareUrl = $this->getAddToCompareUrl($_product)): ?>
                                        <a href="<?php if (Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)): ?>javascript:void(0)<?php else: ?><?php echo $_compareUrl ?><?php endif; ?>"
                                           <?php if (Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)): ?>onclick="ajaxCompare(this,'<?php echo $_compareUrl ?>','<?php echo $_product->getId() ?>');"<?php endif; ?>
                                           class="comparelink" title="<?php echo $this->__('Add to Compare') ?>"><i
                                                class="icon-compare"></i></a>
                                    <?php endif; ?>
                                    <div class="clearer"></div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

        </div>
        <script type="text/javascript">
            jQuery(function ($) {
                $("#block-related").owlCarousel({
                    lazyLoad: true,
                    itemsCustom: [[0, 2], [320, 2], [480, 2], [768, 3], [992, 3], [1200, <?php echo $_columnCount; ?>]],
                    responsiveRefreshRate: 50,
                    slideSpeed: 200,
                    paginationSpeed: 500,
                    scrollPerPage: false,
                    stopOnHover: true,
                    rewindNav: true,
                    rewindSpeed: 600,
                    pagination: true,
                    navigation: false,
                    autoPlay: true,
                    navigationText: ["<i class='icon-left-open'></i>", "<i class='icon-right-open'></i>"]
                });
            });
        </script>
    </div>
    <?php if ($add_upsell_background): ?>
        </div>
    <?php endif; ?>
<?php endif; ?>
