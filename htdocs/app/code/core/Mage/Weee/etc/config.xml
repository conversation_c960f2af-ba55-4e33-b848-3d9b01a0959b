<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Weee
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_Weee>
            <version>1.6.0.0</version>
        </Mage_Weee>
    </modules>
    <global>
        <models>
            <weee>
                <class>Mage_Weee_Model</class>
                <resourceModel>weee_resource</resourceModel>
            </weee>
            <weee_resource>
                <class>Mage_Weee_Model_Resource</class>
                <deprecatedNode>weee_mysql4</deprecatedNode>
                <entities>
                    <tax>
                        <table>weee_tax</table>
                    </tax>
                    <discount>
                        <table>weee_discount</table>
                    </discount>
                </entities>
            </weee_resource>
        </models>
        <resources>
            <weee_setup>
                <setup>
                    <module>Mage_Weee</module>
                    <class>Mage_Weee_Model_Resource_Setup</class>
                </setup>
            </weee_setup>
        </resources>
        <blocks>
            <weee>
                <class>Mage_Weee_Block</class>
            </weee>
        </blocks>
        <events>
            <catalog_prepare_price_select>
                <observers>
                    <weee>
                        <type>model</type>
                        <class>weee/observer</class>
                        <method>prepareCatalogIndexSelect</method>
                    </weee>
                </observers>
            </catalog_prepare_price_select>
            <catalog_entity_attribute_save_before>
                <observers>
                    <weee>
                        <type>model</type>
                        <class>weee/observer</class>
                        <method>assignBackendModelToAttribute</method>
                    </weee>
                </observers>
            </catalog_entity_attribute_save_before>
            <catalogrule_after_apply>
                <observers>
                    <weee>
                        <class>weee/observer</class>
                        <method>updateDiscountPercents</method>
                    </weee>
                </observers>
            </catalogrule_after_apply>
            <catalog_product_view_config>
                <observers>
                    <weee>
                        <class>weee/observer</class>
                        <method>updateCofigurableProductOptions</method>
                    </weee>
                </observers>
            </catalog_product_view_config>
            <bundle_product_view_config>
                <observers>
                    <weee>
                        <class>weee/observer</class>
                        <method>updateBundleProductOptions</method>
                    </weee>
                </observers>
            </bundle_product_view_config>
            <create_order_session_quote_initialized>
                <observers>
                    <weee>
                        <class>weee/observer</class>
                        <method>setSessionQuoteStore</method>
                    </weee>
                </observers>
            </create_order_session_quote_initialized>
            <init_from_order_session_quote_initialized>
                <observers>
                    <weee>
                        <class>weee/observer</class>
                        <method>setSessionQuoteStore</method>
                    </weee>
                </observers>
            </init_from_order_session_quote_initialized>
        </events>
        <fieldsets>
            <sales_convert_quote_item>
                <weee_tax_applied>
                    <to_order_item>*</to_order_item>
                </weee_tax_applied>
                <weee_tax_applied_amount>
                    <to_order_item>*</to_order_item>
                </weee_tax_applied_amount>
                <weee_tax_applied_row_amount>
                    <to_order_item>*</to_order_item>
                </weee_tax_applied_row_amount>
                <base_weee_tax_applied_amount>
                    <to_order_item>*</to_order_item>
                </base_weee_tax_applied_amount>
                <base_weee_tax_applied_row_amount>
                    <to_order_item>*</to_order_item>
                </base_weee_tax_applied_row_amount>
                <weee_tax_disposition>
                    <to_order_item>*</to_order_item>
                </weee_tax_disposition>
                <base_weee_tax_disposition>
                    <to_order_item>*</to_order_item>
                </base_weee_tax_disposition>
                <weee_tax_row_disposition>
                    <to_order_item>*</to_order_item>
                </weee_tax_row_disposition>
                <base_weee_tax_row_disposition>
                    <to_order_item>*</to_order_item>
                </base_weee_tax_row_disposition>
            </sales_convert_quote_item>
            <sales_convert_order_item>
                <weee_tax_applied>
                    <to_cm_item>*</to_cm_item>
                    <to_invoice_item>*</to_invoice_item>
                </weee_tax_applied>
                <weee_tax_applied_amount>
                    <to_cm_item>*</to_cm_item>
                    <to_invoice_item>*</to_invoice_item>
                </weee_tax_applied_amount>
                <weee_tax_applied_row_amount>
                    <to_cm_item>*</to_cm_item>
                    <to_invoice_item>*</to_invoice_item>
                </weee_tax_applied_row_amount>
                <base_weee_tax_applied_amount>
                    <to_cm_item>*</to_cm_item>
                    <to_invoice_item>*</to_invoice_item>
                </base_weee_tax_applied_amount>
                <base_weee_tax_applied_row_amount>
                    <to_cm_item>*</to_cm_item>
                    <to_invoice_item>*</to_invoice_item>
                </base_weee_tax_applied_row_amount>
                <weee_tax_disposition>
                    <to_cm_item>*</to_cm_item>
                    <to_invoice_item>*</to_invoice_item>
                </weee_tax_disposition>
                <base_weee_tax_disposition>
                    <to_cm_item>*</to_cm_item>
                    <to_invoice_item>*</to_invoice_item>
                </base_weee_tax_disposition>
                <weee_tax_row_disposition>
                    <to_cm_item>*</to_cm_item>
                    <to_invoice_item>*</to_invoice_item>
                </weee_tax_row_disposition>
                <base_weee_tax_row_disposition>
                    <to_cm_item>*</to_cm_item>
                    <to_invoice_item>*</to_invoice_item>
                </base_weee_tax_row_disposition>
            </sales_convert_order_item>
        </fieldsets>
        <sales>
            <quote>
                <totals>
                    <weee>
                        <class>weee/total_quote_weee</class>
                        <after>subtotal,tax_subtotal</after>
                        <before>shipping,tax,discount</before>
                    </weee>
                </totals>
                <nominal_totals>
                    <nominal_weee>
                        <class>weee/total_quote_nominal_weee</class>
                        <sort_order>600</sort_order>
                    </nominal_weee>
                </nominal_totals>
            </quote>
            <order_invoice>
                <totals>
                    <weee>
                        <class>weee/total_invoice_weee</class>
                        <after>subtotal,tax,discount,grand_total,cost_total,shipping</after>
                    </weee>
                </totals>
            </order_invoice>
            <order_creditmemo>
                <totals>
                    <weee>
                        <class>weee/total_creditmemo_weee</class>
                        <after>subtotal</after>
                        <before>tax,discount,grand_total</before>
                    </weee>
                </totals>
            </order_creditmemo>
            <old_fields_map>
                <order_item>
                    <base_weee_tax_applied_row_amount>base_weee_tax_applied_row_amnt</base_weee_tax_applied_row_amount>
                </order_item>
                <invoice_item>
                    <base_weee_tax_applied_row_amount>base_weee_tax_applied_row_amnt</base_weee_tax_applied_row_amount>
                </invoice_item>
                <creditmemo_item>
                    <base_weee_tax_applied_row_amount>base_weee_tax_applied_row_amnt</base_weee_tax_applied_row_amount>
                </creditmemo_item>
            </old_fields_map>
        </sales>
    </global>
    <adminhtml>
        <events>
            <adminhtml_catalog_product_edit_prepare_form>
                <observers>
                    <weee>
                        <class>weee/observer</class>
                        <method>setWeeeRendererInForm</method>
                    </weee>
                </observers>
            </adminhtml_catalog_product_edit_prepare_form>
            <adminhtml_catalog_product_form_prepare_excluded_field_list>
                <observers>
                    <weee>
                        <class>weee/observer</class>
                        <method>updateExcludedFieldList</method>
                    </weee>
                </observers>
            </adminhtml_catalog_product_form_prepare_excluded_field_list>
            <adminhtml_product_attribute_types>
                <observers>
                    <weee>
                        <type>model</type>
                        <class>weee/observer</class>
                        <method>addWeeeTaxAttributeType</method>
                    </weee>
                </observers>
            </adminhtml_product_attribute_types>
            <adminhtml_catalog_product_edit_element_types>
                <observers>
                    <weee>
                        <class>weee/observer</class>
                        <method>updateElementTypes</method>
                    </weee>
                </observers>
            </adminhtml_catalog_product_edit_element_types>
        </events>
    </adminhtml>
    <default>
        <sales>
            <totals_sort>
                <weee>50</weee>
            </totals_sort>
        </sales>
        <tax>
            <weee>
                <enable>0</enable>
                <display>0</display>
                <display_list>0</display_list>
                <display_sales>0</display_sales>
                <display_email>0</display_email>
                <discount>0</discount>
                <apply_vat>0</apply_vat>
                <include_in_subtotal>0</include_in_subtotal>
            </weee>
        </tax>
        <general>
            <validator_data>
                <input_types>
                    <weee>weee</weee>
                </input_types>
            </validator_data>
        </general>
    </default>
    <frontend>
        <layout>
            <updates>
                <weee>
                    <file>weee.xml</file>
                </weee>
            </updates>
        </layout>
    </frontend>
</config>
