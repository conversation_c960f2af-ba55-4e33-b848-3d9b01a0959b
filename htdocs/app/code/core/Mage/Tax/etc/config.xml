<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Tax
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_Tax>
            <version>1.6.0.4</version>
        </Mage_Tax>
    </modules>
    <global>
        <models>
            <tax>
                <class>Mage_Tax_Model</class>
                <resourceModel>tax_resource</resourceModel>
            </tax>
            <tax_resource>
                <class>Mage_Tax_Model_Resource</class>
                <deprecatedNode>tax_mysql4</deprecatedNode>
                <entities>
                    <tax_class>
                        <table>tax_class</table>
                    </tax_class>
                    <tax_calculation>
                        <table>tax_calculation</table>
                    </tax_calculation>
                    <tax_calculation_rate>
                        <table>tax_calculation_rate</table>
                    </tax_calculation_rate>
                    <tax_calculation_rate_title>
                        <table>tax_calculation_rate_title</table>
                    </tax_calculation_rate_title>
                    <tax_calculation_rule>
                        <table>tax_calculation_rule</table>
                    </tax_calculation_rule>
                    <tax_order_aggregated_created>
                        <table>tax_order_aggregated_created</table>
                    </tax_order_aggregated_created>
                    <tax_order_aggregated_updated>
                        <table>tax_order_aggregated_updated</table>
                    </tax_order_aggregated_updated>
                    <sales_order_tax>
                        <table>sales_order_tax</table>
                    </sales_order_tax>
                    <sales_order_tax_item>
                        <table>sales_order_tax_item</table>
                    </sales_order_tax_item>
                </entities>
            </tax_resource>
        </models>
        <resources>
            <tax_setup>
                <setup>
                    <module>Mage_Tax</module>
                    <class>Mage_Tax_Model_Resource_Setup</class>
                </setup>
            </tax_setup>
        </resources>
        <fieldsets>
            <sales_convert_quote_address>
                <subtotal_incl_tax>
                    <to_order>*</to_order>
                </subtotal_incl_tax>
                <base_subtotal_incl_tax>
                    <to_order>*</to_order>
                </base_subtotal_incl_tax>
            </sales_convert_quote_address>
            <sales_convert_quote_item>
                <price_incl_tax>
                    <to_order_item>*</to_order_item>
                </price_incl_tax>
                <base_price_incl_tax>
                    <to_order_item>*</to_order_item>
                </base_price_incl_tax>
                <row_total_incl_tax>
                    <to_order_item>*</to_order_item>
                </row_total_incl_tax>
                <base_row_total_incl_tax>
                    <to_order_item>*</to_order_item>
                </base_row_total_incl_tax>
            </sales_convert_quote_item>
            <sales_convert_order_item>
                <price_incl_tax>
                    <to_invoice_item>*</to_invoice_item>
                    <to_shipment_item>*</to_shipment_item>
                    <to_cm_item>*</to_cm_item>
                </price_incl_tax>
                <base_price_incl_tax>
                    <to_invoice_item>*</to_invoice_item>
                    <to_shipment_item>*</to_shipment_item>
                    <to_cm_item>*</to_cm_item>
                </base_price_incl_tax>
            </sales_convert_order_item>
        </fieldsets>
        <events>
            <sales_convert_quote_address_to_order>
                <observers>
                    <tax>
                        <class>tax/observer</class>
                        <method>salesEventConvertQuoteAddressToOrder</method>
                    </tax>
                </observers>
            </sales_convert_quote_address_to_order>
            <sales_order_save_after>
                <observers>
                    <tax>
                        <class>tax/observer</class>
                        <method>salesEventOrderAfterSave</method>
                    </tax>
                </observers>
            </sales_order_save_after>
            <catalog_prepare_price_select>
                <observers>
                    <tax>
                        <class>tax/observer</class>
                        <method>prepareCatalogIndexPriceSelect</method>
                    </tax>
                </observers>
            </catalog_prepare_price_select>
            <catalog_product_collection_load_after>
                <observers>
                    <tax>
                        <class>tax/observer</class>
                        <method>addTaxPercentToProductCollection</method>
                    </tax>
                </observers>
            </catalog_product_collection_load_after>
            <sales_quote_collect_totals_before>
                <observers>
                    <tax>
                        <class>tax/observer</class>
                        <method>quoteCollectTotalsBefore</method>
                    </tax>
                </observers>
            </sales_quote_collect_totals_before>
        </events>
        <sales>
            <quote>
                <totals>
                    <tax_subtotal>
                        <class>tax/sales_total_quote_subtotal</class>
                        <after>freeshipping</after>
                        <before>tax,discount</before>
                    </tax_subtotal>
                    <tax_shipping>
                        <class>tax/sales_total_quote_shipping</class>
                        <after>shipping,tax_subtotal</after>
                        <before>tax,discount</before>
                    </tax_shipping>
                    <tax>
                        <class>tax/sales_total_quote_tax</class>
                        <after>subtotal,shipping</after>
                        <before>grand_total</before>
                        <renderer>tax/checkout_tax</renderer>
                        <admin_renderer>adminhtml/sales_order_create_totals_tax</admin_renderer>
                    </tax>
                    <subtotal>
                        <renderer>tax/checkout_subtotal</renderer>
                        <admin_renderer>adminhtml/sales_order_create_totals_subtotal</admin_renderer>
                    </subtotal>
                    <shipping>
                        <renderer>tax/checkout_shipping</renderer>
                        <admin_renderer>adminhtml/sales_order_create_totals_shipping</admin_renderer>
                    </shipping>
                    <discount>
                        <renderer>tax/checkout_discount</renderer>
                        <admin_renderer>adminhtml/sales_order_create_totals_discount</admin_renderer>
                    </discount>
                    <grand_total>
                        <renderer>tax/checkout_grandtotal</renderer>
                        <admin_renderer>adminhtml/sales_order_create_totals_grandtotal</admin_renderer>
                    </grand_total>
                </totals>
                <nominal_totals>
                    <nominal_tax_subtotal>
                        <class>tax/sales_total_quote_nominal_subtotal</class>
                        <sort_order>500</sort_order>
                    </nominal_tax_subtotal>
                    <nominal_tax>
                        <class>tax/sales_total_quote_nominal_tax</class>
                        <sort_order>750</sort_order>
                    </nominal_tax>
                </nominal_totals>
            </quote>
        </sales>
        <pdf>
            <totals>
                <subtotal>
                    <model>tax/sales_pdf_subtotal</model>
                </subtotal>
                <shipping>
                    <model>tax/sales_pdf_shipping</model>
                </shipping>
                <grand_total>
                    <model>tax/sales_pdf_grandtotal</model>
                </grand_total>
                <tax translate="title">
                    <title>Tax</title>
                    <source_field>tax_amount</source_field>
                    <model>tax/sales_pdf_tax</model>
                    <font_size>7</font_size>
                    <display_zero>0</display_zero>
                    <sort_order>300</sort_order>
                </tax>
            </totals>
        </pdf>
    </global>
    <adminhtml>
        <layout>
            <updates>
                <tax>
                    <file>tax.xml</file>
                </tax>
            </updates>
        </layout>
        <translate>
            <modules>
                <Mage_Tax>
                    <files>
                        <default>Mage_Tax.csv</default>
                    </files>
                </Mage_Tax>
            </modules>
        </translate>
    </adminhtml>
    <frontend>
        <translate>
            <modules>
                <Mage_Tax>
                    <files>
                        <default>Mage_Tax.csv</default>
                    </files>
                </Mage_Tax>
            </modules>
        </translate>
    </frontend>
    <default>
        <tax>
            <classes>
                <shipping_tax_class/>
            </classes>
            <calculation>
                <algorithm>TOTAL_BASE_CALCULATION</algorithm>
                <apply_after_discount>1</apply_after_discount>
                <discount_tax>0</discount_tax>
                <based_on>shipping</based_on>
                <price_includes_tax>0</price_includes_tax>
                <shipping_includes_tax>0</shipping_includes_tax>
                <apply_tax_on>0</apply_tax_on>
            </calculation>
            <defaults>
                <country>US</country>
                <region>0</region>
                <postcode>*</postcode>
            </defaults>
            <display>
                <type>1</type>
                <shipping>1</shipping>
            </display>
            <cart_display>
                <price>1</price>
                <subtotal>1</subtotal>
                <shipping>1</shipping>
                <discount>1</discount>
                <grandtotal>0</grandtotal>
                <full_summary>0</full_summary>
                <zero_tax>0</zero_tax>
            </cart_display>
            <sales_display>
                <price>1</price>
                <subtotal>1</subtotal>
                <shipping>1</shipping>
                <discount>1</discount>
                <grandtotal>0</grandtotal>
                <full_summary>0</full_summary>
                <zero_tax>0</zero_tax>
            </sales_display>
            <notification>
                <url>http://www.magentocommerce.com/knowledge-base/entry/magento-ce-18-ee-113-tax-calc</url>
            </notification>
        </tax>
    </default>
    <crontab>
        <jobs>
            <aggregate_sales_report_tax_data>
                <schedule>
                    <cron_expr>0 0 * * *</cron_expr>
                </schedule>
                <run>
                    <model>tax/observer::aggregateSalesReportTaxData</model>
                </run>
            </aggregate_sales_report_tax_data>
        </jobs>
    </crontab>
</config>
