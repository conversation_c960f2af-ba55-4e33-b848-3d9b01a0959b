<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Api2
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_Api2>
            <version>*******</version>
        </Mage_Api2>
    </modules>
    <global>
        <cache>
            <types>
                <config_api2 translate="label,description" module="api2">
                    <label>Web Services Configuration</label>
                    <description>Web Services definition files (api2.xml).</description>
                    <tags>CONFIG_API2</tags>
                </config_api2>
            </types>
        </cache>
        <models>
            <api2>
                <class>Mage_Api2_Model</class>
                <resourceModel>api2_resource</resourceModel>
            </api2>
            <api2_resource>
                <class>Mage_Api2_Model_Resource</class>
                <entities>
                    <acl_role>
                        <table>api2_acl_role</table>
                    </acl_role>
                    <acl_user>
                        <table>api2_acl_user</table>
                    </acl_user>
                    <acl_rule>
                        <table>api2_acl_rule</table>
                    </acl_rule>
                    <acl_attribute>
                        <table>api2_acl_attribute</table>
                    </acl_attribute>
                </entities>
            </api2_resource>
        </models>
        <blocks>
            <api2>
                <class>Mage_Api2_Block</class>
            </api2>
        </blocks>
        <helpers>
            <api2>
                <class>Mage_Api2_Helper</class>
            </api2>
        </helpers>
        <resources>
            <api2_setup>
                <setup>
                    <module>Mage_Api2</module>
                    <class>Mage_Api2_Model_Resource_Setup</class>
                </setup>
            </api2_setup>
        </resources>
        <events>
            <admin_user_save_after>
                <observers>
                    <api2>
                        <class>api2/observer</class>
                        <method>saveAdminToRoleRelation</method>
                    </api2>
                </observers>
            </admin_user_save_after>
            <api_user_authenticated>
                <observers>
                    <api2_upgrade_key>
                        <class>Mage_Api2_Model_Observer</class>
                        <method>upgradeApiKey</method>
                    </api2_upgrade_key>
                </observers>
            </api_user_authenticated>
        </events>
        <api2>
            <auth_adapters>
                <oauth module="api2" translate="label">
                    <model>api2/auth_adapter_oauth</model>
                    <label>OAuth</label>
                    <enabled>1</enabled>
                    <order>10</order>
                </oauth>
            </auth_adapters>
            <user_types>
                <admin>
                    <model>api2/auth_user_admin</model>
                    <allowed>1</allowed>
                </admin>
                <customer>
                    <model>api2/auth_user_customer</model>
                    <allowed>1</allowed>
                </customer>
                <guest>
                    <model>api2/auth_user_guest</model>
                    <allowed>1</allowed>
                </guest>
            </user_types>
            <request>
                <interpreters>
                    <!-- JSON -->
                    <application_json>
                        <type>application/json</type>
                        <model>api2/request_interpreter_json</model>
                    </application_json>

                    <!-- QUERY -->
                    <text_plain>
                        <type>text/plain</type>
                        <model>api2/request_interpreter_query</model>
                    </text_plain>

                    <!-- XML -->
                    <application_xml>
                        <type>application/xml</type>
                        <model>api2/request_interpreter_xml</model>
                    </application_xml>
                    <application_xhtml_xml>
                        <type>application/xhtml+xml</type>
                        <model>api2/request_interpreter_xml</model>
                    </application_xhtml_xml>
                    <text_xml>
                        <type>text/xml</type>
                        <model>api2/request_interpreter_xml</model>
                    </text_xml>
                </interpreters>
            </request>
            <response>
                <renders>
                    <!-- JSON -->
                    <default>
                        <type>*/*</type>
                        <model>api2/renderer_json</model>
                    </default>
                    <application_json>
                        <type>application/json</type>
                        <model>api2/renderer_json</model>
                    </application_json>

                    <!-- QUERY -->
                    <text_plain>
                        <type>text/plain</type>
                        <model>api2/renderer_query</model>
                    </text_plain>

                    <!-- XML -->
                    <text_xml>
                        <type>text/xml</type>
                        <model>api2/renderer_xml</model>
                    </text_xml>
                    <application_xml>
                        <type>application/xml</type>
                        <model>api2/renderer_xml</model>
                    </application_xml>
                    <application_xhtml_xml>
                        <type>application/xhtml+xml</type>
                        <model>api2/renderer_xml</model>
                    </application_xhtml_xml>
                </renders>
            </response>
        </api2>
    </global>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <api2 before="Mage_Adminhtml">Mage_Api2_Adminhtml</api2>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <layout>
            <updates>
                <api2>
                    <file>api2.xml</file>
                </api2>
            </updates>
        </layout>
        <events>
            <catalog_entity_attribute_save_after>
                <observers>
                    <api2>
                        <class>api2/observer</class>
                        <method>catalogAttributeSaveAfter</method>
                    </api2>
                </observers>
            </catalog_entity_attribute_save_after>
        </events>
    </adminhtml>
</config>
