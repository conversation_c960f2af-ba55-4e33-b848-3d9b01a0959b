<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:ns="http://fedex.com/ws/ship/v10"
             xmlns:s1="http://schemas.xmlsoap.org/wsdl/soap/"
             targetNamespace="http://fedex.com/ws/ship/v10" name="ShipServiceDefinitions">
  <types>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://fedex.com/ws/ship/v10">
      <xs:element name="CancelPendingShipmentReply" type="ns:CancelPendingShipmentReply"/>
      <xs:element name="CancelPendingShipmentRequest" type="ns:CancelPendingShipmentRequest"/>
      <xs:element name="CreatePendingShipmentReply" type="ns:CreatePendingShipmentReply"/>
      <xs:element name="CreatePendingShipmentRequest" type="ns:CreatePendingShipmentRequest"/>
      <xs:element name="DeleteShipmentRequest" type="ns:DeleteShipmentRequest"/>
      <xs:element name="DeleteTagRequest" type="ns:DeleteTagRequest"/>
      <xs:element name="ProcessShipmentReply" type="ns:ProcessShipmentReply"/>
      <xs:element name="ProcessShipmentRequest" type="ns:ProcessShipmentRequest"/>
      <xs:element name="ProcessTagReply" type="ns:ProcessTagReply"/>
      <xs:element name="ProcessTagRequest" type="ns:ProcessTagRequest"/>
      <xs:element name="ShipmentReply" type="ns:ShipmentReply"/>
      <xs:element name="ValidateShipmentRequest" type="ns:ValidateShipmentRequest"/>
      <xs:complexType name="AdditionalLabelsDetail">
        <xs:annotation>
          <xs:documentation>Specifies additional labels to be produced. All required labels for shipments will be produced without the need to request additional labels. These are only available as thermal labels.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:AdditionalLabelsType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The type of additional labels to return.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Count" type="xs:nonNegativeInteger" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The number of this type label to return</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="AdditionalLabelsType">
        <xs:annotation>
          <xs:documentation>Identifies the type of additional labels.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER"/>
          <xs:enumeration value="CONSIGNEE"/>
          <xs:enumeration value="CUSTOMS"/>
          <xs:enumeration value="DESTINATION"/>
          <xs:enumeration value="FREIGHT_REFERENCE"/>
          <xs:enumeration value="MANIFEST"/>
          <xs:enumeration value="ORIGIN"/>
          <xs:enumeration value="RECIPIENT"/>
          <xs:enumeration value="SHIPPER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Address">
        <xs:annotation>
          <xs:documentation>Descriptive data for a physical location. May be used as an actual physical address (place to which one could go), or as a container of "address parts" which should be handled as a unit (such as a city-state-ZIP combination within the US).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="StreetLines" type="xs:string" minOccurs="0" maxOccurs="2">
            <xs:annotation>
              <xs:documentation>Combination of number, street name, etc. At least one line is required for a valid physical address; empty lines should not be included.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="City" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Name of city, town, etc.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StateOrProvinceCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifying abbreviation for US state, Canada province, etc. Format and presence of this field will vary, depending on country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PostalCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identification of a region (usually small) for mail/package delivery. Format and presence of this field will vary, depending on country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UrbanizationCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Relevant only to addresses in Puerto Rico.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CountryCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The two-letter code used to identify a country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Residential" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether this address residential (as opposed to commercial).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="AstraLabelElement">
        <xs:sequence>
          <xs:element name="Number" type="xs:int" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Position of Astra element</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Content" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Content corresponding to the Astra Element</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="B13AFilingOptionType">
        <xs:annotation>
          <xs:documentation>
            Specifies which filing option is being exercised by the customer.
            Required for non-document shipments originating in Canada destined for any country other than Canada, the United States, Puerto Rico or the U.S. Virgin Islands.
          </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FILED_ELECTRONICALLY"/>
          <xs:enumeration value="MANUALLY_ATTACHED"/>
          <xs:enumeration value="NOT_REQUIRED"/>
          <xs:enumeration value="SUMMARY_REPORTING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="BarcodeSymbologyType">
        <xs:annotation>
          <xs:documentation>Identification of the type of barcode (symbology) used on FedEx documents and labels.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CODE128B"/>
          <xs:enumeration value="CODE128C"/>
          <xs:enumeration value="CODE39"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="BinaryBarcode">
        <xs:annotation>
          <xs:documentation>Each instance of this data type represents a barcode whose content must be represented as binary data (i.e. not ASCII text).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:BinaryBarcodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The kind of barcode data in this instance.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:base64Binary" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The data content of this instance.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="BinaryBarcodeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMMON_2D"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CancelPendingShipmentReply">
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="1"/>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="1" maxOccurs="unbounded"/>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0"/>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CancelPendingShipmentRequest">
        <xs:annotation>
          <xs:documentation>Descriptive data sent to FedEx by a customer in order to Cancel a Pending shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackingId" type="ns:TrackingId" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CarrierCodeType">
        <xs:annotation>
          <xs:documentation>Identification of a FedEx operating company (transportation).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FDXC"/>
          <xs:enumeration value="FDXE"/>
          <xs:enumeration value="FDXG"/>
          <xs:enumeration value="FXCC"/>
          <xs:enumeration value="FXFR"/>
          <xs:enumeration value="FXSP"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CertificateOfOriginDetail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the Certificate of Origin ( e.g. whether or not to include the instructions, image type, etc ...)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="DocumentFormat" type="ns:ShippingDocumentFormat" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerImageUsages" type="ns:CustomerImageUsage" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of customer supplied images to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ClearanceBrokerageType">
        <xs:annotation>
          <xs:documentation>Specifies the type of brokerage to be applied to a shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER_INCLUSIVE"/>
          <xs:enumeration value="BROKER_INCLUSIVE_NON_RESIDENT_IMPORTER"/>
          <xs:enumeration value="BROKER_SELECT"/>
          <xs:enumeration value="BROKER_SELECT_NON_RESIDENT_IMPORTER"/>
          <xs:enumeration value="BROKER_UNASSIGNED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ClientDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data for the client submitting a transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="AccountNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The FedEx account number associated with this transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MeterNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>This number is assigned by FedEx and identifies the unique device from which the request is originating</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="IntegratorId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used in transactions which require identification of the Fed Ex Office integrator.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The language to be used for human-readable Notification.localizedMessages in responses to the request containing this ClientDetail object. Different requests from the same client may contain different Localization data. (Contrast with TransactionDetail.localization, which governs data payload language/translation.)</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CodAddTransportationChargesType">
        <xs:annotation>
          <xs:documentation>Identifies what freight charges should be added to the COD collect amount.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADD_ACCOUNT_COD_SURCHARGE"/>
          <xs:enumeration value="ADD_ACCOUNT_NET_CHARGE"/>
          <xs:enumeration value="ADD_ACCOUNT_NET_FREIGHT"/>
          <xs:enumeration value="ADD_ACCOUNT_TOTAL_CUSTOMER_CHARGE"/>
          <xs:enumeration value="ADD_LIST_COD_SURCHARGE"/>
          <xs:enumeration value="ADD_LIST_NET_CHARGE"/>
          <xs:enumeration value="ADD_LIST_NET_FREIGHT"/>
          <xs:enumeration value="ADD_LIST_TOTAL_CUSTOMER_CHARGE"/>
          <xs:enumeration value="ADD_SUM_OF_ACCOUNT_NET_CHARGES"/>
          <xs:enumeration value="ADD_SUM_OF_ACCOUNT_NET_FREIGHT"/>
          <xs:enumeration value="ADD_SUM_OF_LIST_NET_CHARGES"/>
          <xs:enumeration value="ADD_SUM_OF_LIST_NET_FREIGHT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="CodCollectionType">
        <xs:annotation>
          <xs:documentation>Identifies the type of funds FedEx should collect upon shipment delivery.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ANY"/>
          <xs:enumeration value="CASH"/>
          <xs:enumeration value="COMPANY_CHECK"/>
          <xs:enumeration value="GUARANTEED_FUNDS"/>
          <xs:enumeration value="PERSONAL_CHECK"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CodDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data required for a FedEx COD (Collect-On-Delivery) shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="CodCollectionAmount" type="ns:Money" minOccurs="0"/>
          <xs:element name="AddTransportationCharges" type="ns:CodAddTransportationChargesType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies if freight charges are to be added to the COD amount. This element determines which freight charges should be added to the COD collect amount. See CodAddTransportationChargesType for a list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CollectionType" type="ns:CodCollectionType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the type of funds FedEx should collect upon package delivery</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodRecipient" type="ns:Party" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For Express this is the descriptive data that is used for the recipient of the FedEx Letter containing the COD payment. For Ground this is the descriptive data for the party to receive the payment that prints the COD receipt.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ReferenceIndicator" type="ns:CodReturnReferenceIndicatorType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates which type of reference information to include on the COD return shipping label.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CodReturnPackageDetail">
        <xs:sequence>
          <xs:element name="CollectionAmount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The COD amount (after any accumulations) that must be collected upon delivery of a package shipped using the COD special service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Electronic" type="xs:boolean" minOccurs="0"/>
          <xs:element name="Barcodes" type="ns:PackageBarcodes" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains the data which form the Astra and 2DCommon barcodes that print on the COD return label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Label" type="ns:ShippingDocument" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The label image or printer commands to print the label.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CodReturnReferenceIndicatorType">
        <xs:annotation>
          <xs:documentation>Indicates which type of reference information to include on the COD return shipping label.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="INVOICE"/>
          <xs:enumeration value="PO"/>
          <xs:enumeration value="REFERENCE"/>
          <xs:enumeration value="TRACKING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CodReturnShipmentDetail">
        <xs:sequence>
          <xs:element name="CollectionAmount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The COD amount (after any accumulations) that must be collected upon delivery of a package shipped using the COD special service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Handling" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Currently not supported.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>TBD</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="ServiceTypeDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The description of the FedEx service type used for the COD return shipment. Currently not supported.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>70</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackagingDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The description of the packaging used for the COD return shipment.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>40</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="SecuredDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Currently not supported.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>TBD</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Remitter" type="ns:Party" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Currently not supported.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodRecipient" type="ns:Party" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Currently not supported.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodRoutingDetail" type="ns:RoutingDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                The CodRoutingDetail element will contain the COD return tracking number and form id. In the case of a COD multiple piece shipment these will need to be inserted in the request for the last piece of the multiple piece shipment.
                The service commitment is the only other element of the RoutingDetail that is used for a CodRoutingDetail.
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Barcodes" type="ns:PackageBarcodes" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains the data which form the Astra and 2DCommon barcodes that print on the COD return label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Label" type="ns:ShippingDocument" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The label image or printer commands to print the label.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CommercialInvoice">
        <xs:annotation>
          <xs:documentation>CommercialInvoice element is required for electronic upload of CI data. It will serve to create/transmit an Electronic Commercial Invoice through the FedEx Systems. Customers are responsible for printing their own Commercial Invoice.If you would likeFedEx to generate a Commercial Invoice and transmit it to Customs. for clearance purposes, you need to specify that in the ShippingDocumentSpecification element. If you would like a copy of the Commercial Invoice that FedEx generated returned to you in reply it needs to be specified in the ETDDetail/RequestedDocumentCopies element. Commercial Invoice support consists of maximum of 99 commodity line items.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Comments" type="xs:string" minOccurs="0" maxOccurs="99">
            <xs:annotation>
              <xs:documentation>Any comments that need to be communicated about this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Any freight charges that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TaxesOrMiscellaneousCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Any taxes or miscellaneous charges(other than Freight charges or Insurance charges) that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackingCosts" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Any packing costs that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HandlingCosts" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Any handling costs that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialInstructions" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free-form text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeclarationStatment" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free-form text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PaymentTerms" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free-form text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Purpose" type="ns:PurposeOfShipmentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The reason for the shipment. Note: SOLD is not a valid purpose for a Proforma Invoice.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerInvoiceNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer assigned Invoice number</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OriginatorName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Name of the International Expert that completed the Commercial Invoice different from Sender.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TermsOfSale" type="ns:TermsOfSaleType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Required for dutiable international Express or Ground shipment. This field is not applicable to an international PIB(document) or a non-document which does not require a Commercial Invoice</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CommercialInvoiceDetail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the Commercial Invoice( e.g. image type) Specifies characteristics of a shipping document to be produced.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0"/>
          <xs:element name="CustomerImageUsages" type="ns:CustomerImageUsage" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of a customer supplied image to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Commodity">
        <xs:annotation>
          <xs:documentation>
            For international multiple piece shipments, commodity information must be passed in the Master and on each child transaction.
            If this shipment cotains more than four commodities line items, the four highest valued should be included in the first 4 occurances for this request.
          </xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Name" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Name of this commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NumberOfPieces" type="xs:nonNegativeInteger" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Total number of pieces of this commodity</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Complete and accurate description of this commodity.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>450</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="CountryOfManufacture" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Country code where commodity contents were produced or manufactured in their final form.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>2</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="HarmonizedCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Unique alpha/numeric representing commodity item.
                At least one occurrence is required for US Export shipments if the Customs Value is greater than $2500 or if a valid US Export license is required.
              </xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>14</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Weight" type="ns:Weight" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Total weight of this commodity. 1 explicit decimal position. Max length 11 including decimal.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Quantity" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Number of units of a commodity in total number of pieces for this line item. Max length is 9</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="QuantityUnits" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Unit of measure used to express the quantity of this commodity line item.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>3</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="AdditionalMeasures" type="ns:Measure" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Contains only additional quantitative information other than weight and quantity to calculate duties and taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UnitPrice" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Value of each unit in Quantity. Six explicit decimal positions, Max length 18 including decimal.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomsValue" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Total customs value for this line item.
                It should equal the commodity unit quantity times commodity unit value.
                Six explicit decimal positions, max length 18 including decimal.
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExciseConditions" type="ns:EdtExciseCondition" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Defines additional characteristic of commodity used to calculate duties and taxes</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExportLicenseNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Applicable to US export shipping only.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>12</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExportLicenseExpirationDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Date of expiration. Must be at least 1 day into future.
                The date that the Commerce Export License expires. Export License commodities may not be exported from the U.S. on an expired license.
                Applicable to US Export shipping only.
                Required only if commodity is shipped on commerce export license, and Export License Number is supplied.
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CIMarksAndNumbers" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                An identifying mark or number used on the packaging of a shipment to help customers identify a particular shipment.
              </xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>15</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="NaftaDetail" type="ns:NaftaCommodityDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>All data required for this commodity in NAFTA Certificate of Origin.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CompletedEtdDetail">
        <xs:sequence>
          <xs:element name="FolderId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The identifier for all clearance documents associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UploadDocumentReferenceDetails" type="ns:UploadDocumentReferenceDetail" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CompletedHoldAtLocationDetail">
        <xs:sequence>
          <xs:element name="HoldingLocation" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the branded location name, the hold at location phone number and the address of the location.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HoldingLocationType" type="ns:FedExLocationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of FedEx location.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CompletedPackageDetail">
        <xs:sequence>
          <xs:element name="SequenceNumber" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The package sequence number of this package in a multiple piece shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackingIds" type="ns:TrackingId" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The Tracking number and form id for this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GroupNumber" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used with request containing PACKAGE_GROUPS, to identify which group of identical packages was used to produce a reply item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OversizeClass" type="ns:OversizeClassType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Oversize class for this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageRating" type="ns:PackageRating" minOccurs="0">
            <xs:annotation>
              <xs:documentation>All package-level rating data for this package, which may include data for multiple rate types.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GroundServiceCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Associated with package, due to interaction with per-package hazardous materials presence/absence.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Barcodes" type="ns:PackageBarcodes" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The data that is used to from the Astra and 2DCommon barcodes for the label..</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AstraHandlingText" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The textual description of the special service applied to the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AstraLabelElements" type="ns:AstraLabelElement" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="Label" type="ns:ShippingDocument" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The label image or printer commands to print the label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageDocuments" type="ns:ShippingDocument" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All package-level shipping documents (other than labels and barcodes). For use in loads after January, 2008.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodReturnDetail" type="ns:CodReturnPackageDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Information about the COD return shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureOption" type="ns:SignatureOptionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Actual signature option applied, to allow for cases in which the original value conflicted with other service features in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HazardousCommodities" type="ns:ValidatedHazardousCommodityContent" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Documents the kinds and quantities of all hazardous commodities in the current package, using updated hazardous commodity description data.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CompletedShipmentDetail">
        <xs:sequence>
          <xs:element name="UsDomestic" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether or not this is a US Domestic shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CarrierCode" type="ns:CarrierCodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the carrier that will be used to deliver this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MasterTrackingId" type="ns:TrackingId" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The master tracking number and form id of this multiple piece shipment. This information is to be provided for each subsequent of a multiple piece shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ServiceTypeDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Description of the FedEx service used for this shipment. Currently not supported.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>70</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackagingDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Description of the packaging used for this shipment. Currently not supported.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>40</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="RoutingDetail" type="ns:ShipmentRoutingDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Information about the routing, origin, destination and delivery of a shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AccessDetail" type="ns:PendingShipmentAccessDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used with pending shipments.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TagDetail" type="ns:CompletedTagDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used in the reply to tag requests.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SmartPostDetail" type="ns:CompletedSmartPostDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Provides reply information specific to SmartPost shipments.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentRating" type="ns:ShipmentRating" minOccurs="0">
            <xs:annotation>
              <xs:documentation>All shipment-level rating data for this shipment, which may include data for multiple rate types.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodReturnDetail" type="ns:CodReturnShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Information about the COD return shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompletedHoldAtLocationDetail" type="ns:CompletedHoldAtLocationDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Returns the default holding location information when HOLD_AT_LOCATION special service is requested and the client does not specify the hold location address.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="IneligibleForMoneyBackGuarantee" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether or not this shipment is eligible for a money back guarantee.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExportComplianceStatement" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Returns any defaults or updates applied to RequestedShipment.exportDetail.exportComplianceStatement.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompletedEtdDetail" type="ns:CompletedEtdDetail" minOccurs="0"/>
          <xs:element name="ShipmentDocuments" type="ns:ShippingDocument" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All shipment-level shipping documents (other than labels and barcodes).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompletedPackageDetails" type="ns:CompletedPackageDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Package level details about this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CompletedSmartPostDetail">
        <xs:annotation>
          <xs:documentation>Provides reply information specific to SmartPost shipments.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PickUpCarrier" type="ns:CarrierCodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the carrier that will pick up the SmartPost shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Machinable" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether the shipment is deemed to be machineable, based on dimensions, weight, and packaging.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CompletedTagDetail">
        <xs:annotation>
          <xs:documentation>Provides reply information specific to a tag request.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ConfirmationNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AccessTime" type="xs:duration" minOccurs="0">
            <xs:annotation>
              <xs:documentation>As of June 2007, returned only for FedEx Express services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CutoffTime" type="xs:time" minOccurs="0">
            <xs:annotation>
              <xs:documentation>As of June 2007, returned only for FedEx Express services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Location" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>As of June 2007, returned only for FedEx Express services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryCommitment" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>As of June 2007, returned only for FedEx Express services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DispatchDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>FEDEX INTERNAL USE ONLY: for use by INET.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ConfigurableLabelReferenceEntry">
        <xs:annotation>
          <xs:documentation>Defines additional data to print in the Configurable portion of the label, this allows you to print the same type information on the label that can also be printed on the doc tab.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ZoneNumber" type="xs:positiveInteger" minOccurs="1">
            <xs:annotation>
              <xs:documentation>1 of 12 possible zones to position data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Header" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The identifiying text for the data in this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DataField" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A reference to a field in either the request or reply to print in this zone following the header.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LiteralValue" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A literal value to print after the header in this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Contact">
        <xs:annotation>
          <xs:documentation>The descriptive data for a point-of-contact person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ContactId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Client provided identifier corresponding to this contact information.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PersonName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the contact person's name.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Title" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the contact person's title.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompanyName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the company this contact is associated with.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PhoneNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the phone number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PhoneExtension" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the phone extension associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PagerNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the pager number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FaxNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the fax number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EMailAddress" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the email address associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ContactAndAddress">
        <xs:sequence>
          <xs:element name="Contact" type="ns:Contact" minOccurs="1"/>
          <xs:element name="Address" type="ns:Address" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ContentRecord">
        <xs:annotation>
          <xs:documentation>Content Record.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PartNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Part Number.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ItemNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Item Number.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ReceivedQuantity" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Received Quantity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Description.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CreatePendingShipmentReply">
        <xs:annotation>
          <xs:documentation>Reply to the Close Request transaction. The Close Reply bring back the ASCII data buffer which will be used to print the Close Manifest. The Manifest is essential at the time of pickup.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the highest severity encountered when executing the request; in order from high to low: FAILURE, ERROR, WARNING, NOTE, SUCCESS.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="1" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The descriptive data detailing the status of a sumbitted transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data that governs data payload language/translations. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompletedShipmentDetail" type="ns:CompletedShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The reply payload. All of the returned information about this shipment/package.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CreatePendingShipmentRequest">
        <xs:annotation>
          <xs:documentation>Create Pending Shipment Request</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestedShipment" type="ns:RequestedShipment" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data about the shipment being sent by the requestor.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CurrencyExchangeRate">
        <xs:annotation>
          <xs:documentation>Currency exchange rate information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FromCurrency" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The currency code for the original (converted FROM) currency.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="IntoCurrency" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The currency code for the final (converted INTO) currency.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Rate" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Multiplier used to convert fromCurrency units to intoCurrency units.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomDeliveryWindowDetail">
        <xs:sequence>
          <xs:element name="Type" type="ns:CustomDeliveryWindowType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the type of custom delivery being requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestTime" type="xs:time" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Time by which delivery is requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestRange" type="ns:DateRange" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Range of dates for custom delivery request; only used if type is BETWEEN.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Date for custom delivery request; only used for types of ON, BETWEEN, or AFTER.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomDeliveryWindowType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="AFTER"/>
          <xs:enumeration value="BEFORE"/>
          <xs:enumeration value="BETWEEN"/>
          <xs:enumeration value="ON"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomDocumentDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce a custom-specified document, either at shipment or package level.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Common information controlling document production.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelPrintingOrientation" type="ns:LabelPrintingOrientationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Applicable only to documents produced on thermal printers with roll stock.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelRotation" type="ns:LabelRotationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Applicable only to documents produced on thermal printers with roll stock.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecificationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the formatting specification used to construct this custom document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomDocumentIdentifier" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the individual document specified by the client.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DocTabContent" type="ns:DocTabContent" minOccurs="0">
            <xs:annotation>
              <xs:documentation>If provided, thermal documents will include specified doc tab content. If omitted, document will be produced without doc tab content.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelBarcodeEntry">
        <xs:annotation>
          <xs:documentation>Constructed string, based on format and zero or more data fields, printed in specified barcode symbology.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Position" type="ns:CustomLabelPosition" minOccurs="1"/>
          <xs:element name="Format" type="xs:string" minOccurs="0"/>
          <xs:element name="DataFields" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="BarHeight" type="xs:int" minOccurs="0"/>
          <xs:element name="ThinBarWidth" type="xs:int" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Width of thinnest bar/space element in the barcode.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BarcodeSymbology" type="ns:BarcodeSymbologyType" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelBoxEntry">
        <xs:annotation>
          <xs:documentation>Solid (filled) rectangular area on label.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="TopLeftCorner" type="ns:CustomLabelPosition" minOccurs="1"/>
          <xs:element name="BottomRightCorner" type="ns:CustomLabelPosition" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomLabelCoordinateUnits">
        <xs:annotation>
          <xs:documentation>Valid values for CustomLabelCoordinateUnits</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="MILS"/>
          <xs:enumeration value="PIXELS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomLabelDetail">
        <xs:sequence>
          <xs:element name="CoordinateUnits" type="ns:CustomLabelCoordinateUnits" minOccurs="0"/>
          <xs:element name="TextEntries" type="ns:CustomLabelTextEntry" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="GraphicEntries" type="ns:CustomLabelGraphicEntry" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="BoxEntries" type="ns:CustomLabelBoxEntry" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="BarcodeEntries" type="ns:CustomLabelBarcodeEntry" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelGraphicEntry">
        <xs:annotation>
          <xs:documentation>Image to be included from printer's memory, or from a local file for offline clients.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Position" type="ns:CustomLabelPosition" minOccurs="0"/>
          <xs:element name="PrinterGraphicId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Printer-specific index of graphic image to be printed.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FileGraphicFullName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Fully-qualified path and file name for graphic image to be printed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelPosition">
        <xs:sequence>
          <xs:element name="X" type="xs:nonNegativeInteger" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Horizontal position, relative to left edge of custom area.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Y" type="xs:nonNegativeInteger" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Vertical position, relative to top edge of custom area.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelTextEntry">
        <xs:annotation>
          <xs:documentation>Constructed string, based on format and zero or more data fields, printed in specified printer font (for thermal labels) or generic font/size (for plain paper labels).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Position" type="ns:CustomLabelPosition" minOccurs="1"/>
          <xs:element name="Format" type="xs:string" minOccurs="0"/>
          <xs:element name="DataFields" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ThermalFontId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Printer-specific font name for use with thermal printer labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FontName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Generic font name for use with plain paper labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FontSize" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Generic font size for use with plain paper labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomerImageUsage">
        <xs:sequence>
          <xs:element name="Type" type="ns:CustomerImageUsageType" minOccurs="0"/>
          <xs:element name="Id" type="ns:ImageId" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomerImageUsageType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LETTER_HEAD"/>
          <xs:enumeration value="SIGNATURE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomerReference">
        <xs:annotation>
          <xs:documentation>Reference information to be associated with this package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="CustomerReferenceType" type="ns:CustomerReferenceType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The reference type to be associated with this reference data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:string" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomerReferenceType">
        <xs:annotation>
          <xs:documentation>The types of references available for use.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BILL_OF_LADING"/>
          <xs:enumeration value="CUSTOMER_REFERENCE"/>
          <xs:enumeration value="DEPARTMENT_NUMBER"/>
          <xs:enumeration value="ELECTRONIC_PRODUCT_CODE"/>
          <xs:enumeration value="INTRACOUNTRY_REGULATORY_REFERENCE"/>
          <xs:enumeration value="INVOICE_NUMBER"/>
          <xs:enumeration value="P_O_NUMBER"/>
          <xs:enumeration value="SHIPMENT_INTEGRITY"/>
          <xs:enumeration value="STORE_NUMBER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomerSpecifiedLabelDetail">
        <xs:annotation>
          <xs:documentation>Allows customer-specified control of label content.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="DocTabContent" type="ns:DocTabContent" minOccurs="0">
            <xs:annotation>
              <xs:documentation>If omitted, no doc tab will be produced (i.e. default = former NONE type).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomContent" type="ns:CustomLabelDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Defines any custom content to print on the label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ConfigurableReferenceEntries" type="ns:ConfigurableLabelReferenceEntry" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Defines additional data to print in the Configurable portion of the label, this allows you to print the same type information on the label that can also be printed on the doc tab.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MaskedData" type="ns:LabelMaskableDataType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Controls which data/sections will be suppressed.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ScncOverride" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-provided SCNC for use with label-data-only processing of FedEx Ground shipments.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TermsAndConditionsLocalization" type="ns:Localization" minOccurs="0"/>
          <xs:element name="AdditionalLabels" type="ns:AdditionalLabelsDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Controls the number of additional copies of supplemental labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AirWaybillSuppressionCount" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This value reduces the default quantity of destination/consignee air waybill labels. A value of zero indicates no change to default. A minimum of one copy will always be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomsClearanceDetail">
        <xs:sequence>
          <xs:element name="Broker" type="ns:Party" minOccurs="0"/>
          <xs:element name="ClearanceBrokerage" type="ns:ClearanceBrokerageType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Interacts both with properties of the shipment and contractual relationship with the shipper.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ImporterOfRecord" type="ns:Party" minOccurs="0"/>
          <xs:element name="RecipientCustomsId" type="ns:RecipientCustomsId" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how the recipient is identified for customs purposes; the requirements on this information vary with destination country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DutiesPayment" type="ns:Payment" minOccurs="0"/>
          <xs:element name="DocumentContent" type="ns:InternationalDocumentContentType" minOccurs="0"/>
          <xs:element name="CustomsValue" type="ns:Money" minOccurs="0"/>
          <xs:element name="FreightOnValue" type="ns:FreightOnValueType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies responsibilities with respect to loss, damage, etc.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="InsuranceCharges" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Documents amount paid to third party for coverage of shipment content.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PartiesToTransactionAreRelated" type="xs:boolean" minOccurs="0"/>
          <xs:element name="CommercialInvoice" type="ns:CommercialInvoice" minOccurs="0"/>
          <xs:element name="Commodities" type="ns:Commodity" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ExportDetail" type="ns:ExportDetail" minOccurs="0"/>
          <xs:element name="RegulatoryControls" type="ns:RegulatoryControlType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DangerousGoodsAccessibilityType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCESSIBLE"/>
          <xs:enumeration value="INACCESSIBLE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DangerousGoodsDetail">
        <xs:annotation>
          <xs:documentation>The descriptive data required for a FedEx shipment containing dangerous goods (hazardous materials).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Accessibility" type="ns:DangerousGoodsAccessibilityType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies whether or not the products being shipped are required to be accessible during delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CargoAircraftOnly" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Shipment is packaged/documented for movement ONLY on cargo aircraft.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Options" type="ns:HazardousCommodityOptionType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Indicates which kinds of hazardous content are in the current package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HazardousCommodities" type="ns:HazardousCommodityContent" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Documents the kinds and quantities of all hazardous commodities in the current package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Packaging" type="ns:HazardousCommodityPackagingDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Description of the packaging of this commodity, suitable for use on OP-900 and OP-950 forms.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EmergencyContactNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Telephone number to use for contact in the event of an emergency.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Offeror" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Offeror's name or contract number, per DOT regulation.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DateRange">
        <xs:sequence>
          <xs:element name="Begins" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The beginning date in a date range.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Ends" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The end date in a date range.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DayOfWeekType">
        <xs:annotation>
          <xs:documentation>Valid values for DayofWeekType</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FRI"/>
          <xs:enumeration value="MON"/>
          <xs:enumeration value="SAT"/>
          <xs:enumeration value="SUN"/>
          <xs:enumeration value="THU"/>
          <xs:enumeration value="TUE"/>
          <xs:enumeration value="WED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DeleteShipmentRequest">
        <xs:annotation>
          <xs:documentation>Descriptive data sent to FedEx by a customer in order to delete a package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipTimestamp" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The timestamp of the shipment request.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackingId" type="ns:TrackingId" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the FedEx tracking number of the package being cancelled.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeletionControl" type="ns:DeletionControlType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Determines the type of deletion to be performed in relation to package level vs shipment level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DeleteTagRequest">
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DispatchLocationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used for tags which had FedEx Express services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DispatchDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used for tags which had FedEx Express services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Payment" type="ns:Payment" minOccurs="1">
            <xs:annotation>
              <xs:documentation>If the original ProcessTagRequest specified third-party payment, then the delete request must contain the same pay type and payor account number for security purposes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ConfirmationNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Also known as Pickup Confirmation Number or Dispatch Number</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DeletionControlType">
        <xs:annotation>
          <xs:documentation>Specifies the type of deletion to be performed on a shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DELETE_ALL_PACKAGES"/>
          <xs:enumeration value="DELETE_ONE_PACKAGE"/>
          <xs:enumeration value="LEGACY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DestinationControlDetail">
        <xs:annotation>
          <xs:documentation>Data required to complete the Destionation Control Statement for US exports.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="StatementTypes" type="ns:DestinationControlStatementType" minOccurs="1" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>List of applicable Statment types.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationCountries" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Comma-separated list of up to four country codes, required for DEPARTMENT_OF_STATE statement.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EndUser" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Name of end user, required for DEPARTMENT_OF_STATE statement.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DestinationControlStatementType">
        <xs:annotation>
          <xs:documentation>Used to indicate whether the Destination Control Statement is of type Department of Commerce, Department of State or both.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DEPARTMENT_OF_COMMERCE"/>
          <xs:enumeration value="DEPARTMENT_OF_STATE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Dimensions">
        <xs:annotation>
          <xs:documentation>The dimensions of this package and the unit type used for the measurements.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Length" type="xs:nonNegativeInteger" minOccurs="1"/>
          <xs:element name="Width" type="xs:nonNegativeInteger" minOccurs="1"/>
          <xs:element name="Height" type="xs:nonNegativeInteger" minOccurs="1"/>
          <xs:element name="Units" type="ns:LinearUnits" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DocTabContent">
        <xs:sequence>
          <xs:element name="DocTabContentType" type="ns:DocTabContentType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The DocTabContentType options available.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Zone001" type="ns:DocTabContentZone001" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The DocTabContentType should be set to ZONE001 to specify additional Zone details.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Barcoded" type="ns:DocTabContentBarcoded" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The DocTabContentType should be set to BARCODED to specify additional BarCoded details.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DocTabContentBarcoded">
        <xs:sequence>
          <xs:element name="Symbology" type="ns:BarcodeSymbologyType" minOccurs="0"/>
          <xs:element name="Specification" type="ns:DocTabZoneSpecification" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DocTabContentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BARCODED"/>
          <xs:enumeration value="MINIMUM"/>
          <xs:enumeration value="STANDARD"/>
          <xs:enumeration value="ZONE001"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocTabContentZone001">
        <xs:sequence>
          <xs:element name="DocTabZoneSpecifications" type="ns:DocTabZoneSpecification" minOccurs="1" maxOccurs="12"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DocTabZoneJustificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LEFT"/>
          <xs:enumeration value="RIGHT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocTabZoneSpecification">
        <xs:sequence>
          <xs:element name="ZoneNumber" type="xs:positiveInteger" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Zone number can be between 1 and 12.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Header" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Header value on this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DataField" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Reference path to the element in the request/reply whose value should be printed on this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LiteralValue" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free form-text to be printed in this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Justification" type="ns:DocTabZoneJustificationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Justification for the text printed on this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DropoffType">
        <xs:annotation>
          <xs:documentation>Identifies the method by which the package is to be tendered to FedEx. This element does not dispatch a courier for package pickup.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BUSINESS_SERVICE_CENTER"/>
          <xs:enumeration value="DROP_BOX"/>
          <xs:enumeration value="REGULAR_PICKUP"/>
          <xs:enumeration value="REQUEST_COURIER"/>
          <xs:enumeration value="STATION"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EMailLabelDetail">
        <xs:annotation>
          <xs:documentation>Describes specific information about the email label shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="NotificationEMailAddress" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Notification email will be sent to this email address</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NotificationMessage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Message to be sent in the notification email</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EMailNotificationAggregationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PER_PACKAGE"/>
          <xs:enumeration value="PER_SHIPMENT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EMailNotificationDetail">
        <xs:annotation>
          <xs:documentation>Information describing email notifications that will be sent in relation to events that occur during package movement</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="AggregationType" type="ns:EMailNotificationAggregationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies whether/how email notifications are grouped.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PersonalMessage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A message that will be included in the email notifications</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Recipients" type="ns:EMailNotificationRecipient" minOccurs="1" maxOccurs="6">
            <xs:annotation>
              <xs:documentation>Information describing the destination of the email, format of the email and events to be notified on</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EMailNotificationFormatType">
        <xs:annotation>
          <xs:documentation>The format of the email</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="HTML"/>
          <xs:enumeration value="TEXT"/>
          <xs:enumeration value="WIRELESS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EMailNotificationRecipient">
        <xs:annotation>
          <xs:documentation>The descriptive data for a FedEx email notification recipient.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="EMailNotificationRecipientType" type="ns:EMailNotificationRecipientType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the relationship this email recipient has to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EMailAddress" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The email address to send the notification to</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NotifyOnShipment" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Notify the email recipient when this shipment has been shipped.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NotifyOnException" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Notify the email recipient if this shipment encounters a problem while in route</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NotifyOnDelivery" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Notify the email recipient when this shipment has been delivered.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Format" type="ns:EMailNotificationFormatType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The format of the email notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The language/locale to be used in this email notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EMailNotificationRecipientType">
        <xs:annotation>
          <xs:documentation>Identifies the set of valid email notification recipient types. For SHIPPER, RECIPIENT and BROKER the email address asssociated with their definitions will be used, any email address sent with the email notification for these three email notification recipient types will be ignored.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="RECIPIENT"/>
          <xs:enumeration value="SHIPPER"/>
          <xs:enumeration value="THIRD_PARTY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EdtCommodityTax">
        <xs:sequence>
          <xs:element name="HarmonizedCode" type="xs:string" minOccurs="0"/>
          <xs:element name="Taxes" type="ns:EdtTaxDetail" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="EdtExciseCondition">
        <xs:sequence>
          <xs:element name="Category" type="xs:string" minOccurs="0"/>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-declared value, with data type and legal values depending on excise condition, used in defining the taxable value of the item.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EdtRequestType">
        <xs:annotation>
          <xs:documentation>Specifies the types of Estimated Duties and Taxes to be included in a rate quotation for an international shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ALL"/>
          <xs:enumeration value="NONE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EdtTaxDetail">
        <xs:sequence>
          <xs:element name="TaxType" type="ns:EdtTaxType" minOccurs="0"/>
          <xs:element name="EffectiveDate" type="xs:date" minOccurs="0"/>
          <xs:element name="Name" type="xs:string" minOccurs="0"/>
          <xs:element name="TaxableValue" type="ns:Money" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Formula" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EdtTaxType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDITIONAL_TAXES"/>
          <xs:enumeration value="CONSULAR_INVOICE_FEE"/>
          <xs:enumeration value="CUSTOMS_SURCHARGES"/>
          <xs:enumeration value="DUTY"/>
          <xs:enumeration value="EXCISE_TAX"/>
          <xs:enumeration value="FOREIGN_EXCHANGE_TAX"/>
          <xs:enumeration value="GENERAL_SALES_TAX"/>
          <xs:enumeration value="IMPORT_LICENSE_FEE"/>
          <xs:enumeration value="INTERNAL_ADDITIONAL_TAXES"/>
          <xs:enumeration value="INTERNAL_SENSITIVE_PRODUCTS_TAX"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="SENSITIVE_PRODUCTS_TAX"/>
          <xs:enumeration value="STAMP_TAX"/>
          <xs:enumeration value="STATISTICAL_TAX"/>
          <xs:enumeration value="TRANSPORT_FACILITIES_TAX"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ErrorLabelBehaviorType">
        <xs:annotation>
          <xs:documentation>
            Specifies the client-requested response in the event of errors within shipment.
            PACKAGE_ERROR_LABELS : Return per-package error label in addition to error Notifications.
            STANDARD             : Return error Notifications only.
          </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="PACKAGE_ERROR_LABELS"/>
          <xs:enumeration value="STANDARD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EtdDetail">
        <xs:annotation>
          <xs:documentation>Electronic Trade document references used with the ETD special service.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RequestedDocumentCopies" type="ns:RequestedShippingDocumentType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Indicates the types of shipping documents produced for the shipper by FedEx (see ShippingDocumentSpecification) which should be copied back to the shipper in the shipment result data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DocumentReferences" type="ns:UploadDocumentReferenceDetail" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExportDetail">
        <xs:annotation>
          <xs:documentation>Country specific details of an International shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="B13AFilingOption" type="ns:B13AFilingOptionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Specifies which filing option is being exercised by the customer.
                Required for non-document shipments originating in Canada destined for any country other than Canada, the United States, Puerto Rico or the U.S. Virgin Islands.
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExportComplianceStatement" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>General field for exporting-country-specific export data (e.g. B13A for CA, FTSR Exemption or AES Citation for US).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PermitNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This field is applicable only to Canada export non-document shipments of any value to any destination. No special characters allowed. </xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>10</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationControlDetail" type="ns:DestinationControlDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Department of Commerce/Department of State information about this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExpressFreightDetail">
        <xs:annotation>
          <xs:documentation>Details specific to an Express freight shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PackingListEnclosed" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether or nor a packing list is enclosed.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShippersLoadAndCount" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Total shipment pieces.
                e.g. 3 boxes and 3 pallets of 100 pieces each = Shippers Load and Count of 303.
                Applicable to International Priority Freight and International Economy Freight.
                Values must be in the range of 1 - 99999
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BookingConfirmationNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Required for International Freight shipping. Values must be 8- 12 characters in length.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>12</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FedExLocationType">
        <xs:annotation>
          <xs:documentation>Identifies a kind of FedEx facility.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_EXPRESS_STATION"/>
          <xs:enumeration value="FEDEX_GROUND_TERMINAL"/>
          <xs:enumeration value="FEDEX_OFFICE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightAccountPaymentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COLLECT"/>
          <xs:enumeration value="PREPAID"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightAddressLabelDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce the Freight handling-unit-level address labels. Note that the number of UNIQUE labels (the N as in 1 of N, 2 of N, etc.) is determined by total handling units.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0"/>
          <xs:element name="Copies" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the number of copies to be produced for each unique label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DocTabContent" type="ns:DocTabContent" minOccurs="0">
            <xs:annotation>
              <xs:documentation>If omitted, no doc tab will be produced (i.e. default = former NONE type).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightBaseCharge">
        <xs:annotation>
          <xs:documentation>Individual charge which contributes to the total base charge for the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FreightClass" type="ns:FreightClassType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Freight class for this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RatedAsClass" type="ns:FreightClassType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Effective freight class used for rating this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NmfcCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>NMFC Code for commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-provided description for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Weight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Weight for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ChargeRate" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Rate or factor applied to this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ChargeBasis" type="ns:FreightChargeBasisType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the manner in which the chargeRate for this line item was applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExtendedAmount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The net or extended charge for this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightChargeBasisType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CWT"/>
          <xs:enumeration value="FLAT"/>
          <xs:enumeration value="MINIMUM"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightClassType">
        <xs:annotation>
          <xs:documentation>These values represent the industry-standard freight classes used for FedEx Freight and FedEx National Freight shipment description. (Note: The alphabetic prefixes are required to distinguish these values from decimal numbers on some client platforms.)</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CLASS_050"/>
          <xs:enumeration value="CLASS_055"/>
          <xs:enumeration value="CLASS_060"/>
          <xs:enumeration value="CLASS_065"/>
          <xs:enumeration value="CLASS_070"/>
          <xs:enumeration value="CLASS_077_5"/>
          <xs:enumeration value="CLASS_085"/>
          <xs:enumeration value="CLASS_092_5"/>
          <xs:enumeration value="CLASS_100"/>
          <xs:enumeration value="CLASS_110"/>
          <xs:enumeration value="CLASS_125"/>
          <xs:enumeration value="CLASS_150"/>
          <xs:enumeration value="CLASS_175"/>
          <xs:enumeration value="CLASS_200"/>
          <xs:enumeration value="CLASS_250"/>
          <xs:enumeration value="CLASS_300"/>
          <xs:enumeration value="CLASS_400"/>
          <xs:enumeration value="CLASS_500"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightCollectTermsType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="SECTION_7_SIGNED"/>
          <xs:enumeration value="STANDARD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightOnValueType">
        <xs:annotation>
          <xs:documentation>Identifies responsibilities with respect to loss, damage, etc.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CARRIER_RISK"/>
          <xs:enumeration value="OWN_RISK"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightRateDetail">
        <xs:annotation>
          <xs:documentation>Rate data specific to FedEx Freight or FedEx National Freight services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="QuoteNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A unique identifier for a specific rate quotation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BaseCharges" type="ns:FreightBaseCharge" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Freight charges which accumulate to the total base charge for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Notations" type="ns:FreightRateNotation" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Human-readable descriptions of additional information on this shipment rating.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightRateNotation">
        <xs:annotation>
          <xs:documentation>Additional non-monetary data returned with Freight rates.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Code" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Unique identifier for notation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Human-readable explanation of notation.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightShipmentDetail">
        <xs:annotation>
          <xs:documentation>Data applicable to shipments using FEDEX_FREIGHT and FEDEX_NATIONAL_FREIGHT services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FedExFreightAccountNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Account number used with FEDEX_FREIGHT service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FedExFreightBillingContactAndAddress" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used for validating FedEx Freight account number and (optionally) identifying third party payment on the bill of lading.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PrintedReferences" type="ns:PrintedReference" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Identification values to be printed during creation of a Freight bill of lading.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Role" type="ns:FreightShipmentRoleType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the role of the party submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PaymentType" type="ns:FreightAccountPaymentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Designates which of the requester's tariffs will be used for rating.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CollectTermsType" type="ns:FreightCollectTermsType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Designates the terms of the "collect" payment for a Freight Shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeclaredValuePerUnit" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the declared value for the shipment</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeclaredValueUnits" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the declared value units corresponding to the above defined declared value</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LiabilityCoverageDetail" type="ns:LiabilityCoverageDetail" minOccurs="0"/>
          <xs:element name="Coupons" type="xs:string" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Identifiers for promotional discounts offered to customers.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalHandlingUnits" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total number of individual handling units in the entire shipment (for unit pricing).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDiscountPercent" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Estimated discount rate provided by client for unsecured rate quote.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PalletWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total weight of pallets used in shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentDimensions" type="ns:Dimensions" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Overall shipment dimensions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Comment" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Description for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialServicePayments" type="ns:FreightSpecialServicePayment" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies which party will pay surcharges for any special services which support split billing.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HazardousMaterialsEmergencyContactNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Must be populated if any line items contain hazardous materials.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LineItems" type="ns:FreightShipmentLineItem" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Details of the commodities in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightShipmentLineItem">
        <xs:annotation>
          <xs:documentation>Description of an individual commodity or class of content in a shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FreightClass" type="ns:FreightClassType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Freight class for this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClassProvidedByCustomer" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>FEDEX INTERNAL USE ONLY: for FedEx system that estimate freight class from customer-provided dimensions and weight.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HandlingUnits" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Number of individual handling units to which this line applies. (NOTE: Total of line-item-level handling units may not balance to shipment-level total handling units.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Packaging" type="ns:PhysicalPackagingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specification of handling-unit packaging for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Pieces" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Number of pieces for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NmfcCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>NMFC Code for commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HazardousMaterials" type="ns:HazardousCommodityOptionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the kind of hazardous material content in this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BillOfLadingNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For printed reference per line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PurchaseOrderNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For printed reference per line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-provided description for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Weight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Weight for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Dimensions" type="ns:Dimensions" minOccurs="0">
            <xs:annotation>
              <xs:documentation>FED EX INTERNAL USE ONLY - Individual line item dimensions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Volume" type="ns:Volume" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Volume (cubic measure) for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightShipmentRoleType">
        <xs:annotation>
          <xs:documentation>Indicates the role of the party submitting the transaction.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONSIGNEE"/>
          <xs:enumeration value="SHIPPER"/>
          <xs:enumeration value="THIRD_PARTY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightSpecialServicePayment">
        <xs:annotation>
          <xs:documentation>Specifies which party will be responsible for payment of any surcharges for Freight special services for which split billing is allowed.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SpecialService" type="ns:ShipmentSpecialServiceType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the special service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PaymentType" type="ns:FreightAccountPaymentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates who will pay for the special service.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="GeneralAgencyAgreementDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce a General Agency Agreement document. Remaining content (business data) to be defined once requirements have been completed.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HazardousCommodityContent">
        <xs:annotation>
          <xs:documentation>Documents the kind and quantity of an individual hazardous commodity in a package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Description" type="ns:HazardousCommodityDescription" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies and describes an individual hazardous commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Quantity" type="ns:HazardousCommodityQuantityDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the amount of the commodity in alternate units.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Options" type="ns:HazardousCommodityOptionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-provided specifications for handling individual commodities.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HazardousCommodityDescription">
        <xs:annotation>
          <xs:documentation>Identifies and describes an individual hazardous commodity. For 201001 load, this is based on data from the FedEx Ground Hazardous Materials Shipping Guide.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Id" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Regulatory identifier for a commodity (e.g. "UN ID" value).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackingGroup" type="ns:HazardousCommodityPackingGroupType" minOccurs="0"/>
          <xs:element name="ProperShippingName" type="xs:string" minOccurs="0"/>
          <xs:element name="TechnicalName" type="xs:string" minOccurs="0"/>
          <xs:element name="HazardClass" type="xs:string" minOccurs="0"/>
          <xs:element name="SubsidiaryClasses" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="LabelText" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityLabelTextOptionType">
        <xs:annotation>
          <xs:documentation>Specifies how the commodity is to be labeled.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="APPEND"/>
          <xs:enumeration value="OVERRIDE"/>
          <xs:enumeration value="STANDARD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityOptionDetail">
        <xs:annotation>
          <xs:documentation>Customer-provided specifications for handling individual commodities.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="LabelTextOption" type="ns:HazardousCommodityLabelTextOptionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how the customer wishes the label text to be handled for this commodity in this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerSuppliedLabelText" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Text used in labeling the commodity under control of the labelTextOption field.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityOptionType">
        <xs:annotation>
          <xs:documentation>Indicates which kind of hazardous content (as defined by DOT) is being reported.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="HAZARDOUS_MATERIALS"/>
          <xs:enumeration value="LITHIUM_BATTERY_EXCEPTION"/>
          <xs:enumeration value="ORM_D"/>
          <xs:enumeration value="REPORTABLE_QUANTITIES"/>
          <xs:enumeration value="SMALL_QUANTITY_EXCEPTION"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityPackagingDetail">
        <xs:annotation>
          <xs:documentation>Identifies number and type of packaging units for hazardous commodities.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Count" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Number of units of the type below.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Units" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Units in which the hazardous commodity is packaged.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityPackingGroupType">
        <xs:annotation>
          <xs:documentation>Identifies DOT packing group for a hazardous commodity.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="I"/>
          <xs:enumeration value="II"/>
          <xs:enumeration value="III"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityQuantityDetail">
        <xs:annotation>
          <xs:documentation>Identifies amount and units for quantity of hazardous commodities.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Amount" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Number of units of the type below.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Units" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Units by which the hazardous commodity is measured.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HoldAtLocationDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data required for a FedEx shipment that is to be held at the destination FedEx location for pickup by the recipient.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PhoneNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Contact phone number for recipient of shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocationContactAndAddress" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contact and address of FedEx facility at which shipment is to be held.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocationType" type="ns:FedExLocationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Type of facility at which package/shipment is to be held.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HomeDeliveryPremiumDetail">
        <xs:annotation>
          <xs:documentation>The descriptive data required by FedEx for home delivery services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="HomeDeliveryPremiumType" type="ns:HomeDeliveryPremiumType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The type of Home Delivery Premium service being requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Date" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Required for Date Certain Home Delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PhoneNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Required for Date Certain and Appointment Home Delivery.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>15</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HomeDeliveryPremiumType">
        <xs:annotation>
          <xs:documentation>The type of Home Delivery Premium service being requested.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="APPOINTMENT"/>
          <xs:enumeration value="DATE_CERTAIN"/>
          <xs:enumeration value="EVENING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ImageId">
        <xs:restriction base="xs:string">
          <xs:enumeration value="IMAGE_1"/>
          <xs:enumeration value="IMAGE_2"/>
          <xs:enumeration value="IMAGE_3"/>
          <xs:enumeration value="IMAGE_4"/>
          <xs:enumeration value="IMAGE_5"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="InternationalDocumentContentType">
        <xs:annotation>
          <xs:documentation>The type of International shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DERIVED"/>
          <xs:enumeration value="DOCUMENTS_ONLY"/>
          <xs:enumeration value="NON_DOCUMENTS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelFormatType">
        <xs:annotation>
          <xs:documentation>Specifies the type of label to be returned.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMMON2D"/>
          <xs:enumeration value="FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING"/>
          <xs:enumeration value="LABEL_DATA_ONLY"/>
          <xs:enumeration value="VICS_BILL_OF_LADING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelMaskableDataType">
        <xs:annotation>
          <xs:documentation>Names for data elements / areas which may be suppressed from printing on labels.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMS_VALUE"/>
          <xs:enumeration value="DUTIES_AND_TAXES_PAYOR_ACCOUNT_NUMBER"/>
          <xs:enumeration value="SHIPPER_ACCOUNT_NUMBER"/>
          <xs:enumeration value="TERMS_AND_CONDITIONS"/>
          <xs:enumeration value="TRANSPORTATION_CHARGES_PAYOR_ACCOUNT_NUMBER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelPrintingOrientationType">
        <xs:annotation>
          <xs:documentation>This indicates if the top or bottom of the label comes out of the printer first.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BOTTOM_EDGE_OF_TEXT_FIRST"/>
          <xs:enumeration value="TOP_EDGE_OF_TEXT_FIRST"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelRotationType">
        <xs:annotation>
          <xs:documentation>Relative to normal orientation for the printer.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="LEFT"/>
          <xs:enumeration value="NONE"/>
          <xs:enumeration value="RIGHT"/>
          <xs:enumeration value="UPSIDE_DOWN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LabelSpecification">
        <xs:annotation>
          <xs:documentation>Description of shipping label to be returned in the reply</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Dispositions" type="ns:ShippingDocumentDispositionDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies how to create, organize, and return the document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelFormatType" type="ns:LabelFormatType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Specify type of label to be returned</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ImageType" type="ns:ShippingDocumentImageType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the image format used for a shipping document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelStockType" type="ns:LabelStockType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For thermal printer lables this indicates the size of the label and the location of the doc tab if present.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelPrintingOrientation" type="ns:LabelPrintingOrientationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This indicates if the top or bottom of the label comes out of the printer first.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PrintedLabelOrigin" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>If present, this contact and address information will replace the return address information on the label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerSpecifiedDetail" type="ns:CustomerSpecifiedLabelDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Allows customer-specified control of label content.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LabelStockType">
        <xs:annotation>
          <xs:documentation>For thermal printer labels this indicates the size of the label and the location of the doc tab if present.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="PAPER_4X6"/>
          <xs:enumeration value="PAPER_4X8"/>
          <xs:enumeration value="PAPER_4X9"/>
          <xs:enumeration value="PAPER_7X4.75"/>
          <xs:enumeration value="PAPER_8.5X11_BOTTOM_HALF_LABEL"/>
          <xs:enumeration value="PAPER_8.5X11_TOP_HALF_LABEL"/>
          <xs:enumeration value="PAPER_LETTER"/>
          <xs:enumeration value="STOCK_4X6"/>
          <xs:enumeration value="STOCK_4X6.75_LEADING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X6.75_TRAILING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X8"/>
          <xs:enumeration value="STOCK_4X9_LEADING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X9_TRAILING_DOC_TAB"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LiabilityCoverageDetail">
        <xs:sequence>
          <xs:element name="CoverageType" type="ns:LiabilityCoverageType" minOccurs="0"/>
          <xs:element name="CoverageAmount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the Liability Coverage Amount. For Jan 2010 this value represents coverage amount per pound</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LiabilityCoverageType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="NEW"/>
          <xs:enumeration value="USED_OR_RECONDITIONED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LinearMeasure">
        <xs:annotation>
          <xs:documentation>Represents a one-dimensional measurement in small units (e.g. suitable for measuring a package or document), contrasted with Distance, which represents a large one-dimensional measurement (e.g. distance between cities).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Value" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The numerical quantity of this measurement.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Units" type="ns:LinearUnits" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The units for this measurement.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LinearUnits">
        <xs:annotation>
          <xs:documentation>CM = centimeters, IN = inches</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CM"/>
          <xs:enumeration value="IN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Localization">
        <xs:annotation>
          <xs:documentation>Identifies the representation of human-readable text.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="LanguageCode" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Two-letter code for language (e.g. EN, FR, etc.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocaleCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Two-letter code for the region (e.g. us, ca, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Measure">
        <xs:sequence>
          <xs:element name="Quantity" type="xs:decimal" minOccurs="0"/>
          <xs:element name="Units" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="MinimumChargeType">
        <xs:annotation>
          <xs:documentation>Identifies which type minimum charge was applied.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMER"/>
          <xs:enumeration value="CUSTOMER_FREIGHT_WEIGHT"/>
          <xs:enumeration value="EARNED_DISCOUNT"/>
          <xs:enumeration value="MIXED"/>
          <xs:enumeration value="RATE_SCALE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Money">
        <xs:annotation>
          <xs:documentation>The descriptive data for the medium of exchange for FedEx services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Currency" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the currency of the monetary amount.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>3</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Amount" type="xs:decimal" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the monetary amount.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="NaftaCertificateOfOriginDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce a Certificate of Origin document. Remaining content (business data) to be defined once requirements have been completed.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0"/>
          <xs:element name="BlanketPeriod" type="ns:DateRange" minOccurs="0"/>
          <xs:element name="ImporterSpecification" type="ns:NaftaImporterSpecificationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates which Party (if any) from the shipment is to be used as the source of importer data on the NAFTA COO form.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureContact" type="ns:Contact" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contact information for "Authorized Signature" area of form.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProducerSpecification" type="ns:NaftaProducerSpecificationType" minOccurs="0"/>
          <xs:element name="Producers" type="ns:NaftaProducer" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="CustomerImageUsages" type="ns:CustomerImageUsage" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="NaftaCommodityDetail">
        <xs:annotation>
          <xs:documentation>This element is currently not supported and is for the future use.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PreferenceCriterion" type="ns:NaftaPreferenceCriterionCode" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Defined by NAFTA regulations.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProducerDetermination" type="ns:NaftaProducerDeterminationCode" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Defined by NAFTA regulations.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProducerId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identification of which producer is associated with this commodity (if multiple producers are used in a single shipment).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NetCostMethod" type="ns:NaftaNetCostMethodCode" minOccurs="0"/>
          <xs:element name="NetCostDateRange" type="ns:DateRange" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Date range over which RVC net cost was calculated.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NaftaImporterSpecificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="IMPORTER_OF_RECORD"/>
          <xs:enumeration value="RECIPIENT"/>
          <xs:enumeration value="UNKNOWN"/>
          <xs:enumeration value="VARIOUS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NaftaNetCostMethodCode">
        <xs:annotation>
          <xs:documentation>Net cost method used.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="NC"/>
          <xs:enumeration value="NO"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NaftaPreferenceCriterionCode">
        <xs:annotation>
          <xs:documentation>See instructions for NAFTA Certificate of Origin for code definitions.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="A"/>
          <xs:enumeration value="B"/>
          <xs:enumeration value="C"/>
          <xs:enumeration value="D"/>
          <xs:enumeration value="E"/>
          <xs:enumeration value="F"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="NaftaProducer">
        <xs:annotation>
          <xs:documentation>This element is currently not supported and is for the future use.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Id" type="xs:string" minOccurs="0"/>
          <xs:element name="Producer" type="ns:Party" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NaftaProducerDeterminationCode">
        <xs:annotation>
          <xs:documentation>See instructions for NAFTA Certificate of Origin for code definitions.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="NO_1"/>
          <xs:enumeration value="NO_2"/>
          <xs:enumeration value="NO_3"/>
          <xs:enumeration value="YES"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NaftaProducerSpecificationType">
        <xs:annotation>
          <xs:documentation>This element is currently not supported and is for the future use.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="AVAILABLE_UPON_REQUEST"/>
          <xs:enumeration value="MULTIPLE_SPECIFIED"/>
          <xs:enumeration value="SAME"/>
          <xs:enumeration value="SINGLE_SPECIFIED"/>
          <xs:enumeration value="UNKNOWN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Notification">
        <xs:annotation>
          <xs:documentation>The descriptive data regarding the result of the submitted transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Severity" type="ns:NotificationSeverityType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The severity of this notification. This can indicate success or failure or some other information about the request. The values that can be returned are SUCCESS - Your transaction succeeded with no other applicable information. NOTE - Additional information that may be of interest to you about your transaction. WARNING - Additional information that you need to know about your transaction that you may need to take action on. ERROR - Information about an error that occurred while processing your transaction. FAILURE - FedEx was unable to process your transaction at this time due to a system failure. Please try again later</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Source" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Indicates the source of this notification. Combined with the Code it uniquely identifies this notification</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Code" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A code that represents this notification. Combined with the Source it uniquely identifies this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Message" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Human-readable text that explains this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocalizedMessage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The translated message. The language and locale specified in the ClientDetail. Localization are used to determine the representation. Currently only supported in a TrackReply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MessageParameters" type="ns:NotificationParameter" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>A collection of name/value pairs that provide specific data to help the client determine the nature of an error (or warning, etc.) witout having to parse the message string.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="NotificationParameter">
        <xs:sequence>
          <xs:element name="Id" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of data contained in Value (e.g. SERVICE_TYPE, PACKAGE_SEQUENCE, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The value of the parameter (e.g. PRIORITY_OVERNIGHT, 2, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NotificationSeverityType">
        <xs:annotation>
          <xs:documentation>Identifies the set of severity values for a Notification.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ERROR"/>
          <xs:enumeration value="FAILURE"/>
          <xs:enumeration value="NOTE"/>
          <xs:enumeration value="SUCCESS"/>
          <xs:enumeration value="WARNING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Op900Detail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the OP-900 form for hazardous materials packages.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Reference" type="ns:CustomerReferenceType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies which reference type (from the package's customer references) is to be used as the source for the reference on this OP-900.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerImageUsages" type="ns:CustomerImageUsage" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of customer supplied images to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Data field to be used when a name is to be printed in the document instead of (or in addition to) a signature image.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="OversizeClassType">
        <xs:annotation>
          <xs:documentation>The oversize class types.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="OVERSIZE_1"/>
          <xs:enumeration value="OVERSIZE_2"/>
          <xs:enumeration value="OVERSIZE_3"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PackageBarcodes">
        <xs:annotation>
          <xs:documentation>Each instance of this data type represents the set of barcodes (of all types) which are associated with a specific package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="BinaryBarcodes" type="ns:BinaryBarcode" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Binary-style barcodes for this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StringBarcodes" type="ns:StringBarcode" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>String-style barcodes for this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="PackageRateDetail">
        <xs:annotation>
          <xs:documentation>Data for a package's rates, as calculated per a specific rate type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RateType" type="ns:ReturnedRateType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Type used for this specific set of rate data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RatedWeightMethod" type="ns:RatedWeightMethod" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates which weight was used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MinimumChargeType" type="ns:MinimumChargeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>INTERNAL FEDEX USE ONLY.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BillingWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The weight that was used to calculate the rate.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DimWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The dimensional weight of this package (if greater than actual).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OversizeWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The oversize weight of this package (if the package is oversize).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BaseCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The transportation charge only (prior to any discounts applied) for this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalFreightDiscounts" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The sum of all discounts on this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NetFreight" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This package's baseCharge - totalFreightDiscounts.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalSurcharges" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The sum of all surcharges on this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NetFedExCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This package's netFreight + totalSurcharges (not including totalTaxes).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The sum of all taxes on this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NetCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This package's netFreight + totalSurcharges + totalTaxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalRebates" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total sum of all rebates applied to this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightDiscounts" type="ns:RateDiscount" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rate discounts that apply to this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Rebates" type="ns:Rebate" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rebates that apply to this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Surcharges" type="ns:Surcharge" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All surcharges that apply to this package (either because of characteristics of the package itself, or because it is carrying per-shipment surcharges for the shipment of which it is a part).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Taxes" type="ns:Tax" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All taxes applicable (or distributed to) this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableHandlingCharges" type="ns:VariableHandlingCharges" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The variable handling charges calculated based on the type variable handling charges requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="PackageRating">
        <xs:annotation>
          <xs:documentation>This class groups together for a single package all package-level rate data (across all rate types) as part of the response to a shipping request, which groups shipment-level data together and groups package-level data by package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ActualRateType" type="ns:ReturnedRateType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This rate type identifies which entry in the following array is considered as presenting the "actual" rates for the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EffectiveNetDiscount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The "list" net charge minus "actual" net charge.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageRateDetails" type="ns:PackageRateDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Each element of this field provides package-level rate data for a specific rate type.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PackageSpecialServiceType">
        <xs:annotation>
          <xs:documentation>Identifies the collection of special service offered by FedEx. BROKER_SELECT_OPTION should be used for Ground shipments only.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="APPOINTMENT_DELIVERY"/>
          <xs:enumeration value="COD"/>
          <xs:enumeration value="DANGEROUS_GOODS"/>
          <xs:enumeration value="DRY_ICE"/>
          <xs:enumeration value="NON_STANDARD_CONTAINER"/>
          <xs:enumeration value="PRIORITY_ALERT"/>
          <xs:enumeration value="SIGNATURE_OPTION"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PackageSpecialServicesRequested">
        <xs:annotation>
          <xs:documentation>These special services are available at the package level for some or all service types. If the shipper is requesting a special service which requires additional data, the package special service type must be present in the specialServiceTypes collection, and the supporting detail must be provided in the appropriate sub-object below.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SpecialServiceTypes" type="ns:PackageSpecialServiceType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The types of all special services requested for the enclosing shipment or package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodDetail" type="ns:CodDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For use with FedEx Ground services only; COD must be present in shipment's special services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DangerousGoodsDetail" type="ns:DangerousGoodsDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for a FedEx shipment containing dangerous materials. This element is required when SpecialServiceType.DANGEROUS_GOODS or HAZARDOUS_MATERIAL is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DryIceWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for a FedEx shipment containing dry ice. This element is required when SpecialServiceType.DRY_ICE is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureOptionDetail" type="ns:SignatureOptionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data required for FedEx signature services. This element is required when SpecialServiceType.SIGNATURE_OPTION is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PriorityAlertDetail" type="ns:PriorityAlertDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data required for FedEx Priority Alert service. This element is required when SpecialServiceType.PRIORITY_ALERT is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PackagingType">
        <xs:annotation>
          <xs:documentation>Identifies the collection of available FedEx or customer packaging options.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_10KG_BOX"/>
          <xs:enumeration value="FEDEX_25KG_BOX"/>
          <xs:enumeration value="FEDEX_BOX"/>
          <xs:enumeration value="FEDEX_ENVELOPE"/>
          <xs:enumeration value="FEDEX_PAK"/>
          <xs:enumeration value="FEDEX_TUBE"/>
          <xs:enumeration value="YOUR_PACKAGING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Party">
        <xs:annotation>
          <xs:documentation>The descriptive data for a person or company entitiy doing business with FedEx.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="AccountNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the FedEx account number assigned to the customer.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>12</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Tins" type="ns:TaxpayerIdentification" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="Contact" type="ns:Contact" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the point-of-contact person.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Address" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data for a physical location.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Payment">
        <xs:annotation>
          <xs:documentation>The descriptive data for the monetary compensation given to FedEx for services rendered to the customer.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PaymentType" type="ns:PaymentType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the method of payment for a service. See PaymentType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Payor" type="ns:Payor" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the party responsible for payment for a service.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PaymentType">
        <xs:annotation>
          <xs:documentation>Identifies the method of payment for a service.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COLLECT"/>
          <xs:enumeration value="RECIPIENT"/>
          <xs:enumeration value="SENDER"/>
          <xs:enumeration value="THIRD_PARTY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Payor">
        <xs:annotation>
          <xs:documentation>The descriptive data identifying the party responsible for payment for a service.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="AccountNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the FedEx account number assigned to the payor.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>12</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="CountryCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the country of the payor.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>2</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="PendingShipmentAccessDetail">
        <xs:annotation>
          <xs:documentation>This information describes how and when a pending shipment may be accessed for completion.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="EmailLabelUrl" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only for pending shipment type of "EMAIL"</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UserId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only for pending shipment type of "EMAIL"</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Password" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only for pending shipment type of "EMAIL"</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExpirationTimestamp" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This element is currently not supported and is for the future use.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="PendingShipmentDetail">
        <xs:annotation>
          <xs:documentation>This information describes the kind of pending shipment being requested.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:PendingShipmentType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the type of FedEx pending shipment</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExpirationDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Date after which the pending shipment will no longer be available for completion.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EmailLabelDetail" type="ns:EMailLabelDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used with type of EMAIL.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PendingShipmentType">
        <xs:annotation>
          <xs:documentation>Identifies the type of service for a pending shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EMAIL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PhysicalPackagingType">
        <xs:annotation>
          <xs:documentation>This enumeration rationalizes the former FedEx Express international "admissibility package" types (based on ANSI X.12) and the FedEx Freight packaging types. The values represented are those common to both carriers.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BAG"/>
          <xs:enumeration value="BARREL"/>
          <xs:enumeration value="BASKET"/>
          <xs:enumeration value="BOX"/>
          <xs:enumeration value="BUCKET"/>
          <xs:enumeration value="BUNDLE"/>
          <xs:enumeration value="CARTON"/>
          <xs:enumeration value="CASE"/>
          <xs:enumeration value="CONTAINER"/>
          <xs:enumeration value="CRATE"/>
          <xs:enumeration value="CYLINDER"/>
          <xs:enumeration value="DRUM"/>
          <xs:enumeration value="ENVELOPE"/>
          <xs:enumeration value="HAMPER"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PAIL"/>
          <xs:enumeration value="PALLET"/>
          <xs:enumeration value="PIECE"/>
          <xs:enumeration value="REEL"/>
          <xs:enumeration value="ROLL"/>
          <xs:enumeration value="SKID"/>
          <xs:enumeration value="TANK"/>
          <xs:enumeration value="TUBE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PickupDetail">
        <xs:annotation>
          <xs:documentation>This class describes the pickup characteristics of a shipment (e.g. for use in a tag request).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ReadyDateTime" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="LatestPickupDateTime" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="CourierInstructions" type="xs:string" minOccurs="0"/>
          <xs:element name="RequestType" type="ns:PickupRequestType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of Pickup request</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestSource" type="ns:PickupRequestSourceType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of source for Pickup request</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PickupRequestSourceType">
        <xs:annotation>
          <xs:documentation>Identifies the type of source for pickup request service.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="AUTOMATION"/>
          <xs:enumeration value="CUSTOMER_SERVICE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PickupRequestType">
        <xs:annotation>
          <xs:documentation>Identifies the type of pickup request service.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FUTURE_DAY"/>
          <xs:enumeration value="SAME_DAY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PricingCodeType">
        <xs:annotation>
          <xs:documentation>Identifies the type of pricing used for this shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACTUAL"/>
          <xs:enumeration value="ALTERNATE"/>
          <xs:enumeration value="BASE"/>
          <xs:enumeration value="HUNDREDWEIGHT"/>
          <xs:enumeration value="HUNDREDWEIGHT_ALTERNATE"/>
          <xs:enumeration value="INTERNATIONAL_DISTRIBUTION"/>
          <xs:enumeration value="INTERNATIONAL_ECONOMY_SERVICE"/>
          <xs:enumeration value="LTL_FREIGHT"/>
          <xs:enumeration value="PACKAGE"/>
          <xs:enumeration value="SHIPMENT"/>
          <xs:enumeration value="SHIPMENT_FIVE_POUND_OPTIONAL"/>
          <xs:enumeration value="SHIPMENT_OPTIONAL"/>
          <xs:enumeration value="SPECIAL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PrintedReference">
        <xs:annotation>
          <xs:documentation>Represents a reference identifier printed on Freight bills of lading</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:PrintedReferenceType" minOccurs="0"/>
          <xs:element name="Value" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PrintedReferenceType">
        <xs:annotation>
          <xs:documentation>Identifies a particular reference identifier printed on a Freight bill of lading.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONSIGNEE_ID_NUMBER"/>
          <xs:enumeration value="SHIPPER_ID_NUMBER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PriorityAlertDetail">
        <xs:sequence>
          <xs:element name="Content" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ProcessShipmentReply">
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>This indicates the highest level of severity of all the notifications returned in this reply</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="1" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The descriptive data regarding the results of the submitted transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompletedShipmentDetail" type="ns:CompletedShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The reply payload. All of the returned information about this shipment/package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ErrorLabels" type="ns:ShippingDocument" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Empty unless error label behavior is PACKAGE_ERROR_LABELS and one or more errors occured during transaction processing.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ProcessShipmentRequest">
        <xs:annotation>
          <xs:documentation>Descriptive data sent to FedEx by a customer in order to ship a package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestedShipment" type="ns:RequestedShipment" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data about the shipment being sent by the requestor.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ProcessTagReply">
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="1"/>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="1" maxOccurs="unbounded"/>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0"/>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1"/>
          <xs:element name="CompletedShipmentDetail" type="ns:CompletedShipmentDetail" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ProcessTagRequest">
        <xs:annotation>
          <xs:documentation>Descriptive data sent to FedEx by a customer in order to ship a package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestedShipment" type="ns:RequestedShipment" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data about the shipment being sent by the requestor.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PurposeOfShipmentType">
        <xs:annotation>
          <xs:documentation>Test for the Commercial Invoice. Note that Sold is not a valid Purpose for a Proforma Invoice.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="GIFT"/>
          <xs:enumeration value="NOT_SOLD"/>
          <xs:enumeration value="PERSONAL_EFFECTS"/>
          <xs:enumeration value="REPAIR_AND_RETURN"/>
          <xs:enumeration value="SAMPLE"/>
          <xs:enumeration value="SOLD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RateDimensionalDivisorType">
        <xs:annotation>
          <xs:documentation>Indicates the reason that a dim divisor value was chose.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COUNTRY"/>
          <xs:enumeration value="CUSTOMER"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PRODUCT"/>
          <xs:enumeration value="WAIVED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RateDiscount">
        <xs:annotation>
          <xs:documentation>Identifies a discount applied to the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RateDiscountType" type="ns:RateDiscountType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of discount applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The amount of the discount applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Percent" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The percentage of the discount applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RateDiscountType">
        <xs:annotation>
          <xs:documentation>The type of the discount.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BONUS"/>
          <xs:enumeration value="COUPON"/>
          <xs:enumeration value="EARNED"/>
          <xs:enumeration value="INCENTIVE"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="VOLUME"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RateRequestType">
        <xs:annotation>
          <xs:documentation>Identifies the type(s) of rates to be returned in the reply.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCOUNT"/>
          <xs:enumeration value="LIST"/>
          <xs:enumeration value="PREFERRED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RatedWeightMethod">
        <xs:annotation>
          <xs:documentation>The weight method used to calculate the rate.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACTUAL"/>
          <xs:enumeration value="AVERAGE_PACKAGE_WEIGHT_MINIMUM"/>
          <xs:enumeration value="BALLOON"/>
          <xs:enumeration value="DIM"/>
          <xs:enumeration value="FREIGHT_MINIMUM"/>
          <xs:enumeration value="MIXED"/>
          <xs:enumeration value="OVERSIZE"/>
          <xs:enumeration value="OVERSIZE_1"/>
          <xs:enumeration value="OVERSIZE_2"/>
          <xs:enumeration value="OVERSIZE_3"/>
          <xs:enumeration value="PACKAGING_MINIMUM"/>
          <xs:enumeration value="WEIGHT_BREAK"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Rebate">
        <xs:sequence>
          <xs:element name="RebateType" type="ns:RebateType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="0"/>
          <xs:element name="Percent" type="xs:decimal" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RebateType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BONUS"/>
          <xs:enumeration value="EARNED"/>
          <xs:enumeration value="OTHER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RecipientCustomsId">
        <xs:annotation>
          <xs:documentation>Specifies how the recipient is identified for customs purposes; the requirements on this information vary with destination country.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:RecipientCustomsIdType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the kind of identification being used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains the actual ID value, of the type specified above.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RecipientCustomsIdType">
        <xs:annotation>
          <xs:documentation>Type of Brazilian taxpayer identifier provided in Recipient/TaxPayerIdentification/Number. For shipments bound for Brazil this overrides the value in Recipient/TaxPayerIdentification/TinType</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMPANY"/>
          <xs:enumeration value="INDIVIDUAL"/>
          <xs:enumeration value="PASSPORT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RegulatoryControlType">
        <xs:annotation>
          <xs:documentation>FOOD_OR_PERISHABLE is required by FDA/BTA; must be true for food/perishable items coming to US or PR from non-US/non-PR origin</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EU_CIRCULATION"/>
          <xs:enumeration value="FOOD_OR_PERISHABLE"/>
          <xs:enumeration value="NAFTA"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RequestedPackageDetailType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="INDIVIDUAL_PACKAGES"/>
          <xs:enumeration value="PACKAGE_GROUPS"/>
          <xs:enumeration value="PACKAGE_SUMMARY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RequestedPackageLineItem">
        <xs:annotation>
          <xs:documentation>This class rationalizes RequestedPackage and RequestedPackageSummary from previous interfaces. The way in which it is uses within a RequestedShipment depends on the RequestedPackageDetailType value specified for that shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SequenceNumber" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used only with INDIVIDUAL_PACKAGE, as a unique identifier of each requested package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GroupNumber" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used only with PACKAGE_GROUPS, as a unique identifier of each group of identical packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GroupPackageCount" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used only with PACKAGE_GROUPS, as a count of packages within a group of identical packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableHandlingChargeDetail" type="ns:VariableHandlingChargeDetail" minOccurs="0"/>
          <xs:element name="InsuredValue" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used for INDIVIDUAL_PACKAGES and PACKAGE_GROUPS. Ignored for PACKAGE_SUMMARY, in which case totalInsuredValue and packageCount on the shipment will be used to determine this value.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Weight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used for INDIVIDUAL_PACKAGES and PACKAGE_GROUPS. Ignored for PACKAGE_SUMMARY, in which case totalweight and packageCount on the shipment will be used to determine this value.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Dimensions" type="ns:Dimensions" minOccurs="0"/>
          <xs:element name="PhysicalPackaging" type="ns:PhysicalPackagingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Provides additional detail on how the customer has physically packaged this item. As of June 2009, required for packages moving under international and SmartPost services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ItemDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Human-readable text describing the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerReferences" type="ns:CustomerReference" minOccurs="0" maxOccurs="3"/>
          <xs:element name="SpecialServicesRequested" type="ns:PackageSpecialServicesRequested" minOccurs="0"/>
          <xs:element name="ContentRecords" type="ns:ContentRecord" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Only used for INDIVIDUAL_PACKAGES and PACKAGE_GROUPS.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RequestedShipment">
        <xs:annotation>
          <xs:documentation>The descriptive data for the shipment being tendered to FedEx.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ShipTimestamp" type="xs:dateTime" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the date and time the package is tendered to FedEx. Both the date and time portions of the string are expected to be used. The date should not be a past date or a date more than 10 days in the future. The time is the local time of the shipment based on the shipper's time zone. The date component must be in the format: YYYY-MM-DD (e.g. 2006-06-26). The time component must be in the format: HH:MM:SS using a 24 hour clock (e.g. 11:00 a.m. is 11:00:00, whereas 5:00 p.m. is 17:00:00). The date and time parts are separated by the letter T (e.g. 2006-06-26T17:00:00). There is also a UTC offset component indicating the number of hours/mainutes from UTC (e.g 2006-06-26T17:00:00-0400 is defined form June 26, 2006 5:00 pm Eastern Time).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DropoffType" type="ns:DropoffType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the method by which the package is to be tendered to FedEx. This element does not dispatch a courier for package pickup. See DropoffType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ServiceType" type="ns:ServiceType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the FedEx service to use in shipping the package. See ServiceType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackagingType" type="ns:PackagingType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the packaging used by the requestor for the package. See PackagingType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the total weight of the shipment being conveyed to FedEx.This is only applicable to International shipments and should only be used on the first package of a mutiple piece shipment.This value contains 1 explicit decimal position</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalInsuredValue" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total insured amount.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalDimensions" type="ns:Dimensions" minOccurs="0"/>
          <xs:element name="Shipper" type="ns:Party" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the party responsible for shipping the package. Shipper and Origin should have the same address.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Recipient" type="ns:Party" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the party receiving the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RecipientLocationNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A unique identifier for a recipient location</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>10</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Origin" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Physical starting address for the shipment, if different from shipper's address.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShippingChargesPayment" type="ns:Payment" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data indicating the method and means of payment to FedEx for providing shipping services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialServicesRequested" type="ns:ShipmentSpecialServicesRequested" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data regarding special services requested by the shipper for this shipment. If the shipper is requesting a special service which requires additional data (e.g. COD), the special service type must be present in the specialServiceTypes collection, and the supporting detail must be provided in the appropriate sub-object. For example, to request COD, "COD" must be included in the SpecialServiceTypes collection and the CodDetail object must contain the required data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExpressFreightDetail" type="ns:ExpressFreightDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Details specific to an Express freight shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightShipmentDetail" type="ns:FreightShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Data applicable to shipments using FEDEX_FREIGHT and FEDEX_NATIONAL_FREIGHT services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryInstructions" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used with Ground Home Delivery and Freight.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableHandlingChargeDetail" type="ns:VariableHandlingChargeDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Details about how to calculate variable handling charges at the shipment level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomsClearanceDetail" type="ns:CustomsClearanceDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customs clearance data, used for both international and intra-country shipping.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PickupDetail" type="ns:PickupDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For use in "process tag" transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SmartPostDetail" type="ns:SmartPostShipmentDetail" minOccurs="0"/>
          <xs:element name="BlockInsightVisibility" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>If true, only the shipper/payor will have visibility of this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ErrorLabelBehavior" type="ns:ErrorLabelBehaviorType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the client-requested response in the event of errors within shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelSpecification" type="ns:LabelSpecification" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Details about the image format and printer type the label is to returned in.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShippingDocumentSpecification" type="ns:ShippingDocumentSpecification" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains data used to create additional (non-label) shipping documents.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateRequestTypes" type="ns:RateRequestType" minOccurs="1" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies whether and what kind of rates the customer wishes to have quoted on this shipment. The reply will also be constrained by other data on the shipment and customer.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerSelectedActualRateType" type="ns:ReturnedRateType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the type of rate the customer wishes to have used as the actual rate type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EdtRequestType" type="ns:EdtRequestType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies whether the customer wishes to have Estimated Duties and Taxes provided with the rate quotation on this shipment. Only applies with shipments moving under international services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MasterTrackingId" type="ns:TrackingId" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used with multiple-transaction shipments.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodReturnTrackingId" type="ns:TrackingId" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used with multi-piece COD shipments sent in multiple transactions. Required on last transaction only.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageCount" type="xs:nonNegativeInteger" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The total number of packages in the entire shipment (even when the shipment spans multiple transactions.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageDetail" type="ns:RequestedPackageDetailType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies whether packages are described individually, in groups, or summarized in a single description for total-piece-total-weight. This field controls which fields of the RequestedPackageLineItem will be used, and how many occurrences are expected.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestedPackageLineItems" type="ns:RequestedPackageLineItem" minOccurs="0" maxOccurs="999">
            <xs:annotation>
              <xs:documentation>One or more package-attribute descriptions, each of which describes an individual package, a group of identical packages, or (for the total-piece-total-weight case) common characteristics all packages in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RequestedShippingDocumentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="COMMERCIAL_INVOICE"/>
          <xs:enumeration value="CUSTOMER_SPECIFIED_LABELS"/>
          <xs:enumeration value="CUSTOM_PACKAGE_DOCUMENT"/>
          <xs:enumeration value="CUSTOM_SHIPMENT_DOCUMENT"/>
          <xs:enumeration value="FREIGHT_ADDRESS_LABEL"/>
          <xs:enumeration value="GENERAL_AGENCY_AGREEMENT"/>
          <xs:enumeration value="LABEL"/>
          <xs:enumeration value="NAFTA_CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="OP_900"/>
          <xs:enumeration value="PRO_FORMA_INVOICE"/>
          <xs:enumeration value="RETURN_INSTRUCTIONS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ReturnEMailAllowedSpecialServiceType">
        <xs:annotation>
          <xs:documentation>These values are used to control the availability of certain special services at the time when a customer uses the e-mail label link to create a return shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="SATURDAY_DELIVERY"/>
          <xs:enumeration value="SATURDAY_PICKUP"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ReturnEMailDetail">
        <xs:annotation>
          <xs:documentation>Return Email Details</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="MerchantPhoneNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Phone number of the merchant</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AllowedSpecialServices" type="ns:ReturnEMailAllowedSpecialServiceType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Identifies the allowed (merchant-authorized) special services which may be selected when the subsequent shipment is created. Only services represented in EMailLabelAllowedSpecialServiceType will be controlled by this list.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ReturnShipmentDetail">
        <xs:annotation>
          <xs:documentation>Information relating to a return shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ReturnType" type="ns:ReturnType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The type of return shipment that is being requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Rma" type="ns:Rma" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Return Merchant Authorization</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ReturnEMailDetail" type="ns:ReturnEMailDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Describes specific information about the email label for return shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ReturnType">
        <xs:annotation>
          <xs:documentation>The type of return shipment that is being requested.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_TAG"/>
          <xs:enumeration value="PENDING"/>
          <xs:enumeration value="PRINT_RETURN_LABEL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ReturnedRateType">
        <xs:annotation>
          <xs:documentation>The "PAYOR..." rates are expressed in the currency identified in the payor's rate table(s). The "RATED..." rates are expressed in the currency of the origin country. Former "...COUNTER..." values have become "...RETAIL..." values, except for PAYOR_COUNTER and RATED_COUNTER, which have been removed.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="INCENTIVE"/>
          <xs:enumeration value="PAYOR_ACCOUNT_PACKAGE"/>
          <xs:enumeration value="PAYOR_ACCOUNT_SHIPMENT"/>
          <xs:enumeration value="PAYOR_LIST_PACKAGE"/>
          <xs:enumeration value="PAYOR_LIST_SHIPMENT"/>
          <xs:enumeration value="RATED_ACCOUNT_PACKAGE"/>
          <xs:enumeration value="RATED_ACCOUNT_SHIPMENT"/>
          <xs:enumeration value="RATED_LIST_PACKAGE"/>
          <xs:enumeration value="RATED_LIST_SHIPMENT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ReturnedShippingDocumentType">
        <xs:annotation>
          <xs:documentation>Shipping document type.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="AUXILIARY_LABEL"/>
          <xs:enumeration value="CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="COD_RETURN_2_D_BARCODE"/>
          <xs:enumeration value="COD_RETURN_LABEL"/>
          <xs:enumeration value="COMMERCIAL_INVOICE"/>
          <xs:enumeration value="CUSTOM_PACKAGE_DOCUMENT"/>
          <xs:enumeration value="CUSTOM_SHIPMENT_DOCUMENT"/>
          <xs:enumeration value="ETD_LABEL"/>
          <xs:enumeration value="FREIGHT_ADDRESS_LABEL"/>
          <xs:enumeration value="GENERAL_AGENCY_AGREEMENT"/>
          <xs:enumeration value="GROUND_BARCODE"/>
          <xs:enumeration value="NAFTA_CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="OP_900"/>
          <xs:enumeration value="OUTBOUND_2_D_BARCODE"/>
          <xs:enumeration value="OUTBOUND_LABEL"/>
          <xs:enumeration value="PRO_FORMA_INVOICE"/>
          <xs:enumeration value="RECIPIENT_ADDRESS_BARCODE"/>
          <xs:enumeration value="RECIPIENT_POSTAL_BARCODE"/>
          <xs:enumeration value="RETURN_INSTRUCTIONS"/>
          <xs:enumeration value="TERMS_AND_CONDITIONS"/>
          <xs:enumeration value="USPS_BARCODE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Rma">
        <xs:annotation>
          <xs:documentation>Return Merchant Authorization</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Number" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The RMA number.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>20</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Reason" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The reason for the return.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>60</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RoutingAstraDetail">
        <xs:annotation>
          <xs:documentation>The tracking number information and the data to form the Astra barcode for the label.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="TrackingId" type="ns:TrackingId" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The tracking number information for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Barcode" type="ns:StringBarcode" minOccurs="0"/>
          <xs:element name="AstraHandlingText" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The textual description of the special service applied to the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AstraLabelElements" type="ns:AstraLabelElement" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RoutingDetail">
        <xs:annotation>
          <xs:documentation>Information about the routing, origin, destination and delivery of a shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ShipmentRoutingDetail" type="ns:ShipmentRoutingDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The routing information detail for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AstraDetails" type="ns:RoutingAstraDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The tracking number information and the data to form the Astra barcode for the label.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ServiceType">
        <xs:annotation>
          <xs:documentation>Identifies the collection of available FedEx service options.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EUROPE_FIRST_INTERNATIONAL_PRIORITY"/>
          <xs:enumeration value="FEDEX_1_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_2_DAY"/>
          <xs:enumeration value="FEDEX_2_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_3_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_EXPRESS_SAVER"/>
          <xs:enumeration value="FEDEX_GROUND"/>
          <xs:enumeration value="FIRST_OVERNIGHT"/>
          <xs:enumeration value="GROUND_HOME_DELIVERY"/>
          <xs:enumeration value="INTERNATIONAL_ECONOMY"/>
          <xs:enumeration value="INTERNATIONAL_ECONOMY_FREIGHT"/>
          <xs:enumeration value="INTERNATIONAL_FIRST"/>
          <xs:enumeration value="INTERNATIONAL_GROUND"/>
          <xs:enumeration value="INTERNATIONAL_PRIORITY"/>
          <xs:enumeration value="INTERNATIONAL_PRIORITY_FREIGHT"/>
          <xs:enumeration value="PRIORITY_OVERNIGHT"/>
          <xs:enumeration value="SMART_POST"/>
          <xs:enumeration value="STANDARD_OVERNIGHT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentDryIceDetail">
        <xs:annotation>
          <xs:documentation>Shipment-level totals of dry ice data across all packages.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PackageCount" type="xs:nonNegativeInteger" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Total number of packages in the shipment that contain dry ice.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalWeight" type="ns:Weight" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Total shipment dry ice weight for all packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentRateDetail">
        <xs:annotation>
          <xs:documentation>Data for a shipment's total/summary rates, as calculated per a specific rate type. The "total..." fields may differ from the sum of corresponding package data for Multiweight or Express MPS.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RateType" type="ns:ReturnedRateType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Type used for this specific set of rate data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateScale" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the rate scale used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateZone" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the rate zone used (based on origin and destination).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PricingCode" type="ns:PricingCodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of pricing used for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RatedWeightMethod" type="ns:RatedWeightMethod" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates which weight was used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MinimumChargeType" type="ns:MinimumChargeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>INTERNAL FEDEX USE ONLY.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CurrencyExchangeRate" type="ns:CurrencyExchangeRate" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the currency exchange performed on financial amounts for this rate.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialRatingApplied" type="ns:SpecialRatingAppliedType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Indicates which special rating cases applied to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DimDivisor" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The value used to calculate the weight based on the dimensions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DimDivisorType" type="ns:RateDimensionalDivisorType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of dim divisor that was applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FuelSurchargePercent" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies a fuel surcharge percentage.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalBillingWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The weight used to calculate these rates.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalDimWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Sum of dimensional weights for all packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalBaseCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total freight charge that was calculated for this package before surcharges, discounts and taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalFreightDiscounts" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total discounts used in the rate calculation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetFreight" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The freight charge minus discounts.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalSurcharges" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total amount of all surcharges applied to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetFedExCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetFreight + totalSurcharges (not including totalTaxes).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total of the transportation-based taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The net charge after applying all discounts and surcharges.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalRebates" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total sum of all rebates applied to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalDutiesAndTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total of all values under this shipment's dutiesAndTaxes; only provided if estimated duties and taxes were calculated for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetChargeWithDutiesAndTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetCharge + totalDutiesAndTaxes; only provided if estimated duties and taxes were calculated for this shipment AND duties, taxes and transportation charges are all paid by the same sender's account.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightRateDetail" type="ns:FreightRateDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Rate data specific to FedEx Freight and FedEx National Freight services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightDiscounts" type="ns:RateDiscount" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rate discounts that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Rebates" type="ns:Rebate" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rebates that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Surcharges" type="ns:Surcharge" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All surcharges that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Taxes" type="ns:Tax" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All transportation-based taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DutiesAndTaxes" type="ns:EdtCommodityTax" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All commodity-based duties and taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableHandlingCharges" type="ns:VariableHandlingCharges" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The "order level" variable handling charges.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalVariableHandlingCharges" type="ns:VariableHandlingCharges" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total of all variable handling charges at both shipment (order) and package level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentRating">
        <xs:annotation>
          <xs:documentation>This class groups together all shipment-level rate data (across all rate types) as part of the response to a shipping request, which groups shipment-level data together and groups package-level data by package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ActualRateType" type="ns:ReturnedRateType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This rate type identifies which entry in the following array is considered as presenting the "actual" rates for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EffectiveNetDiscount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The "list" total net charge minus "actual" total net charge.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentRateDetails" type="ns:ShipmentRateDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Each element of this field provides shipment-level rate totals for a specific rate type.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentReply">
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>This indicates the highest level of severity of all the notifications returned in this reply</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="1" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The descriptive data regarding the results of the submitted transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentRoutingDetail">
        <xs:annotation>
          <xs:documentation>Information about the routing, origin, destination and delivery of a shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="UrsaPrefixCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The prefix portion of the URSA (Universal Routing and Sort Aid) code.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>2</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="UrsaSuffixCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The suffix portion of the URSA code.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>5</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="OriginLocationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The identifier of the origin location of the shipment. Express only.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>5</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="OriginServiceArea" type="xs:string" minOccurs="0"/>
          <xs:element name="DestinationLocationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The identifier of the destination location of the shipment. Express only.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>5</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationServiceArea" type="xs:string" minOccurs="0"/>
          <xs:element name="DestinationLocationStateOrProvinceCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This is the state of the destination location ID, and is not necessarily the same as the postal state.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Expected/estimated date of delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryDay" type="ns:DayOfWeekType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Expected/estimated day of week of delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CommitDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Committed date of delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CommitDay" type="ns:DayOfWeekType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Committed day of week of delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransitTime" type="ns:TransitTimeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Standard transit time per origin, destination, and service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MaximumTransitTime" type="ns:TransitTimeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Maximum expected transit time</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AstraPlannedServiceLevel" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Text describing planned delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AstraDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Currently not supported.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>TBD</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="PostalCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The postal code of the destination of the shipment.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>16</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="StateOrProvinceCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The state or province code of the destination of the shipment.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>14</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="CountryCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The country code of the destination of the shipment.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>2</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="AirportId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The identifier for the airport of the destination of the shipment.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>4</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShipmentSpecialServiceType">
        <xs:annotation>
          <xs:documentation>Identifies the collection of special service offered by FedEx. BROKER_SELECT_OPTION should be used for Express shipments only.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER_SELECT_OPTION"/>
          <xs:enumeration value="CALL_BEFORE_DELIVERY"/>
          <xs:enumeration value="COD"/>
          <xs:enumeration value="CUSTOM_DELIVERY_WINDOW"/>
          <xs:enumeration value="DANGEROUS_GOODS"/>
          <xs:enumeration value="DO_NOT_BREAK_DOWN_PALLETS"/>
          <xs:enumeration value="DO_NOT_STACK_PALLETS"/>
          <xs:enumeration value="DRY_ICE"/>
          <xs:enumeration value="EAST_COAST_SPECIAL"/>
          <xs:enumeration value="ELECTRONIC_TRADE_DOCUMENTS"/>
          <xs:enumeration value="EMAIL_NOTIFICATION"/>
          <xs:enumeration value="EXTREME_LENGTH"/>
          <xs:enumeration value="FOOD"/>
          <xs:enumeration value="FREIGHT_GUARANTEE"/>
          <xs:enumeration value="FUTURE_DAY_SHIPMENT"/>
          <xs:enumeration value="HOLD_AT_LOCATION"/>
          <xs:enumeration value="HOME_DELIVERY_PREMIUM"/>
          <xs:enumeration value="INSIDE_DELIVERY"/>
          <xs:enumeration value="INSIDE_PICKUP"/>
          <xs:enumeration value="LIFTGATE_DELIVERY"/>
          <xs:enumeration value="LIFTGATE_PICKUP"/>
          <xs:enumeration value="LIMITED_ACCESS_DELIVERY"/>
          <xs:enumeration value="LIMITED_ACCESS_PICKUP"/>
          <xs:enumeration value="PENDING_SHIPMENT"/>
          <xs:enumeration value="POISON"/>
          <xs:enumeration value="PROTECTION_FROM_FREEZING"/>
          <xs:enumeration value="RETURN_SHIPMENT"/>
          <xs:enumeration value="SATURDAY_DELIVERY"/>
          <xs:enumeration value="SATURDAY_PICKUP"/>
          <xs:enumeration value="TOP_LOAD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentSpecialServicesRequested">
        <xs:annotation>
          <xs:documentation>These special services are available at the shipment level for some or all service types. If the shipper is requesting a special service which requires additional data (such as the COD amount), the shipment special service type must be present in the specialServiceTypes collection, and the supporting detail must be provided in the appropriate sub-object below.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SpecialServiceTypes" type="ns:ShipmentSpecialServiceType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The types of all special services requested for the enclosing shipment (or other shipment-level transaction).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodDetail" type="ns:CodDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for a FedEx COD (Collect-On-Delivery) shipment. This element is required when SpecialServiceType.COD is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HoldAtLocationDetail" type="ns:HoldAtLocationDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for a FedEx shipment that is to be held at the destination FedEx location for pickup by the recipient. This element is required when SpecialServiceType.HOLD_AT_LOCATION is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EMailNotificationDetail" type="ns:EMailNotificationDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for FedEx to provide email notification to the customer regarding the shipment. This element is required when SpecialServiceType.EMAIL_NOTIFICATION is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ReturnShipmentDetail" type="ns:ReturnShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data required for FedEx Printed Return Label. This element is required when SpecialServiceType.PRINTED_RETURN_LABEL is present in the SpecialServiceTypes collection</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PendingShipmentDetail" type="ns:PendingShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This field should be populated for pending shipments (e.g. e-mail label) It is required by a PENDING_SHIPMENT special service type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentDryIceDetail" type="ns:ShipmentDryIceDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Number of packages in this shipment which contain dry ice and the total weight of the dry ice for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HomeDeliveryPremiumDetail" type="ns:HomeDeliveryPremiumDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data required for FedEx Home Delivery options. This element is required when SpecialServiceType.HOME_DELIVERY_PREMIUM is present in the SpecialServiceTypes collection</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EtdDetail" type="ns:EtdDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Electronic Trade document references.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomDeliveryWindowDetail" type="ns:CustomDeliveryWindowDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specification for date or range of dates on which delivery is to be attempted.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocument">
        <xs:annotation>
          <xs:documentation>All package-level shipping documents (other than labels and barcodes).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:ReturnedShippingDocumentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Shipping Document Type</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Grouping" type="ns:ShippingDocumentGroupingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how this document image/file is organized.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShippingDocumentDisposition" type="ns:ShippingDocumentDispositionType" minOccurs="0"/>
          <xs:element name="AccessReference" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The name under which a STORED or DEFERRED document is written.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Resolution" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the image resolution in DPI (dots per inch).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CopiesToPrint" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Can be zero for documents whose disposition implies that no content is included.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Parts" type="ns:ShippingDocumentPart" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>One or more document parts which make up a single logical document, such as multiple pages of a single form.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentDispositionDetail">
        <xs:annotation>
          <xs:documentation>Each occurrence of this class specifies a particular way in which a kind of shipping document is to be produced and provided.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="DispositionType" type="ns:ShippingDocumentDispositionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Values in this field specify how to create and return the document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Grouping" type="ns:ShippingDocumentGroupingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how to organize all documents of this type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EMailDetail" type="ns:ShippingDocumentEMailDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how to e-mail document images.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PrintDetail" type="ns:ShippingDocumentPrintDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how a queued document is to be printed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentDispositionType">
        <xs:annotation>
          <xs:documentation>Specifies how to return a shipping document to the caller.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONFIRMED"/>
          <xs:enumeration value="DEFERRED_RETURNED"/>
          <xs:enumeration value="DEFERRED_STORED"/>
          <xs:enumeration value="EMAILED"/>
          <xs:enumeration value="QUEUED"/>
          <xs:enumeration value="RETURNED"/>
          <xs:enumeration value="STORED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShippingDocumentEMailDetail">
        <xs:annotation>
          <xs:documentation>Specifies how to e-mail shipping documents.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="EMailRecipients" type="ns:ShippingDocumentEMailRecipient" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Provides the roles and email addresses for e-mail recipients.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Grouping" type="ns:ShippingDocumentEMailGroupingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the convention by which documents are to be grouped as e-mail attachments.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentEMailGroupingType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BY_RECIPIENT"/>
          <xs:enumeration value="NONE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShippingDocumentEMailRecipient">
        <xs:annotation>
          <xs:documentation>Specifies an individual recipient of e-mailed shipping document(s).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RecipientType" type="ns:EMailNotificationRecipientType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the relationship of this recipient in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Address" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Address to which the document is to be sent.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentFormat">
        <xs:annotation>
          <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Dispositions" type="ns:ShippingDocumentDispositionDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies how to create, organize, and return the document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TopOfPageOffset" type="ns:LinearMeasure" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how far down the page to move the beginning of the image; allows for printing on letterhead and other pre-printed stock.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ImageType" type="ns:ShippingDocumentImageType" minOccurs="0"/>
          <xs:element name="StockType" type="ns:ShippingDocumentStockType" minOccurs="0"/>
          <xs:element name="ProvideInstructions" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For those shipping document types which have both a "form" and "instructions" component (e.g. NAFTA Certificate of Origin and General Agency Agreement), this field indicates whether to provide the instructions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Governs the language to be used for this individual document, independently from other content returned for the same shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomDocumentIdentifier" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the individual document specified by the client.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentGroupingType">
        <xs:annotation>
          <xs:documentation>Specifies how to organize all shipping documents of the same type.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONSOLIDATED_BY_DOCUMENT_TYPE"/>
          <xs:enumeration value="INDIVIDUAL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ShippingDocumentImageType">
        <xs:annotation>
          <xs:documentation>Specifies the image format used for a shipping document.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DOC"/>
          <xs:enumeration value="DPL"/>
          <xs:enumeration value="EPL2"/>
          <xs:enumeration value="PDF"/>
          <xs:enumeration value="PNG"/>
          <xs:enumeration value="RTF"/>
          <xs:enumeration value="TEXT"/>
          <xs:enumeration value="ZPLII"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShippingDocumentPart">
        <xs:annotation>
          <xs:documentation>A single part of a shipping document, such as one page of a multiple-page document whose format requires a separate image per page.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="DocumentPartSequenceNumber" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The one-origin position of this part within a document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Image" type="xs:base64Binary" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Graphic or printer commands for this image within a document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentPrintDetail">
        <xs:annotation>
          <xs:documentation>Specifies printing options for a shipping document.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PrinterId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Provides environment-specific printer identification.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentSpecification">
        <xs:annotation>
          <xs:documentation>Contains all data required for additional (non-label) shipping documents to be produced in conjunction with a specific shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ShippingDocumentTypes" type="ns:RequestedShippingDocumentType" minOccurs="1" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Indicates the types of shipping documents requested by the shipper.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CertificateOfOrigin" type="ns:CertificateOfOriginDetail" minOccurs="0"/>
          <xs:element name="CommercialInvoiceDetail" type="ns:CommercialInvoiceDetail" minOccurs="0"/>
          <xs:element name="CustomPackageDocumentDetail" type="ns:CustomDocumentDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the production of each package-level custom document (the same specification is used for all packages).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomShipmentDocumentDetail" type="ns:CustomDocumentDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the production of a shipment-level custom document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GeneralAgencyAgreementDetail" type="ns:GeneralAgencyAgreementDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This element is currently not supported and is for the future use. (Details pertaining to the GAA.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NaftaCertificateOfOriginDetail" type="ns:NaftaCertificateOfOriginDetail" minOccurs="0"/>
          <xs:element name="Op900Detail" type="ns:Op900Detail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the production of the OP-900 document for hazardous materials packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightAddressLabelDetail" type="ns:FreightAddressLabelDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the production of the OP-900 document for hazardous materials.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentStockType">
        <xs:annotation>
          <xs:documentation>Specifies the type of paper (stock) on which a document will be printed.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="OP_900_LG_B"/>
          <xs:enumeration value="OP_900_LL_B"/>
          <xs:enumeration value="OP_950"/>
          <xs:enumeration value="PAPER_4X6"/>
          <xs:enumeration value="PAPER_LETTER"/>
          <xs:enumeration value="STOCK_4X6"/>
          <xs:enumeration value="STOCK_4X6.75_LEADING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X6.75_TRAILING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X8"/>
          <xs:enumeration value="STOCK_4X9_LEADING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X9_TRAILING_DOC_TAB"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="SignatureOptionDetail">
        <xs:annotation>
          <xs:documentation>The descriptive data required for FedEx delivery signature services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="OptionType" type="ns:SignatureOptionType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the delivery signature services option selected by the customer for this shipment. See OptionType for the list of valid values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureReleaseNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the delivery signature release authorization number.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>10</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SignatureOptionType">
        <xs:annotation>
          <xs:documentation>Identifies the delivery signature services options offered by FedEx.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADULT"/>
          <xs:enumeration value="DIRECT"/>
          <xs:enumeration value="INDIRECT"/>
          <xs:enumeration value="NO_SIGNATURE_REQUIRED"/>
          <xs:enumeration value="SERVICE_DEFAULT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SmartPostAncillaryEndorsementType">
        <xs:annotation>
          <xs:documentation>These values are mutually exclusive; at most one of them can be attached to a SmartPost shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDRESS_CORRECTION"/>
          <xs:enumeration value="CARRIER_LEAVE_IF_NO_RESPONSE"/>
          <xs:enumeration value="CHANGE_SERVICE"/>
          <xs:enumeration value="FORWARDING_SERVICE"/>
          <xs:enumeration value="RETURN_SERVICE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SmartPostIndiciaType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="MEDIA_MAIL"/>
          <xs:enumeration value="PARCEL_RETURN"/>
          <xs:enumeration value="PARCEL_SELECT"/>
          <xs:enumeration value="PRESORTED_BOUND_PRINTED_MATTER"/>
          <xs:enumeration value="PRESORTED_STANDARD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="SmartPostShipmentDetail">
        <xs:annotation>
          <xs:documentation>Data required for shipments handled under the SMART_POST and GROUND_SMART_POST service types.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Indicia" type="ns:SmartPostIndiciaType" minOccurs="0"/>
          <xs:element name="AncillaryEndorsement" type="ns:SmartPostAncillaryEndorsementType" minOccurs="0"/>
          <xs:element name="HubId" type="xs:string" minOccurs="0"/>
          <xs:element name="CustomerManifestId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                The CustomerManifestId is used to group Smart Post packages onto a manifest for each trailer that is being prepared. If you do not have multiple trailers this field can be omitted. If you have multiple trailers, you
                must assign the same Manifest Id to each SmartPost package as determined by its trailer.  In other words, all packages on a trailer must have the same Customer Manifest Id. The manifest Id must be unique to your account number for a minimum of 6 months
                and cannot exceed 8 characters in length. We recommend you use the day of year + the trailer id (this could simply be a sequential number for that trailer). So if you had 3 trailers that you started loading on Feb 10
                the 3 manifest ids would be 041001, 041002, 041003 (in this case we used leading zeros on the trailer numbers).
              </xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SpecialRatingAppliedType">
        <xs:annotation>
          <xs:documentation>Special circumstance rating used for this shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FIXED_FUEL_SURCHARGE"/>
          <xs:enumeration value="IMPORT_PRICING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="StringBarcode">
        <xs:annotation>
          <xs:documentation>Each instance of this data type represents a barcode whose content must be represented as ASCII text (i.e. not binary data).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:StringBarcodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The kind of barcode data in this instance.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The data content of this instance.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="StringBarcodeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDRESS"/>
          <xs:enumeration value="ASTRA"/>
          <xs:enumeration value="FDX_1D"/>
          <xs:enumeration value="GROUND"/>
          <xs:enumeration value="POSTAL"/>
          <xs:enumeration value="USPS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Surcharge">
        <xs:annotation>
          <xs:documentation>Identifies each surcharge applied to the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SurchargeType" type="ns:SurchargeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The type of surcharge applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Level" type="ns:SurchargeLevelType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The amount of the surcharge applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SurchargeLevelType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PACKAGE"/>
          <xs:enumeration value="SHIPMENT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SurchargeType">
        <xs:annotation>
          <xs:documentation>The type of the surcharge.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDITIONAL_HANDLING"/>
          <xs:enumeration value="ANCILLARY_FEE"/>
          <xs:enumeration value="APPOINTMENT_DELIVERY"/>
          <xs:enumeration value="BROKER_SELECT_OPTION"/>
          <xs:enumeration value="CANADIAN_DESTINATION"/>
          <xs:enumeration value="CLEARANCE_ENTRY_FEE"/>
          <xs:enumeration value="COD"/>
          <xs:enumeration value="CUT_FLOWERS"/>
          <xs:enumeration value="DANGEROUS_GOODS"/>
          <xs:enumeration value="DELIVERY_AREA"/>
          <xs:enumeration value="DELIVERY_CONFIRMATION"/>
          <xs:enumeration value="DOCUMENTATION_FEE"/>
          <xs:enumeration value="DRY_ICE"/>
          <xs:enumeration value="EMAIL_LABEL"/>
          <xs:enumeration value="EUROPE_FIRST"/>
          <xs:enumeration value="EXCESS_VALUE"/>
          <xs:enumeration value="EXHIBITION"/>
          <xs:enumeration value="EXPORT"/>
          <xs:enumeration value="EXTREME_LENGTH"/>
          <xs:enumeration value="FEDEX_TAG"/>
          <xs:enumeration value="FICE"/>
          <xs:enumeration value="FLATBED"/>
          <xs:enumeration value="FREIGHT_GUARANTEE"/>
          <xs:enumeration value="FREIGHT_ON_VALUE"/>
          <xs:enumeration value="FUEL"/>
          <xs:enumeration value="HOLD_AT_LOCATION"/>
          <xs:enumeration value="HOME_DELIVERY_APPOINTMENT"/>
          <xs:enumeration value="HOME_DELIVERY_DATE_CERTAIN"/>
          <xs:enumeration value="HOME_DELIVERY_EVENING"/>
          <xs:enumeration value="INSIDE_DELIVERY"/>
          <xs:enumeration value="INSIDE_PICKUP"/>
          <xs:enumeration value="INSURED_VALUE"/>
          <xs:enumeration value="INTERHAWAII"/>
          <xs:enumeration value="LIFTGATE_DELIVERY"/>
          <xs:enumeration value="LIFTGATE_PICKUP"/>
          <xs:enumeration value="LIMITED_ACCESS_DELIVERY"/>
          <xs:enumeration value="LIMITED_ACCESS_PICKUP"/>
          <xs:enumeration value="METRO_DELIVERY"/>
          <xs:enumeration value="METRO_PICKUP"/>
          <xs:enumeration value="NON_MACHINABLE"/>
          <xs:enumeration value="OFFSHORE"/>
          <xs:enumeration value="ON_CALL_PICKUP"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="OUT_OF_DELIVERY_AREA"/>
          <xs:enumeration value="OUT_OF_PICKUP_AREA"/>
          <xs:enumeration value="OVERSIZE"/>
          <xs:enumeration value="OVER_DIMENSION"/>
          <xs:enumeration value="PIECE_COUNT_VERIFICATION"/>
          <xs:enumeration value="PRE_DELIVERY_NOTIFICATION"/>
          <xs:enumeration value="PRIORITY_ALERT"/>
          <xs:enumeration value="PROTECTION_FROM_FREEZING"/>
          <xs:enumeration value="REGIONAL_MALL_DELIVERY"/>
          <xs:enumeration value="REGIONAL_MALL_PICKUP"/>
          <xs:enumeration value="RESIDENTIAL_DELIVERY"/>
          <xs:enumeration value="RESIDENTIAL_PICKUP"/>
          <xs:enumeration value="RETURN_LABEL"/>
          <xs:enumeration value="SATURDAY_DELIVERY"/>
          <xs:enumeration value="SATURDAY_PICKUP"/>
          <xs:enumeration value="SIGNATURE_OPTION"/>
          <xs:enumeration value="TARP"/>
          <xs:enumeration value="THIRD_PARTY_CONSIGNEE"/>
          <xs:enumeration value="TRANSMART_SERVICE_FEE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Tax">
        <xs:annotation>
          <xs:documentation>Identifies each tax applied to the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="TaxType" type="ns:TaxType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The type of tax applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The amount of the tax applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TaxType">
        <xs:annotation>
          <xs:documentation>The type of the tax.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EXPORT"/>
          <xs:enumeration value="GST"/>
          <xs:enumeration value="HST"/>
          <xs:enumeration value="INTRACOUNTRY"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PST"/>
          <xs:enumeration value="VAT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TaxpayerIdentification">
        <xs:annotation>
          <xs:documentation>The descriptive data for taxpayer identification information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="TinType" type="ns:TinType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the category of the taxpayer identification number. See TinType for the list of values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Number" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the taxpayer identification number.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>15</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Usage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the usage of Tax Identification Number in Shipment processing</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TermsOfSaleType">
        <xs:annotation>
          <xs:documentation>
            Required for dutiable international express or ground shipment. This field is not applicable to an international PIB (document) or a non-document which does not require a commercial invoice express shipment.
            CFR_OR_CPT (Cost and Freight/Carriage Paid TO)
            CIF_OR_CIP (Cost Insurance and Freight/Carraige Insurance Paid)
            DDP (Delivered Duty Paid)
            DDU (Delivered Duty Unpaid)
            EXW (Ex Works)
            FOB_OR_FCA (Free On Board/Free Carrier)
          </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CFR_OR_CPT"/>
          <xs:enumeration value="CIF_OR_CIP"/>
          <xs:enumeration value="DDP"/>
          <xs:enumeration value="DDU"/>
          <xs:enumeration value="EXW"/>
          <xs:enumeration value="FOB_OR_FCA"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TinType">
        <xs:annotation>
          <xs:documentation>Identifies the category of the taxpayer identification number.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BUSINESS_NATIONAL"/>
          <xs:enumeration value="BUSINESS_STATE"/>
          <xs:enumeration value="PERSONAL_NATIONAL"/>
          <xs:enumeration value="PERSONAL_STATE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackingId">
        <xs:sequence>
          <xs:element name="TrackingIdType" type="ns:TrackingIdType" minOccurs="0"/>
          <xs:element name="FormId" type="xs:string" minOccurs="0"/>
          <xs:element name="UspsApplicationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For use with SmartPost tracking IDs only</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TrackingNumber" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackingIdType">
        <xs:annotation>
          <xs:documentation>TrackingIdType</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EXPRESS"/>
          <xs:enumeration value="FREIGHT"/>
          <xs:enumeration value="GROUND"/>
          <xs:enumeration value="USPS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TransactionDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="CustomerTransactionId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free form text to be echoed back in the reply. Used to match requests and replies.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Governs data payload language/translations (contrasted with ClientDetail.localization, which governs Notification.localizedMessage language selection).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TransitTimeType">
        <xs:annotation>
          <xs:documentation>Identifies the set of valid shipment transit time values.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EIGHTEEN_DAYS"/>
          <xs:enumeration value="EIGHT_DAYS"/>
          <xs:enumeration value="ELEVEN_DAYS"/>
          <xs:enumeration value="FIFTEEN_DAYS"/>
          <xs:enumeration value="FIVE_DAYS"/>
          <xs:enumeration value="FOURTEEN_DAYS"/>
          <xs:enumeration value="FOUR_DAYS"/>
          <xs:enumeration value="NINETEEN_DAYS"/>
          <xs:enumeration value="NINE_DAYS"/>
          <xs:enumeration value="ONE_DAY"/>
          <xs:enumeration value="SEVENTEEN_DAYS"/>
          <xs:enumeration value="SEVEN_DAYS"/>
          <xs:enumeration value="SIXTEEN_DAYS"/>
          <xs:enumeration value="SIX_DAYS"/>
          <xs:enumeration value="TEN_DAYS"/>
          <xs:enumeration value="THIRTEEN_DAYS"/>
          <xs:enumeration value="THREE_DAYS"/>
          <xs:enumeration value="TWELVE_DAYS"/>
          <xs:enumeration value="TWENTY_DAYS"/>
          <xs:enumeration value="TWO_DAYS"/>
          <xs:enumeration value="UNKNOWN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="UploadDocumentIdProducer">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMER"/>
          <xs:enumeration value="FEDEX_CSHP"/>
          <xs:enumeration value="FEDEX_GTM"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="UploadDocumentProducerType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMER"/>
          <xs:enumeration value="FEDEX_CLS"/>
          <xs:enumeration value="FEDEX_GTM"/>
          <xs:enumeration value="OTHER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="UploadDocumentReferenceDetail">
        <xs:sequence>
          <xs:element name="LineNumber" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="CustomerReference" type="xs:string" minOccurs="0"/>
          <xs:element name="DocumentProducer" type="ns:UploadDocumentProducerType" minOccurs="0"/>
          <xs:element name="DocumentType" type="ns:UploadDocumentType" minOccurs="0"/>
          <xs:element name="DocumentId" type="xs:string" minOccurs="0"/>
          <xs:element name="DocumentIdProducer" type="ns:UploadDocumentIdProducer" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="UploadDocumentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="COMMERCIAL_INVOICE"/>
          <xs:enumeration value="ETD_LABEL"/>
          <xs:enumeration value="NAFTA_CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PRO_FORMA_INVOICE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ValidateShipmentRequest">
        <xs:annotation>
          <xs:documentation>Descriptive data sent to FedEx by a customer in order to validate a shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestedShipment" type="ns:RequestedShipment" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data about the shipment being sent by the requestor.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ValidatedHazardousCommodityContent">
        <xs:annotation>
          <xs:documentation>Documents the kind and quantity of an individual hazardous commodity in a package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Description" type="ns:ValidatedHazardousCommodityDescription" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies and describes an individual hazardous commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Quantity" type="ns:HazardousCommodityQuantityDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the amount of the commodity in alternate units.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Options" type="ns:HazardousCommodityOptionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-provided specifications for handling individual commodities.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ValidatedHazardousCommodityDescription">
        <xs:annotation>
          <xs:documentation>Identifies and describes an individual hazardous commodity. For 201001 load, this is based on data from the FedEx Ground Hazardous Materials Shipping Guide.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Id" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Regulatory identifier for a commodity (e.g. "UN ID" value).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackingGroup" type="ns:HazardousCommodityPackingGroupType" minOccurs="0"/>
          <xs:element name="ProperShippingName" type="xs:string" minOccurs="0"/>
          <xs:element name="ProperShippingNameAndDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Fully-expanded descriptive text for a hazardous commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TechnicalName" type="xs:string" minOccurs="0"/>
          <xs:element name="HazardClass" type="xs:string" minOccurs="0"/>
          <xs:element name="SubsidiaryClasses" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="Symbols" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Coded indications for special requirements or constraints.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelText" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="VariableHandlingChargeDetail">
        <xs:annotation>
          <xs:documentation>Details about how to calculate variable handling charges at the shipment level.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="VariableHandlingChargeType" type="ns:VariableHandlingChargeType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The type of handling charge to be calculated and returned in the reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FixedValue" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Used with Variable handling charge type of FIXED_VALUE.
                Contains the amount to be added to the freight charge.
                Contains 2 explicit decimal positions with a total max length of 10 including the decimal.
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PercentValue" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Actual percentage (10 means 10%, which is a mutiplier of 0.1)</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="VariableHandlingChargeType">
        <xs:annotation>
          <xs:documentation>The type of handling charge to be calculated and returned in the reply.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FIXED_AMOUNT"/>
          <xs:enumeration value="PERCENTAGE_OF_NET_CHARGE"/>
          <xs:enumeration value="PERCENTAGE_OF_NET_CHARGE_EXCLUDING_TAXES"/>
          <xs:enumeration value="PERCENTAGE_OF_NET_FREIGHT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="VariableHandlingCharges">
        <xs:annotation>
          <xs:documentation>The variable handling charges calculated based on the type variable handling charges requested.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="VariableHandlingCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The variable handling charge amount calculated based on the requested variable handling charge detail.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalCustomerCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The calculated varibale handling charge plus the net charge.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Volume">
        <xs:annotation>
          <xs:documentation>Three-dimensional volume/cubic measurement.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Units" type="ns:VolumeUnits" minOccurs="0"/>
          <xs:element name="Value" type="xs:decimal" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="VolumeUnits">
        <xs:annotation>
          <xs:documentation>Units of three-dimensional volume/cubic measure.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUBIC_FT"/>
          <xs:enumeration value="CUBIC_M"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Weight">
        <xs:annotation>
          <xs:documentation>The descriptive data for the heaviness of an object.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Units" type="ns:WeightUnits" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the unit of measure associated with a weight value.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:decimal" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the weight value of a package/shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="WeightUnits">
        <xs:annotation>
          <xs:documentation>Identifies the unit of measure associated with a weight value. See the list of enumerated types for valid values.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="KG"/>
          <xs:enumeration value="LB"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="WebAuthenticationDetail">
        <xs:annotation>
          <xs:documentation>Used in authentication of the sender's identity.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="UserCredential" type="ns:WebAuthenticationCredential" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Credential used to authenticate a specific software application. This value is provided by FedEx after registration.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="WebAuthenticationCredential">
        <xs:annotation>
          <xs:documentation>Two part authentication string used for the sender's identity</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Key" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifying part of authentication credential. This value is provided by FedEx after registration</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Password" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Secret part of authentication key. This value is provided by FedEx after registration.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="VersionId">
        <xs:annotation>
          <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ServiceId" type="xs:string" fixed="ship" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies a system or sub-system which performs an operation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Major" type="xs:int" fixed="10" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the service business level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Intermediate" type="xs:int" fixed="0" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the service interface level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Minor" type="xs:int" fixed="0" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the service code level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </types>
  <message name="ProcessShipmentReply">
    <part name="ProcessShipmentReply" element="ns:ProcessShipmentReply"/>
  </message>
  <message name="DeleteTagRequest">
    <part name="DeleteTagRequest" element="ns:DeleteTagRequest"/>
  </message>
  <message name="ProcessShipmentRequest">
    <part name="ProcessShipmentRequest" element="ns:ProcessShipmentRequest"/>
  </message>
  <message name="CreatePendingShipmentRequest">
    <part name="CreatePendingShipmentRequest" element="ns:CreatePendingShipmentRequest"/>
  </message>
  <message name="ProcessTagRequest">
    <part name="ProcessTagRequest" element="ns:ProcessTagRequest"/>
  </message>
  <message name="CancelPendingShipmentReply">
    <part name="CancelPendingShipmentReply" element="ns:CancelPendingShipmentReply"/>
  </message>
  <message name="CancelPendingShipmentRequest">
    <part name="CancelPendingShipmentRequest" element="ns:CancelPendingShipmentRequest"/>
  </message>
  <message name="DeleteShipmentRequest">
    <part name="DeleteShipmentRequest" element="ns:DeleteShipmentRequest"/>
  </message>
  <message name="ShipmentReply">
    <part name="ShipmentReply" element="ns:ShipmentReply"/>
  </message>
  <message name="ProcessTagReply">
    <part name="ProcessTagReply" element="ns:ProcessTagReply"/>
  </message>
  <message name="ValidateShipmentRequest">
    <part name="ValidateShipmentRequest" element="ns:ValidateShipmentRequest"/>
  </message>
  <message name="CreatePendingShipmentReply">
    <part name="CreatePendingShipmentReply" element="ns:CreatePendingShipmentReply"/>
  </message>
  <portType name="ShipPortType">
    <operation name="processTag" parameterOrder="ProcessTagRequest">
      <input message="ns:ProcessTagRequest"/>
      <output message="ns:ProcessTagReply"/>
    </operation>
    <operation name="createPendingShipment" parameterOrder="CreatePendingShipmentRequest">
      <input message="ns:CreatePendingShipmentRequest"/>
      <output message="ns:CreatePendingShipmentReply"/>
    </operation>
    <operation name="cancelPendingShipment" parameterOrder="CancelPendingShipmentRequest">
      <input message="ns:CancelPendingShipmentRequest"/>
      <output message="ns:CancelPendingShipmentReply"/>
    </operation>
    <operation name="processShipment" parameterOrder="ProcessShipmentRequest">
      <input message="ns:ProcessShipmentRequest"/>
      <output message="ns:ProcessShipmentReply"/>
    </operation>
    <operation name="deleteTag" parameterOrder="DeleteTagRequest">
      <input message="ns:DeleteTagRequest"/>
      <output message="ns:ShipmentReply"/>
    </operation>
    <operation name="validateShipment" parameterOrder="ValidateShipmentRequest">
      <input message="ns:ValidateShipmentRequest"/>
      <output message="ns:ShipmentReply"/>
    </operation>
    <operation name="deleteShipment" parameterOrder="DeleteShipmentRequest">
      <input message="ns:DeleteShipmentRequest"/>
      <output message="ns:ShipmentReply"/>
    </operation>
  </portType>
  <binding name="ShipServiceSoapBinding" type="ns:ShipPortType">
    <s1:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="processTag">
      <s1:operation soapAction="processTag" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
    <operation name="createPendingShipment">
      <s1:operation soapAction="createPendingShipment" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
    <operation name="cancelPendingShipment">
      <s1:operation soapAction="cancelPendingShipment" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
    <operation name="processShipment">
      <s1:operation soapAction="processShipment" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
    <operation name="deleteTag">
      <s1:operation soapAction="deleteTag" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
    <operation name="validateShipment">
      <s1:operation soapAction="validateShipment" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
    <operation name="deleteShipment">
      <s1:operation soapAction="deleteShipment" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="ShipService">
    <port name="ShipServicePort" binding="ns:ShipServiceSoapBinding">
      <s1:address location="https://wsbeta.fedex.com:443/web-services/ship"/>
    </port>
  </service>
</definitions>
