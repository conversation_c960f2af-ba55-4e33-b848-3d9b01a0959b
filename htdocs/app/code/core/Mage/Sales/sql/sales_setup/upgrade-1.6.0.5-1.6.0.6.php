<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Sales
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

/* @var $installer Mage_Sales_Model_Entity_Setup */
$installer = $this;

$entitiesToAlter = array(
    'quote_address',
    'order_address'
);

$attributes = array(
    'vat_id' => array('type' => Varien_Db_Ddl_Table::TYPE_TEXT),
    'vat_is_valid' => array('type' => Varien_Db_Ddl_Table::TYPE_SMALLINT),
    'vat_request_id' => array('type' => Varien_Db_Ddl_Table::TYPE_TEXT),
    'vat_request_date' => array('type' => Varien_Db_Ddl_Table::TYPE_TEXT),
    'vat_request_success' => array('type' => Varien_Db_Ddl_Table::TYPE_SMALLINT)
);

foreach ($entitiesToAlter as $entityName) {
    foreach ($attributes as $attributeCode => $attributeParams) {
        $installer->addAttribute($entityName, $attributeCode, $attributeParams);
    }
}
