<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Log
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_Log>
            <version>1.6.1.1</version>
        </Mage_Log>
    </modules>
    <global>
        <ignoredModules>
            <entities>
                <install/>
                <adminhtml/>
                <admin/>
            </entities>
        </ignoredModules>
        <ignore_user_agents>
            <google1>Googlebot/1.0 (<EMAIL> http://googlebot.com/)</google1>
            <google2>Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)</google2>
            <google3>Googlebot/2.1 (+http://www.googlebot.com/bot.html)</google3>
        </ignore_user_agents>
        <helpers>
            <log>
                <class>Mage_Log_Helper</class>
            </log>
        </helpers>
        <models>
            <log>
                <class>Mage_Log_Model</class>
                <resourceModel>log_resource</resourceModel>
            </log>
            <log_resource>
                <class>Mage_Log_Model_Resource</class>
                <deprecatedNode>log_mysql4</deprecatedNode>
                <entities>
                    <customer>
                        <table>log_customer</table>
                    </customer>
                    <visitor>
                        <table>log_visitor</table>
                    </visitor>
                    <visitor_info>
                        <table>log_visitor_info</table>
                    </visitor_info>
                    <url_table>
                        <table>log_url</table>
                    </url_table>
                    <url_info_table>
                        <table>log_url_info</table>
                    </url_info_table>
                    <summary_table>
                        <table>log_summary</table>
                    </summary_table>
                    <summary_type_table>
                        <table>log_summary_type</table>
                    </summary_type_table>
                    <quote_table>
                        <table>log_quote</table>
                    </quote_table>
                    <visitor_online>
                        <table>log_visitor_online</table>
                    </visitor_online>
                </entities>
            </log_resource>
        </models>
        <resources>
            <log_setup>
                <setup>
                    <module>Mage_Log</module>
                </setup>
            </log_setup>
        </resources>
        <template>
            <email>
                <system_log_error_email_template translate="label" module="log">
                    <label>Log cleanup Warnings</label>
                    <file>log_clean_warning.html</file>
                    <type>text</type>
                </system_log_error_email_template>
            </email>
        </template>
    </global>
    <frontend>
        <events>
            <controller_action_predispatch>
                <observers>
                    <log>
                        <class>log/visitor</class>
                        <method>initByRequest</method>
                    </log>
                </observers>
            </controller_action_predispatch>
            <controller_action_postdispatch>
                <observers>
                    <log>
                        <class>log/visitor</class>
                        <method>saveByRequest</method>
                    </log>
                </observers>
            </controller_action_postdispatch>
            <customer_login>
                <observers>
                    <log>
                        <class>log/visitor</class>
                        <method>bindCustomerLogin</method>
                    </log>
                </observers>
            </customer_login>
            <customer_logout>
                <observers>
                    <log>
                        <class>log/visitor</class>
                        <method>bindCustomerLogout</method>
                    </log>
                </observers>
            </customer_logout>
            <sales_quote_save_after>
                <observers>
                    <log>
                        <class>log/visitor</class>
                        <method>bindQuoteCreate</method>
                    </log>
                </observers>
            </sales_quote_save_after>
            <checkout_quote_destroy>
                <observers>
                    <log>
                        <class>log/visitor</class>
                        <method>bindQuoteDestroy</method>
                    </log>
                </observers>
            </checkout_quote_destroy>
        </events>
    </frontend>
    <adminhtml>
        <translate>
            <modules>
                <Mage_Log>
                    <files>
                        <default>Mage_Log.csv</default>
                    </files>
                </Mage_Log>
            </modules>
        </translate>
    </adminhtml>
    <default>
        <log>
            <visitor>
                <online_update_frequency>60</online_update_frequency>
            </visitor>
        </log>
        <system>
            <log>
                <enable_log>2</enable_log>
                <clean_after_day>180</clean_after_day>
                <enabled>1</enabled>
                <time/>
                <frequency>D</frequency>
                <error_email/>
                <error_email_identity>general</error_email_identity>
                <error_email_template>system_log_error_email_template</error_email_template>
            </log>
        </system>
    </default>
    <crontab>
        <jobs>
            <log_clean>
                <run>
                    <model>log/cron::logClean</model>
                </run>
            </log_clean>
        </jobs>
    </crontab>
</config>
