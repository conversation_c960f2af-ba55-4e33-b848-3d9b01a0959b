<?php
class PFG_AltCurrency_Model_Observer
{
    protected $templates = [
        [
            // Match escaped HTML with <sup> tags: \<span class="price">6,\<sup>12\</sup> лв.\</span>
            'template' => '/(\\\\<span class="price">(\d+),\\\\<sup>(\d+)\\\\<\/sup>(?:&nbsp;|\s)*лв\.\\\\<\/span>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '{{original_price_span}}{{converted_price_span}}',
        ],
        [
            // Match normal HTML with <sup> tags: <span class="price">6,<sup>12</sup> лв.</span>
            'template' => '/(<span class="price">(\d+),<sup>(\d+)<\/sup>(?:&nbsp;|\s)*лв\.<\/span>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '{{original_price_span}}{{converted_price_span}}',
        ],
        [
            // Fallback: Match escaped HTML without <sup> tags
            'template' => '/(\\\\<span class="price">(\d+),(\d+)(?:&nbsp;|\s)*лв\.\\\\<\/span>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '{{original_price_span}}{{converted_price_span}}',
        ],
        [
            // Fallback: Match normal HTML without <sup> tags
            'template' => '/(<span class="price">(\d+),(\d+)(?:&nbsp;|\s)*лв\.<\/span>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '{{original_price_span}}{{converted_price_span}}',
        ],
    ];

    public function appendConvertedPrice(Varien_Event_Observer $observer)
    {
        Mage::log("==== Observer triggered at " . date('Y-m-d H:i:s') . " (UPDATED VERSION) ====", null, 'altcurrency_debug.log', true);

        try {
            $transport = $observer->getTransport();
            if (!$transport) {
                Mage::log("❌ Transport is missing.", null, 'altcurrency_debug.log', true);
                return;
            }

            $originalHtmlFromTransport = $transport->getHtml();
            $html = html_entity_decode($originalHtmlFromTransport, ENT_QUOTES, 'UTF-8');

            // More aggressive cleanup for escaped HTML
            $html = str_replace(['\\<', '\\>'], ['<', '>'], $html);
            $html = str_replace(['\\\\<', '\\\\>'], ['<', '>'], $html);

            // Log the transformation for debugging
            Mage::log("🔧 Original HTML: " . substr($originalHtmlFromTransport, 0, 200), null, 'altcurrency_debug.log', true);
            Mage::log("🔧 After preprocessing: " . substr($html, 0, 200), null, 'altcurrency_debug.log', true);

            if ($originalHtmlFromTransport !== $html) {
                Mage::log("🔧 HTML preprocessing applied. Escaped characters were converted.", null, 'altcurrency_debug.log', true);
            } else {
                Mage::log("🔧 No HTML preprocessing needed.", null, 'altcurrency_debug.log', true);
            }

            if (!$html) {
                Mage::log("❌ HTML is empty.", null, 'altcurrency_debug.log', true);
                return;
            }

            if (strpos($html, 'alt-currency') !== false) {
                Mage::log("⚠️ alt-currency tag already present. Skipping.", null, 'altcurrency_debug.log', true);
                return;
            }

            if (strlen($html) < 10) {
                Mage::log("⚠️ HTML too short. Skipping.", null, 'altcurrency_debug.log', true);
                return;
            }

            // Only process HTML that contains price-related elements
            if (strpos($html, 'regular-price') === false && strpos($html, 'product-price') === false) {
                Mage::log("⚠️ HTML doesn't contain price elements. Skipping.", null, 'altcurrency_debug.log', true);
                return;
            }

            $rate = 1.95583;
            $symbol = '€';

            $originalHtml = $html;

            // Log a sample of the HTML to help debug the structure
            $htmlSample = substr($html, 0, 1000);
            Mage::log("📄 HTML sample (first 1000 chars): " . $htmlSample, null, 'altcurrency_debug.log', true);

            // Check if HTML contains price-box structure
            if (strpos($html, 'price-box') !== false) {
                Mage::log("✅ HTML contains price-box structure", null, 'altcurrency_debug.log', true);
            } else {
                Mage::log("⚠️ HTML does NOT contain price-box structure", null, 'altcurrency_debug.log', true);
            }

            // Try each template until one matches
            foreach ($this->templates as $templateIndex => $template) {
                Mage::log("✅ Trying template #$templateIndex: " . $template['template'], null, 'altcurrency_debug.log', true);

                $tempHtml = preg_replace_callback($template['template'], function ($matches) use ($rate, $symbol, $template, $templateIndex) {
                    Mage::log("🔄 Match found with template #$templateIndex!", null, 'altcurrency_debug.log', true);
                    Mage::log(print_r($matches, true), null, 'altcurrency_debug.log', true);

                    $originalPriceSpan = $matches[1];
                    $majorPart = $matches[2];
                    $minorPart = $matches[3];

                    $priceBGN = floatval($majorPart . '.' . $minorPart);
                    $priceEUR = $priceBGN / $rate;

                    // Format EUR price with same structure as original (with <sup> tags)
                    $eurParts = explode('.', number_format($priceEUR, 2, '.', ''));
                    $eurMajor = $eurParts[0];
                    $eurMinor = $eurParts[1];

                    // Check if original uses escaped HTML and match the format
                    if (strpos($originalPriceSpan, '\\<') !== false) {
                        // Use escaped format
                        $convertedPriceSpan = '\\<span class="price">' . $eurMajor . ',\\<sup>' . $eurMinor . '\\</sup>&nbsp;€\\</span>';
                    } else {
                        // Use normal format
                        $convertedPriceSpan = '<span class="price">' . $eurMajor . ',<sup>' . $eurMinor . '</sup>&nbsp;€</span>';
                    }

                    Mage::log("💰 BGN: $priceBGN, EUR: $priceEUR, Original: $originalPriceSpan, Converted: $convertedPriceSpan", null, 'altcurrency_debug.log', true);

                    // Build the result: original + converted
                    $result = $template['converted_template'];
                    $result = str_replace('{{original_price_span}}', $originalPriceSpan, $result);
                    $result = str_replace('{{converted_price_span}}', $convertedPriceSpan, $result);

                    Mage::log("🧱 Final result: " . $result, null, 'altcurrency_debug.log', true);

                    return $result;
                }, $html);

                if ($tempHtml !== $html) {
                    Mage::log("✅ HTML successfully converted with template #$templateIndex.", null, 'altcurrency_debug.log', true);
                    $html = $tempHtml;
                    break; // Exit the loop once we find a match
                } else {
                    Mage::log("⚠️ Template #$templateIndex: No matches found.", null, 'altcurrency_debug.log', true);
                }
            }

            if ($html === $originalHtml) {
                Mage::log("⚠️ No templates matched — HTML unchanged.", null, 'altcurrency_debug.log', true);
            }

            $transport->setHtml($html);

        } catch (Exception $e) {
            Mage::log('❌ Exception in appendConvertedPrice: ' . $e->getMessage(), null, 'altcurrency_error.log', true);
        }

        Mage::log("==== Observer completed ====\n", null, 'altcurrency_debug.log', true);
    }
}

