var RevSliderSettings,colorPicker;!function(e){RevSliderSettings=new function(){var t={},n=this;this.getSettingsObject=function(t){for(var n,i,o,c,s=new Object,a=document.getElementById(t),r=a.elements.length,d=0;d<r;d++){var l=a.elements[d];if("##NAME##[]"!=l.name&&!e(l).hasClass("rs-ingore-save")){switch(n=l.name,i=l.value,o=l.type,e(l).hasClass("wp-editor-area")&&(o="editor"),c=!0,o){case"checkbox":if(void 0!==a.elements[d].getAttribute("data-useval")&&null!==a.elements[d].getAttribute("data-useval")){if(!a.elements[d].checked)continue;i=a.elements[d].value}else i=void 0!==a.elements[d].getAttribute("data-unchecked")&&null!==a.elements[d].getAttribute("data-unchecked")?a.elements[d].checked?i:a.elements[d].getAttribute("data-unchecked"):a.elements[d].checked;break;case"radio":0==a.elements[d].checked&&(c=!1);break;case"editor":"undefined"!=typeof tiynMCE&&null!=tinyMCE.get(n)&&(i=tinyMCE.get(n).getContent());break;case"select-multiple":(i=e(l).val())&&(i=i.toString())}1==c&&void 0!=n&&(n.indexOf("[]")>-1?("object"!=typeof s[n=n.replace("[]","")]&&(s[n]=[]),s[n][Object.keys(s[n]).length]=i):s[n]=i)}}return s};var i=function(){if("undefined"!=typeof g_settingsObj)for(key in g_settingsObj)if(g_settingsObj.hasOwnProperty(key)){var e=g_settingsObj[key];for(controlKey in e.controls)e.controls.hasOwnProperty(controlKey)&&(t[controlKey]=e.controls[controlKey])}},o=function(t){e("#"+t+" .unite-postbox .inside").slideUp("fast"),e("#"+t+" .unite-postbox h3").addClass("box_closed")};n.initAccordion=function(t){e("#"+t+" .unite-postbox h3").click(function(){var n=e(this);n.hasClass("box_closed")?(o(t),n.removeClass("box_closed").siblings(".inside").slideDown("fast")):n.addClass("box_closed").siblings(".inside").slideUp("fast")})};var c=function(){e(".button-image-select").click(function(){var t=this.id.replace("_button","");UniteAdminRev.openAddImageDialog("Choose Image",function(n,i){e("#"+t).val(n);var o=UniteAdminRev.getUrlShowImage(i,100,70,!0);if(e("#"+t+"_button_preview").html("<div style=\"width:100px;height:70px;background:url('"+o+"'); background-position:center center; background-size:cover;\"></div>"),e(".show_on_thumbnail_exist").show(),"checked"==e("#thumb_for_admin").attr("checked")){var c=e("#slide_thumb_button_preview div").css("background-image");e("#slide_selector .list_slide_links li.selected .slide-media-container").css({"background-image":c,backgroundSize:"cover",backgroundPosition:"center center"})}})}),e(".button-image-remove").click(function(){var t=this.id.replace("_button_remove","");e("#"+t).val(""),e("#"+t+"_button_preview").html(""),e(".show_on_thumbnail_exist").hide()}),e(".button-image-select-video").click(function(){UniteAdminRev.openAddImageDialog("Choose Image",function(t,n){e("#input_video_preview").val(t);var i=UniteAdminRev.getUrlShowImage(n,200,150,!0);e("#video-thumbnail-preview").css({backgroundImage:"url("+i+")"})})}),e(".button-image-remove-video").click(function(){e("#input_video_preview").val(""),"none"!=e("#video_block_vimeo").css("display")&&e("#button_vimeo_search").trigger("click"),"none"!=e("#video_block_youtube").css("display")&&e("#button_youtube_search").trigger("click")}),e(".button-image-select-html5-video").click(function(){UniteAdminRev.openAddImageDialog("Choose Image",function(t,n){e("#html5_url_poster").val(t)})})},s=function(){e(".list_settings li .setting_text").tipsy({gravity:"e",delayIn:70}),e(".tipsy_enabled_top").tipsy({gravity:"s",delayIn:70}),e(".tipsy_enabled_top_left").tipsy({gravity:"se",delayIn:70}),e(".button-primary").tipsy({gravity:"s",delayIn:70}),e('input[type="checkbox"]').change(function(){RevSliderSettings.onoffStatus(e(this))}),i(),c(),e("body").on("click",".rs-close-preview",function(){var t=e("#rs-preview-form");t.action=g_urlAjaxActions,e("#preview-slide-data").val("empty_output"),e("#preview_sliderid").val("empty_output"),e("#rs-preview-wrapper").hide(),t.submit()}),e(document).keyup(function(t){27==t.keyCode&&e(".rs-close-preview").click()}),e(window).resize(function(){e(".rs-preview-width").text(e(".rs-frame-preview-wrapper").width()),e(".rs-preview-height").text(e(".rs-frame-preview-wrapper").height())})};n.createModernOnOff=function(){e(".tp-moderncheckbox").each(function(){var t=e(this);t.wrap('<div class="tp-onoffbutton"><span class="tp-onoff-onstate">On</span><span class="tp-onoff-offstate">Off</span></div>'),t.change(function(){var t=e(this);t.hasClass("changedbyclick")||n.onoffStatus(t)})}),e(".tp-modernonoffboxes").each(function(){var t=e(this);t.wrap('<div class="tp-onoffbutton tp-onoffbasedontwo"><span class="tp-onoff-onstate">On</span><span class="tp-onoff-offstate">Off</span></div>');var i=t.find('input[value="on"]'),o=t.find('input[value="off"]');i.change(function(){i.hasClass("changedbyclick")||n.onoffStatusBasedOnTwo(i,o)}),o.change(function(){i.hasClass("changedbyclick")||n.onoffStatusBasedOnTwo(i,o)}),n.onoffStatusBasedOnTwo(i,o)}),e(".tp-onoffbutton").each(function(){e(this).click(function(){var t=e(this);if(t.hasClass("tp-onoffbasedontwo")){var i=t.find('input[value="on"]'),o=t.find('input[value="off"]');i.addClass("changedbyclick"),setTimeout(function(){i.removeClass("changedbyclick")},200),void 0===i.attr("checked")||0==i.attr("checked")||""==i.attr("checked")?(i.attr("checked","checked"),o.attr("checked",!1)):(i.attr("checked",!1),o.attr("checked","checked")),i.trigger("change"),o.trigger("change"),n.onoffStatusBasedOnTwo(i,o)}else inp=t.find("input"),inp.addClass("changedbyclick"),setTimeout(function(){inp.removeClass("changedbyclick")},200),void 0===inp.attr("checked")||0==inp.attr("checked")||""==inp.attr("checked")?inp.attr("checked","checked"):inp.attr("checked",!1),inp.trigger("change"),n.onoffStatus(inp)})})},n.onoffStatus=function(e){var t=e.closest(".tp-onoffbutton"),n=t.find(".tp-onoff-onstate"),i=t.find(".tp-onoff-offstate");void 0===e.attr("checked")||0==e.attr("checked")||""==e.attr("checked")?(punchgs.TweenLite.to(n,.2,{left:"50px"}),punchgs.TweenLite.to(i,.2,{left:"0px"})):(punchgs.TweenLite.to(n,.2,{left:"0px"}),punchgs.TweenLite.to(i,.2,{left:"-50px"}))},n.onoffStatusBasedOnTwo=function(e,t){var n=e.closest(".tp-onoffbutton"),i=n.find(".tp-onoff-onstate"),o=n.find(".tp-onoff-offstate");void 0===e.attr("checked")||0==e.attr("checked")||""==e.attr("checked")?(punchgs.TweenLite.to(i,.2,{left:"50px"}),punchgs.TweenLite.to(o,.2,{left:"0px"})):(punchgs.TweenLite.to(i,.2,{left:"0px"}),punchgs.TweenLite.to(o,.2,{left:"-50px"}))},e(document).ready(function(){s()})}}($nwd_jQuery);