<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Core
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<jstranslator>
    <!-- validation.js -->
    <validate-no-html-tags translate="message" module="core">
        <message>HTML tags are not allowed</message>
    </validate-no-html-tags>
    <validate-select translate="message" module="core">
        <message>Please select an option.</message>
    </validate-select>
    <required-entry translate="message" module="core">
        <message>This is a required field.</message>
    </required-entry>
    <validate-number translate="message" module="core">
        <message>Please enter a valid number in this field.</message>
    </validate-number>
    <validate-number-range translate="message" module="core">
        <message>The value is not within the specified range.</message>
    </validate-number-range>
    <validate-digits translate="message" module="core">
        <message>Please use numbers only in this field. Please avoid spaces or other characters such as dots or commas.</message>
    </validate-digits>
    <validate-digits-range translate="message" module="core">
        <message>The value is not within the specified range.</message>
    </validate-digits-range>
    <validate-alpha translate="message" module="core">
        <message>Please use letters only (a-z or A-Z) in this field.</message>
    </validate-alpha>
    <validate-code translate="message" module="core">
        <message>Please use only letters (a-z), numbers (0-9) or underscore(_) in this field, first character should be a letter.</message>
    </validate-code>
    <validate-code-event translate="message" module="core">
        <message>Please do not use "event" for an attribute code.</message>
    </validate-code-event>
    <validate-alphanum translate="message" module="core">
        <message>Please use only letters (a-z or A-Z) or numbers (0-9) only in this field. No spaces or other characters are allowed.</message>
    </validate-alphanum>
    <validate-street translate="message" module="core">
        <message>Please use only letters (a-z or A-Z) or numbers (0-9) or spaces and # only in this field.</message>
    </validate-street>
    <validate-phone-strict translate="message" module="core">
        <message>Please enter a valid phone number. For example (************* or ************.</message>
    </validate-phone-strict>
    <validate-phone-lax translate="message" module="core">
        <message>Please enter a valid phone number. For example (************* or ************.</message>
    </validate-phone-lax>
    <validate-fax translate="message" module="core">
        <message>Please enter a valid fax number. For example (************* or ************.</message>
    </validate-fax>
    <validate-date translate="message" module="core">
        <message>Please enter a valid date.</message>
    </validate-date>
    <validate-date-range translate="message" module="core">
        <message>The From Date value should be less than or equal to the To Date value.</message>
    </validate-date-range>
    <validate-email translate="message" module="core">
        <message>Please enter a valid email address. <NAME_EMAIL>.</message>
    </validate-email>
    <validate-email-sender translate="message" module="core">
        <message>Please use only visible characters and spaces.</message>
    </validate-email-sender>
    <validate-password translate="message" module="core">
        <message>Please enter more characters or clean leading or trailing spaces.</message>
    </validate-password>
    <validate-admin-password translate="message" module="core">
        <message>Please enter more characters. Password should contain both numeric and alphabetic characters.</message>
    </validate-admin-password>
    <validate-cpassword translate="message" module="core">
        <message>Please make sure your passwords match.</message>
    </validate-cpassword>
    <validate-url translate="message" module="core">
        <message>Please enter a valid URL. Protocol is required (http://, https:// or ftp://)</message>
    </validate-url>
    <validate-clean-url translate="message" module="core">
        <message>Please enter a valid URL. For example http://www.example.com or www.example.com</message>
    </validate-clean-url>
    <validate-identifier translate="message" module="core">
        <message>Please enter a valid URL Key. For example "example-page", "example-page.html" or "anotherlevel/example-page".</message>
    </validate-identifier>
    <validate-xml-identifier translate="message" module="core">
        <message>Please enter a valid XML-identifier. For example something_1, block5, id-4.</message>
    </validate-xml-identifier>
    <validate-ssn translate="message" module="core">
        <message>Please enter a valid social security number. For example ***********.</message>
    </validate-ssn>
    <validate-zip translate="message" module="core">
        <message>Please enter a valid zip code. For example 90602 or 90602-1234.</message>
    </validate-zip>
    <validate-zip-international translate="message" module="core">
        <message>Please enter a valid zip code.</message>
    </validate-zip-international>
    <validate-date-au translate="message" module="core">
        <message>Please use this date format: dd/mm/yyyy. For example 17/03/2006 for the 17th of March, 2006.</message>
    </validate-date-au>
    <validate-currency-dollar translate="message" module="core">
        <message>Please enter a valid $ amount. For example $100.00.</message>
    </validate-currency-dollar>
    <validate-one-required translate="message" module="core">
        <message>Please select one of the above options.</message>
    </validate-one-required>
    <validate-one-required-by-name translate="message" module="core">
        <message>Please select one of the options.</message>
    </validate-one-required-by-name>
    <validate-not-negative-number translate="message" module="core">
        <message>Please enter a valid number in this field.</message>
    </validate-not-negative-number>
    <validate-state translate="message" module="core">
        <message>Please select State/Province.</message>
    </validate-state>
    <validate-new-password translate="message" module="core">
        <message>Please enter more characters or clean leading or trailing spaces.</message>
    </validate-new-password>
    <validate-greater-than-zero translate="message" module="core">
        <message>Please enter a number greater than 0 in this field.</message>
    </validate-greater-than-zero>
    <validate-zero-or-greater translate="message" module="core">
        <message>Please enter a number 0 or greater in this field.</message>
    </validate-zero-or-greater>
    <validate-cc-number translate="message" module="core">
        <message>Please enter a valid credit card number.</message>
    </validate-cc-number>
    <validate-cc-type translate="message" module="core">
        <message>Credit card number does not match credit card type.</message>
    </validate-cc-type>
    <validate-cc-type-select translate="message" module="core">
        <message>Card type does not match credit card number.</message>
    </validate-cc-type-select>
    <validate-cc-exp translate="message" module="core">
        <message>Incorrect credit card expiration date.</message>
    </validate-cc-exp>
    <validate-cc-cvn translate="message" module="core">
        <message>Please enter a valid credit card verification number.</message>
    </validate-cc-cvn>
    <validate-data translate="message" module="core">
        <message>Please use only letters (a-z or A-Z), numbers (0-9) or underscore(_) in this field, first character should be a letter.</message>
    </validate-data>
    <validate-css-length translate="message" module="core">
        <message>Please input a valid CSS-length. For example 100px or 77pt or 20em or .5ex or 50%.</message>
    </validate-css-length>
    <validate-length translate="message" module="core">
        <message>Text length does not satisfy specified text range.</message>
    </validate-length>
    <validate-percents translate="message" module="core">
        <message>Please enter a number lower than 100.</message>
    </validate-percents>
    <required-file translate="message" module="core">
        <message>Please select a file</message>
    </required-file>
    <validate-cc-ukss translate="message" module="core">
        <message>Please enter issue number or start date for switch/solo card type.</message>
    </validate-cc-ukss>
    <!-- end validation.js -->

    <!-- rules.js -->
    <loading translate="message" module="core">
        <message>Please wait, loading...</message>
    </loading>
    <!-- end rules.js -->

    <!-- js.js -->
    <validate-date-required translate="message" module="core">
        <message>This date is a required value.</message>
    </validate-date-required>
    <validate-date-day translate="message" module="core">
        <message>Please enter a valid day (1-%d).</message>
    </validate-date-day>
    <validate-date-month translate="message" module="core">
        <message>Please enter a valid month (1-12).</message>
    </validate-date-month>
    <validate-date-year translate="message" module="core">
        <message>Please enter a valid year (1900-%d).</message>
    </validate-date-year>
    <validate-date-full-date translate="message" module="core">
        <message>Please enter a valid full date</message>
    </validate-date-full-date>
    <validate-date-date-between translate="message" module="core">
        <message>Please enter a valid date between %s and %s</message>
    </validate-date-date-between>
    <validate-date-greater translate="message" module="core">
        <message>Please enter a valid date equal to or greater than %s</message>
    </validate-date-greater>
    <validate-date-less translate="message" module="core">
        <message>Please enter a valid date less than or equal to %s</message>
    </validate-date-less>
    <!-- end js.js -->
</jstranslator>
