<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Tag
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <api>
        <resources>
            <catalog_product_tag translate="title" module="tag">
                <title>Product Tag API</title>
                <model>tag/api</model>
                <acl>catalog/product/tag</acl>
                <methods>
                    <list translate="title" module="tag">
                        <title>Retrieve list of tags by product</title>
                        <method>items</method>
                        <acl>catalog/product/tag/list</acl>
                    </list>
                    <info translate="title" module="tag">
                        <title>Retrieve product tag info</title>
                        <acl>catalog/product/tag/info</acl>
                    </info>
                    <add translate="title" module="tag">
                        <title>Add tag(s) to product</title>
                        <acl>catalog/product/tag/add</acl>
                    </add>
                    <update translate="title" module="tag">
                        <title>Update product tag</title>
                        <acl>catalog/product/tag/update</acl>
                    </update>
                    <remove translate="title" module="tag">
                        <title>Remove product tag</title>
                        <acl>catalog/product/tag/remove</acl>
                    </remove>
                </methods>
                <faults module="tag">
                    <store_not_exists>
                        <code>101</code>
                        <message>Requested store does not exist.</message>
                    </store_not_exists>
                    <product_not_exists>
                        <code>102</code>
                        <message>Requested product does not exist.</message>
                    </product_not_exists>
                    <customer_not_exists>
                        <code>103</code>
                        <message>Requested customer does not exist.</message>
                    </customer_not_exists>
                    <tag_not_exists>
                        <code>104</code>
                        <message>Requested tag does not exist.</message>
                    </tag_not_exists>
                    <invalid_data>
                        <code>105</code>
                        <message>Provided data is invalid.</message>
                    </invalid_data>
                    <save_error>
                        <code>106</code>
                        <message>Error while saving tag. Details in error message.</message>
                    </save_error>
                    <remove_error>
                        <code>107</code>
                        <message>Error while removing tag. Details in error message.</message>
                    </remove_error>
                </faults>
            </catalog_product_tag>
        </resources>
        <resources_alias>
            <product_tag>catalog_product_tag</product_tag>
        </resources_alias>
        <v2>
            <resources_function_prefix>
                <product_tag>catalogProductTag</product_tag>
            </resources_function_prefix>
        </v2>
        <rest>
            <mapping>
                <product_tag>
                    <post>
                        <method>add</method>
                    </post>
                    <delete>
                        <method>remove</method>
                    </delete>
                </product_tag>
            </mapping>
        </rest>
        <acl>
            <resources>
                <catalog>
                    <product>
                        <tag translate="title" module="tag">
                            <title>Tag</title>
                            <sort_order>103</sort_order>
                            <list translate="title" module="tag">
                                <title>List</title>
                            </list>
                            <info translate="title" module="tag">
                                <title>Info</title>
                            </info>
                            <add translate="title" module="tag">
                                <title>Add</title>
                            </add>
                            <update translate="title" module="tag">
                                <title>Update</title>
                            </update>
                            <remove translate="title" module="tag">
                                <title>Remove</title>
                            </remove>
                        </tag>
                    </product>
                </catalog>
            </resources>
        </acl>
    </api>
</config>
