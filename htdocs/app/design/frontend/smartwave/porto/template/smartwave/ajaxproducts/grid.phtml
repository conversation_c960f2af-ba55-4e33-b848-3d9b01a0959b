<?php
    $_helper = $this->helper('catalog/output');
    $_image_helper = $this->helper('catalog/image');

    $store = Mage::app()->getStore();
    $code  = $store->getCode();
    
    $_productCollection=$this->getLoadedProductCollection();
    $_productCollection->addAttributeToSelect('news_from_date')->addAttributeToSelect('news_to_date');
    
    $_helper = $this->helper('catalog/output');
    $_image_helper = $this->helper('catalog/image');
    $is_logged_user = Mage::getSingleton('customer/session')->isLoggedIn();

    $category_id = $this->getData("category_id");
    $aspect_ratio = $this->getData("aspect_ratio");
    if($aspect_ratio == null) {
        $aspect_ratio = Mage::getStoreConfig("porto_settings/category/aspect_ratio",$code);
    }
    $ratio_width = $this->getData("image_width");
    if(!$ratio_width) {
        $ratio_width = Mage::getStoreConfig("porto_settings/category/ratio_width",$code);
    }
    $ratio_height = $this->getData("image_height");
    if(!$ratio_height) {
        $ratio_height = Mage::getStoreConfig("porto_settings/category/ratio_height",$code);
    }    
    if(!$ratio_width)
        $ratio_width = 300;
    if(!$ratio_height)
        $ratio_height = 400;
    $columns = $this->getData("columns");
    if(!$columns)
        $columns = 5;
    $product_count = $this->getData("product_count");
    if(!$product_count)
        $product_count = 15;

    $lazyload = 1;

    $product_type = $this->getData("product_type");
    if($product_type == null) {
        $product_type = Mage::getStoreConfig("porto_settings/category_grid/product_type",$code);
    }
    if($product_type == null) {
        $product_type = 1;
    }
?>
<?php if(!$_productCollection->count()): ?>
<p class="note-msg"><?php echo $this->__('There are no products matching the selection.') ?></p>
<?php else: ?>
<div class="category-products">
    <?php // Grid Mode ?>

    <?php $_collectionSize = $_productCollection->count() ?>
    <?php
        $_columnCount = $columns;
    ?>
    <ul class="products-grid columns<?php echo $_columnCount; ?> <?php if($product_type == 2): ?>no-padding divider-line<?php endif; ?> <?php if($product_type == 5 || $product_type == 6 || $product_type == 7 || $product_type == 8): ?>no-padding<?php endif; ?> <?php if($product_type == 6): ?>divider-line<?php endif; ?>">
    <?php $i=0; foreach ($_productCollection as $_product): ?>
    <?php $product = $_product; ?>
    <?php
        $rnd_str = "";
        $base_str = "0123456789abcdefghijklmnopqrstuvwxyz";
        $su = strlen($base_str) - 1;
        $rnd_str = substr($base_str, rand(0,$su), 1).substr($base_str, rand(0,$su), 1).substr($base_str, rand(0,$su), 1);
    ?>
            <li class="item"><div class="item-area type<?php echo $product_type; ?>">
                <div class="product-image-area">
                    <div class="loader-container">
                        <div class="loader">
                            <i class="ajax-loader medium animate-spin"></i>
                        </div>
                    </div>
                    <?php
                    if( Mage::getStoreConfig("quickview/general/enableview", $code) ){
                        $base_url = $this->getUrl();
                        if(strpos($base_url,'?')!==false)
                            $base_url = explode("?",$this->getUrl());
                        if(is_array($base_url))
                            $base_url = $base_url[0];
                        $base_url .= "quickview/index/view/";
                        $quickview_url = $base_url."id/".$_product->getId();
                        ?>
                        <a href="<?php echo $quickview_url; ?>" class="quickview-icon"><i class="icon-export"></i><span><?php echo $this->__("Quick View"); ?></span></a>
                    <?php
                    }
                    ?>
                    <a href="<?php echo $_product->getProductUrl() ?>" title="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?>" class="product-image">
                    <?php
                        if(Mage::getStoreConfig("porto_settings/category/alternative_image", $code)){
                    ?>
                        <img class="defaultImage <?php if(!$lazyload): ?>porto-lazyload<?php endif;?>" <?php if(!$lazyload): ?>data-<?php endif; ?>src="<?php if($aspect_ratio):?><?php echo $_image_helper->init($_product, 'small_image')->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($ratio_width);?><?php else: ?><?php echo $_image_helper->init($_product, 'small_image')->resize($ratio_width,$ratio_height); ?><?php endif; ?>" width="<?php echo $ratio_width; ?>" height="<?php echo $ratio_height; ?>"/>
                        <img class="hoverImage" src="<?php if($aspect_ratio):?><?php echo $_image_helper->init($_product, 'thumbnail')->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($ratio_width);?><?php else: ?><?php echo $_image_helper->init($_product, 'thumbnail')->resize($ratio_width,$ratio_height); ?><?php endif; ?>" width="<?php echo $ratio_width; ?>" <?php if(!$aspect_ratio):?>height="<?php echo $ratio_height; ?>"<?php endif; ?> alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'thumbnail'), null, true) ?>"/>
                    <?php
                        } else {
                    ?>
                        <img <?php if(!$lazyload): ?>class="porto-lazyload" data-<?php endif; ?>src="<?php if($aspect_ratio):?><?php echo $_image_helper->init($_product, 'small_image')->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($ratio_width);?><?php else: ?><?php echo $_image_helper->init($_product, 'small_image')->resize($ratio_width,$ratio_height); ?><?php endif; ?>" width="<?php echo $ratio_width; ?>" height="<?php echo $ratio_height; ?>"/>
                    <?php
                        }
                    ?>
                    <?php if (Mage::helper("core")->isModuleEnabled("Magegiant_Dailydeal")): ?>
                    <?php
                    if(Mage::getStoreConfig("dailydeal/general/enable", $code)){
                    $deal = Mage::getModel('dailydeal/dailydeal')->getDealByProduct($_product->getId());

                    if ($deal && ($deal->getQuantity() - $deal->getSold() > 0)) {
                        ?>
                            <?php $now = Mage::getModel('core/date')->timestamp(time()) ?>
                            <?php $endTime = Mage::getModel('core/date')->timestamp(strtotime($deal->getCloseTime())); ?>
                            <div class="deal-label">
                                <i class="icon-clock"></i>
                            </div>
                            <div class="bottom-product-dailydeal bottom-home-dailydeal">
                                <ul class="time-left">
                                    <span><?php echo $this->__('Ends In:'); ?></span>
                                    <div class="timeleft timeleft_<?php echo $_product->getId() ?>"> </div>
                                </ul>
                            </div>
                        <script type="text/javascript">
                            //<![CDATA[
                            i++;
                            dailydealTimeCountersCategory[i] = new DailydealTimeCounter('<?php echo $now ?>', '<?php echo $endTime ?>', '<?php echo $deal->getId() ?>');
                            dailydealTimeCountersCategory[i].setTimeleft('timeleft_<?php echo $_product->getId() ?>');
                            //]]>
                            </script>
                    <?php
                    }
                    }
                    ?>
                    <?php endif; ?>
                    </a>
                    <?php 
                        
                        $sale_label = false;
                        
                        
                        // Get the Special Price
                        $specialprice = $product->getFinalPrice();
                        $orgprice = $product->getPrice();
                        // Get the Special Price FROM date
                        $now = date("Y-m-d");
                        $specialPriceFromDate = substr($product->getSpecialFromDate(),0,10);
                        // Get the Special Price TO date
                        $specialPriceToDate = substr($product->getSpecialToDate(),0,10);
                        // Get Current date
                        $today =  time();
                     
                        if ($specialprice<$orgprice){
                            $save_percent = 100-round(($specialprice/$orgprice)*100);
                            if ($specialPriceToDate != '' || $specialPriceFromDate != ''){
                            if (($specialPriceToDate != '' && $specialPriceFromDate != '' && $now>=$specialPriceFromDate && $now<=$specialPriceToDate) || ($specialPriceToDate == '' && $now >=$specialPriceFromDate) || ($specialPriceFromDate == '' && $now<=$specialPriceToDate)){
                                if(Mage::getStoreConfig("porto_settings/product_label/sale", $code)){
                                    $sale_label = true;
                    ?>
                                <div class="product-label" style="right: 10px;"><span class="sale-product-icon"><?php if(Mage::getStoreConfig("porto_settings/product_label/sale_label_type", $code)):?><?php echo "-".$save_percent."%"; ?><?php else: ?><?php echo Mage::getStoreConfig("porto_settings/product_label/sale_label_text", $code);?><?php endif; ?></span></div>
                    <?php       
                                }
                            }
                        }
                        }
                    ?>
                    <?php
                        $now = date("Y-m-d");
                        $newsFrom= substr($_product->getData('news_from_date'),0,10);
                        $newsTo=  substr($_product->getData('news_to_date'),0,10);
                        if ($newsTo != '' || $newsFrom != ''){
                            if (($newsTo != '' && $newsFrom != '' && $now>=$newsFrom && $now<=$newsTo) || ($newsTo == '' && $now >=$newsFrom) || ($newsFrom == '' && $now<=$newsTo))
                            {
                                if(Mage::getStoreConfig("porto_settings/product_label/new", $code)){
                        ?> 
                                <div class="product-label" style="right: 10px; <?php echo ($sale_label)?"top: 40px;":""; ?>"><span class="new-product-icon"><?php echo Mage::getStoreConfig("porto_settings/product_label/new_label_text", $code);?></span></div>
                        <?php 
                                }
                            }
                        }
                    ?>
                    <?php if($product_type == 3 || $product_type == 5 || $product_type == 6 || $product_type == 7 || $product_type == 8 || $product_type == 9): ?>
                    <div class="actions">
                        <?php if(Mage::getStoreConfig("porto_settings/category/qty_field", $code)):?>
                            <?php if($product->isSaleable()): ?>
                                <?php  if ( !($product->getTypeInstance(true)->hasOptions($product) || $product->isGrouped()) ) :  ?>
                                <?php if(!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>
                                <form id="addtocart_form_<?php echo $_product->getId()."_".$rnd_str; ?>" action="<?php echo $this->getAddToCartUrl($_product) ?>" method="post">
                                    <input type="hidden" name="form_key" value="<?php echo Mage::getSingleton('core/session')->getFormKey(); ?>" />
                                <?php endif; ?>
                                <div class="qty-field">
                                    <label for="qty"><?php echo $this->__('Qty:') ?></label>
                                    <div class="qty-holder">
                                        <input type="text" name="qty" id="qty_<?php echo $_product->getId(); ?>" maxlength="12" value="<?php echo $product->getProductDefaultQty() * 1 ?>" title="<?php echo $this->__('Qty') ?>" class="input-text qty" />
                                        <div class="qty-changer">
                                            <a href="javascript:void(0)" class="qty_inc"><i class="icon-up-dir"></i></a>
                                            <a href="javascript:void(0)" class="qty_dec"><i class="icon-down-dir"></i></a>
                                        </div>
                                    </div>
                                </div>
                                <?php if(!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>
                                </form>
                                <?php endif; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if($product->isSaleable()): ?>
                            <?php  if ( !($product->getTypeInstance(true)->hasOptions($product) || $product->isGrouped()) ) :  ?>
                                <a href="<?php if(!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code) && !Mage::getStoreConfig("porto_settings/category/qty_field", $code)): ?><?php echo $this->getAddToCartUrl($_product) ?><?php else: ?>javascript:void(0)<?php endif; ?>" class="addtocart" title="<?php echo $this->__('Add to Cart') ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>onclick="setLocationAjax(this,'<?php echo $this->getAddToCartUrl($_product) ?>','<?php echo $_product->getId()."_".$rnd_str; ?>')"<?php elseif(Mage::getStoreConfig("porto_settings/category/qty_field", $code)): ?>onclick="document.getElementById('addtocart_form_<?php echo $_product->getId(); ?>').submit()"<?php endif; ?>><i class="icon-cart"></i><span>&nbsp;<?php echo $this->__('Add to Cart') ?></span></a>
                            <?php else : ?>
                                <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>javascript:showOptions('<?php echo $_product->getId()?>')<?php else: ?><?php echo $this->getAddToCartUrl($_product) ?><?php endif; ?>" class="addtocart" title="<?php echo $this->__('Add to Cart') ?>"><i class="icon-cart"></i><span>&nbsp;<?php echo $this->__('Add to Cart') ?></span></a>
                                <a href='<?php echo $this->getUrl('ajaxcart/index/options',array('product_id'=>$_product->getId()));?>' class='fancybox' id='fancybox<?php echo $_product->getId()?>' style='display:none'>Options</a>
                            <?php endif;?>
                        <?php else: ?>
                            <a href="javascript:void(0);" class="addtocart outofstock" title="<?php echo $this->__('Out of stock') ?>"><?php echo $this->__('Out of stock') ?></span></a>
                        <?php endif; ?>
                        <?php if($product_type != 3 && $product_type != 5 && $product_type != 6): ?>
                        <?php if ($this->helper('wishlist')->isAllow()) : ?>
                            <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>javascript:void(0)<?php else: ?><?php echo $this->helper('wishlist')->getAddUrl($_product) ?><?php endif; ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>onclick="ajaxWishlist(this,'<?php echo $this->helper('wishlist')->getAddUrl($_product) ?>','<?php echo $_product->getId()?>');"<?php endif;?> class="addtowishlist" title="<?php echo $this->__('Add to Wishlist') ?>"><i class="icon-wishlist"></i></a>
                        <?php endif; ?>
                        <?php endif; ?>
                        <?php if(Mage::getStoreConfig("porto_settings/category/compare", $code) && $_compareUrl=$this->getAddToCompareUrl($_product)): ?>
                            <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>javascript:void(0)<?php else: ?><?php echo $_compareUrl ?><?php endif; ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>onclick="ajaxCompare(this,'<?php echo $_compareUrl ?>','<?php echo $_product->getId()?>');"<?php endif; ?> class="comparelink" title="<?php echo $this->__('Add to Compare') ?>"><i class="icon-compare"></i></a>
                        <?php endif; ?>
                        <div class="clearer"></div>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="details-area">
                    <h2 class="product-name"><a href="<?php echo $_product->getProductUrl() ?>" title="<?php echo $this->stripTags($_product->getName(), null, true) ?>"><?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?></a></h2>
                    <?php if($product_type == 3 || $product_type == 5 || $product_type == 6): ?>
                        <?php if ($this->helper('wishlist')->isAllow()) : ?>
                            <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>javascript:void(0)<?php else: ?><?php echo $this->helper('wishlist')->getAddUrl($_product) ?><?php endif; ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>onclick="ajaxWishlist(this,'<?php echo $this->helper('wishlist')->getAddUrl($_product) ?>','<?php echo $_product->getId()?>');"<?php endif;?> class="addtowishlist" title="<?php echo $this->__('Add to Wishlist') ?>"><i class="icon-wishlist"></i></a>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if(Mage::getStoreConfig("porto_settings/category/rating_star", $code)):?>
                    <?php 
                    if (Mage::helper('catalog')->isModuleEnabled('Mage_Review')):
                    ?>
                    <?php 
                        $review_html = $this->getReviewsSummaryHtml($product, 'short');
                        if($review_html){
                            echo $review_html;
                        }else{
                    ?>
                        <div class="ratings">
                            <div class="rating-box">
                                <div class="rating" style="width:0"></div>
                            </div>
                        </div>
                    <?php                            
                        }
                    ?>
                    <?php else: ?>
                        <div class="ratings">
                            <div class="rating-box">
                                <div class="rating" style="width:0"></div>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php endif; ?>
                    <?php if(Mage::getStoreConfig("porto_settings/category/product_price", $code) && $is_logged_user):?>
                    <?php echo $this->getPriceHtml($_product, true) ?>
                    <?php endif; ?>
                    <?php if($product_type != 3 && $product_type != 5 && $product_type != 6 && $product_type != 7 && $product_type != 8 && $product_type != 9): ?>
                    <div class="actions">
                        <?php if(Mage::getStoreConfig("porto_settings/category/qty_field", $code)):?>
                            <?php if($product->isSaleable()): ?>
                                <?php  if ( !($product->getTypeInstance(true)->hasOptions($product) || $product->isGrouped()) ) :  ?>
                                <?php if(!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>
                                <form id="addtocart_form_<?php echo $_product->getId()."_".$rnd_str; ?>" action="<?php echo $this->getAddToCartUrl($_product) ?>" method="post">
                                    <input type="hidden" name="form_key" value="<?php echo Mage::getSingleton('core/session')->getFormKey(); ?>" />
                                <?php endif; ?>
                                <div class="qty-field">
                                    <label for="qty"><?php echo $this->__('Qty:') ?></label>
                                    <div class="qty-holder">
                                        <input type="text" name="qty" id="qty_<?php echo $_product->getId(); ?>" maxlength="12" value="<?php echo $product->getProductDefaultQty() * 1 ?>" title="<?php echo $this->__('Qty') ?>" class="input-text qty" />
                                        <div class="qty-changer">
                                            <a href="javascript:void(0)" class="qty_inc"><i class="icon-up-dir"></i></a>
                                            <a href="javascript:void(0)" class="qty_dec"><i class="icon-down-dir"></i></a>
                                        </div>
                                    </div>
                                </div>
                                <?php if(!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>
                                </form>
                                <?php endif; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if($product_type == 1 || $product_type == 2 || $product_type == 9): ?>
                        <?php if ($this->helper('wishlist')->isAllow()) : ?>
                            <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>javascript:void(0)<?php else: ?><?php echo $this->helper('wishlist')->getAddUrl($_product) ?><?php endif; ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>onclick="ajaxWishlist(this,'<?php echo $this->helper('wishlist')->getAddUrl($_product) ?>','<?php echo $_product->getId()?>');"<?php endif;?> class="addtowishlist" title="<?php echo $this->__('Add to Wishlist') ?>"><i class="icon-wishlist"></i></a>
                        <?php endif; ?>
                        <?php endif; ?>
                        <?php if($product->isSaleable()): ?>
                            <?php  if ( !($product->getTypeInstance(true)->hasOptions($product) || $product->isGrouped()) ) :  ?>
                                <a href="<?php if(!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code) && !Mage::getStoreConfig("porto_settings/category/qty_field", $code)): ?><?php echo $this->getAddToCartUrl($_product) ?><?php else: ?>javascript:void(0)<?php endif; ?>" class="addtocart" title="<?php echo $this->__('Add to Cart') ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>onclick="setLocationAjax(this,'<?php echo $this->getAddToCartUrl($_product) ?>','<?php echo $_product->getId()."_".$rnd_str; ?>')"<?php elseif(Mage::getStoreConfig("porto_settings/category/qty_field", $code)): ?>onclick="document.getElementById('addtocart_form_<?php echo $_product->getId(); ?>').submit()"<?php endif; ?>><i class="icon-cart"></i><span>&nbsp;<?php echo $this->__('Add to Cart') ?></span></a>
                            <?php else : ?>
                                <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>javascript:showOptions('<?php echo $_product->getId()?>')<?php else: ?><?php echo $this->getAddToCartUrl($_product) ?><?php endif; ?>" class="addtocart" title="<?php echo $this->__('Add to Cart') ?>"><i class="icon-cart"></i><span>&nbsp;<?php echo $this->__('Add to Cart') ?></span></a>
                                <a href='<?php echo $this->getUrl('ajaxcart/index/options',array('product_id'=>$_product->getId()));?>' class='fancybox' id='fancybox<?php echo $_product->getId()?>' style='display:none'>Options</a>
                            <?php endif;?>
                        <?php else: ?>
                            <a href="javascript:void(0);" class="addtocart outofstock" title="<?php echo $this->__('Out of stock') ?>"><?php echo $this->__('Out of stock') ?></span></a>
                        <?php endif; ?>
                        <?php if($product_type != 1 && $product_type != 2 && $product_type != 9): ?>
                        <?php if ($this->helper('wishlist')->isAllow()) : ?>
                            <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>javascript:void(0)<?php else: ?><?php echo $this->helper('wishlist')->getAddUrl($_product) ?><?php endif; ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>onclick="ajaxWishlist(this,'<?php echo $this->helper('wishlist')->getAddUrl($_product) ?>','<?php echo $_product->getId()?>');"<?php endif;?> class="addtowishlist" title="<?php echo $this->__('Add to Wishlist') ?>"><i class="icon-wishlist"></i></a>
                        <?php endif; ?>
                        <?php endif; ?>
                        <?php if(Mage::getStoreConfig("porto_settings/category/compare", $code) && $_compareUrl=$this->getAddToCompareUrl($_product)): ?>
                            <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>javascript:void(0)<?php else: ?><?php echo $_compareUrl ?><?php endif; ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>onclick="ajaxCompare(this,'<?php echo $_compareUrl ?>','<?php echo $_product->getId()?>');"<?php endif; ?> class="comparelink" title="<?php echo $this->__('Add to Compare') ?>"><i class="icon-compare"></i></a>
                        <?php endif; ?>
                        <div class="clearer"></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div></li>
        <?php endforeach; ?>
        </ul>
        <?php if($_collectionSize == $product_count): ?>
        <div class="load-more-area">
            <p class="ajax-loader-area" style="margin: 40px 0;display: none;"><i class="ajax-loader large animate-spin"></i></p>
            <a href="javascript:void(0)" cat_id="<?php echo $category_id; ?>" product_count="<?php echo $product_count + $columns; ?>"><?php echo $this->__("Load More"); ?></a>
        </div>
        <?php endif; ?>
        <script type="text/javascript">
        jQuery(function($){
            $('.ajax-products .products-grid li:nth-child(2n)').addClass('nth-child-2n');
            $('.ajax-products .products-grid li:nth-child(2n+1)').addClass('nth-child-2np1');
            $('.ajax-products .products-grid li:nth-child(3n)').addClass('nth-child-3n');
            $('.ajax-products .products-grid li:nth-child(3n+1)').addClass('nth-child-3np1');
            $('.ajax-products .products-grid li:nth-child(4n)').addClass('nth-child-4n');
            $('.ajax-products .products-grid li:nth-child(4n+1)').addClass('nth-child-4np1');
            $('.ajax-products .products-grid li:nth-child(5n)').addClass('nth-child-5n');
            $('.ajax-products .products-grid li:nth-child(5n+1)').addClass('nth-child-5np1');
            $('.ajax-products .products-grid li:nth-child(6n)').addClass('nth-child-6n');
            $('.ajax-products .products-grid li:nth-child(6n+1)').addClass('nth-child-6np1');
            $('.ajax-products .products-grid li:nth-child(7n)').addClass('nth-child-7n');
            $('.ajax-products .products-grid li:nth-child(7n+1)').addClass('nth-child-7np1');
            $('.ajax-products .products-grid li:nth-child(8n)').addClass('nth-child-8n');
            $('.ajax-products .products-grid li:nth-child(8n+1)').addClass('nth-child-8np1');
            
            $('.ajax-products img.defaultImage').each(function(){
                str = $(this).attr("src");
                if(str.replace("/small_image/","/thumbnail/") == $(this).parent().children("img.hoverImage").attr("src")) {
                    $(this).parent().children("img.hoverImage").remove();
                    $(this).removeClass("defaultImage");
                }
            });
            
            $('.load-more-area > a').off("click").on("click", function(){
                url = "<?php echo Mage::getBaseUrl()."swporto/index/showcategoryproducts" ?>";
                cat_id = $(this).attr("cat_id");
                
                $(".ajax-products .load-more-area > .ajax-loader-area").fadeIn();
                
                $.ajax({
                    method:"POST",
                    url:url,
                    data:{
                        aspect_ratio:"<?php echo $aspect_ratio; ?>",
                        image_width:"<?php echo $ratio_width; ?>",
                        image_height:"<?php echo $ratio_height; ?>",
                        lazy_owl:"<?php echo $lazy_owl; ?>",
                        product_count:"<?php echo $product_count+$columns; ?>",
                        columns:"<?php echo $columns; ?>",
                        category_id:cat_id
                    },
                    dataType: 'json'
                }).done(function(data){
                    $(".ajax-products > .category-detail > .products-area").html(data.category_products);
                }).fail(function(){
                    
                }).always(function(){
                    $(".ajax-products .load-more-area > .ajax-loader-area").fadeOut();
                });
            });
        });
        </script>
</div>
<?php endif; ?>
