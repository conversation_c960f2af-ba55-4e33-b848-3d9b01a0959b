
/***
TinyMCE add option to use dashicons
***/
i.mce-i-icon { font: 400 20px/1 dashicons; padding: 0; vertical-align: top; speak: none;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;margin-left: -2px;padding-right: 2px}

div[aria-describedby="revslider-tiny-mce-dialog"]   	{	font-family: sans-serif}
div[aria-describedby="revslider-tiny-mce-dialog"]:before 	{	content:" ";position:fixed;width:100%;height:100%;background:rgba(0,0,0,0.5); top:0px;left:0px;}

div[aria-describedby="revslider-tiny-mce-dialog"].ui-dialog.ui-widget.ui-widget-content { padding:0px !important; border-radius: 0; -webkit-border-radius: 0; -moz-border-radius: 0; box-shadow:none;border:none !important;}
div[aria-describedby="revslider-tiny-mce-dialog"] .ui-widget-header	{	background:#fff !important; border-radius: 0; -webkit-border-radius: 0; -moz-border-radius: 0; border:none !important; padding:0px !important}

div[aria-describedby="revslider-tiny-mce-dialog"] .ui-dialog-titlebar {	height:80px; position:relative;z-index: 1 }
div[aria-describedby="revslider-tiny-mce-dialog"] .ui-dialog-title	{ background: url(../images/logo_small.png) no-repeat;width: 207px;height: 80px;top: 0px;position: absolute;left: 20px;background-position: left center;padding-left:225px;line-height:50px;}

div[aria-describedby="revslider-tiny-mce-dialog"] .ui-dialog-titlebar-close			{	background:transparent; padding:0px; width:20px !important;height:20px !important; right:30px; top:20px; position: absolute; border:none !important; font-weight: 400; font-size:20px; margin:0px;}
div[aria-describedby="revslider-tiny-mce-dialog"] .ui-dialog-titlebar-close:before	{	background:transparent !important; content: "\f158"; font-size:30px; font-family: dashicons; color:#777; }

div[aria-describedby="revslider-tiny-mce-dialog"] .ui-button-icon-primary.ui-icon.ui-icon-closethick	{	display:none;}

#revslider-existing-slider {	display: none;}

#revslider-tiny-mce-dialog .button-primary { text-shadow:none !important;}

.ui-widget-overlay.ui-front { background: #000; opacity:0.5;}

.rs-slider-modify-li,
.rs-slide-modify-li 					{	float:left; position:relative; margin-right:10px !important;margin-bottom:10px !important; padding:0 !important; display:block; width:160px;height:125px; cursor: pointer;}
.rs-slider-modify-li.selected:before,
.rs-slide-modify-li.selected:before 	{ 	position:absolute;content:" "; background:#31ad6a; opacity:0.9; width:100%;height:100%;top:0px;left:0px;	}

.rs-slider-modify-li.selected:after,
.rs-slide-modify-li.selected:after 	{	font-family: dashicons; top:40px; left:60px; font-size:35px; color:#fff;content: "\f147"; position: absolute;}

.rs-slider-modify-li .rs-slider-modify-container,
.rs-slide-modify-li .rs-slide-modify-container {	width:160px;height:95px;top:0px;left:0px;}

.rs-slider-modify-li .rs-slider-modify-container.mini-transparent,
.rs-slide-modify-li .rs-slide-modify-container.mini-transparent	{	background:url(../images/trans_tile2.png); background-repeat: repeat;}

.rs-slider-modify-li .rs-slider-modify-title,
.rs-slide-modify-li .rs-slide-modify-title	 {	 width: 160px;
    height: 30px;
    background: #eee;
    line-height: 30px;
    padding: 0px 10px;
    text-align: left;
    color: #555;
    font-size: 11px;
    display: block;
    position: absolute;
    bottom: 0px;
    left: 0px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
}

.rs-slider-modify-li:hover .rs-slider-modify-title,
.rs-slide-modify-li:hover .rs-slide-modify-title {
	background:#333;
	color:#fff;
}


#revslider-tiny-mce-dialog					{	position: relative; padding:0px !important; background:#ddd;}


/***
 ShortCode Generator Fixes
 ***/
 
.ui-dialog {
	z-index: 1000102 !important;
}


#rs-shortcode-select-wrapper,
.rs-mod-slides-wrapper 					{	margin:0 !important; padding:0 !important; height:374px; max-height: 374px; overflow: scroll;}
.revslider-quicktitle 					{	background:#777;  padding:0px 20px; height:50px; line-height: 50px; font-weight:900; margin:0px !important;}
.revslider-quicktitle:before 			{	content:" "; background:url(../images/quickslider.png); background-size:125px 50px; background-position: center center; width:125px; height:50px; display: block; margin:0px !important;}
.revslider-quick-inner-wrapper			{	padding:20px;}

.revslider-stepnavigator,
.revslider-stepnavigator strong			{	line-height: 75px; background: #fff; height:75px; padding:0px 20px; color:#444; font-size:15px; font-weight: 400;}
.revslider-stepnavigator strong 		{	font-weight: 900; padding-left:0px;}

.revslider-currentstep 					{	display: inline-block; float:left; margin:0px; padding:0px;}
.revslider-step-actions-wrapper			{	display:inline-block; float:right; margin:0px; padding:0px;}

#rs-add-predefined-slider,
#rs-modify-predefined-slider,
#revslider-add-custom-shortcode-modify,
.rs-goto-step-1 						{ 	cursor:pointer !important;background:#27ae60 !important; line-height:54px !important; height:54px !important; padding:0px 20px !important; color:#fff !important; font-size:15px !important; font-weight: 14px !important;border:none !important; outline:none  !important; box-shadow: none  !important; border-radius: 0  !important; -moz-border-radius:0  !important; vertical-align: middle !important;margin: 0px 10px 0px 0px!important;}

.rs-goto-step-1,
#rs-modify-predefined-slider 			{	background: #3498db !important}

#rs-add-predefined-slider .dashicons, 
#rs-modify-predefined-slider .dashicons,
.rs-goto-step-1 .dashicons,
#revslider-add-custom-shortcode-modify .dashicons	{	line-height: 55px; font-size:25px; width:40px;}


#rs-create-predefined-slider 			{    display: block;width: 160px;height: 125px;background: rgba(0, 0, 0, 0) !important;border: 1px dashed #aaa;outline: none !important;border-radius: 0px;box-shadow: none !important; position:absolute;}

#rs-create-predefined-slider .dashicons          {       top: 35px;left: 50%;color: #252525;font-size: 35px;margin-left: -17px; position: absolute;}



#rs-add-predefined-slider.nonclickable,
#rs-modify-predefined-slider.nonclickable 			{	background:#eee !important; color:#bbb !important; cursor:default !important;}



#revslider-tiny-settings-wrap .slide-link-published-wrapper {
    position: absolute;
    top: 5px;
    right: 5px;
}



#revslider-tiny-settings-wrap .slide-published, 
#revslider-tiny-settings-wrap .slide-hero-published {
    display: none!important;
    width: 20px;
    height: 20px;
    background: url(../images/toolbar/icon-slide-published.png);
    position: absolute;
    top: 0;
    right: 0;
}
#revslider-tiny-settings-wrap .slide-unpublished.pubclickable, 
#revslider-tiny-settings-wrap .slide-hero-unpublished.pubclickable {
    display: none!important;
}




#revslider-tiny-settings-wrap li:hover .slide-published, 
#revslider-tiny-settings-wrap li:hover .slide-hero-published, 
#revslider-tiny-settings-wrap li:hover .slide-unpublished, 
#revslider-tiny-settings-wrap li:hover .slide-hero-unpublished {
    display: block!important;
}

#revslider-tiny-settings-wrap .slide-unpublished, 
#revslider-tiny-settings-wrap .slide-hero-unpublished {
    width: 20px;
    height: 20px;
    background: url(../images/toolbar/icon-slide-unpublished.png);
    position: absolute;
    top: 0;
    right: 0;
}

#revslider-tiny-settings-wrap .pubclickable {
    right: 25px !important;
    cursor: pointer;
    -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=30);
    filter: alpha(opacity=30);
    -moz-opacity: .3;
    -khtml-opacity: .3;
    opacity: .3;
}

#revslider-tiny-settings-wrap .pubclickable:hover {
	-ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1;
}

