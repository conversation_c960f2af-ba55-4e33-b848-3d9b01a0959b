<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Core
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_Core>
            <version>1.6.0.10</version>
        </Mage_Core>
    </modules>
    <global>
        <models>
            <core_resource>
                <entities>
                    <website>
                        <table>core_website</table>
                    </website>
                    <store_group>
                        <table>core_store_group</table>
                    </store_group>
                    <store>
                        <table>core_store</table>
                    </store>
                    <config_field>
                        <table>core_config_field</table>
                    </config_field>
                    <config_data>
                        <table>core_config_data</table>
                    </config_data>
                    <email_template>
                        <table>core_email_template</table>
                    </email_template>
                    <variable>
                        <table>core_variable</table>
                    </variable>
                    <variable_value>
                        <table>core_variable_value</table>
                    </variable_value>
                    <translate>
                        <table>core_translate</table>
                    </translate>
                    <session>
                        <table>core_session</table>
                    </session>
                    <layout_update>
                        <table>core_layout_update</table>
                    </layout_update>
                    <layout_link>
                        <table>core_layout_link</table>
                    </layout_link>
                    <url_rewrite>
                        <table>core_url_rewrite</table>
                    </url_rewrite>
                    <url_rewrite_tag>
                        <table>core_url_rewrite_tag</table>
                    </url_rewrite_tag>
                    <design_change>
                        <table>design_change</table>
                    </design_change>
                    <flag>
                        <table>core_flag</table>
                    </flag>
                    <email_queue>
                        <table>core_email_queue</table>
                    </email_queue>
                    <email_recipients>
                        <table>core_email_queue_recipients</table>
                    </email_recipients>
                    <file_storage><table>core_file_storage</table></file_storage>
                    <directory_storage><table>core_directory_storage</table></directory_storage>
                </entities>
            </core_resource>
        </models>
        <blocks>
            <core>
                <class>Mage_Core_Block</class>
            </core>
        </blocks>
        <helpers>
            <core>
                <encryption_model>Mage_Core_Model_Encryption</encryption_model>
            </core>
        </helpers>
        <template>
            <email>
                <design_email_header translate="label" module="core">
                    <label>Email - Header</label>
                    <file>html/header.html</file>
                    <type>text</type>
                </design_email_header>
                <design_email_footer translate="label" module="core">
                    <label>Email - Footer</label>
                    <file>html/footer.html</file>
                    <type>text</type>
                </design_email_footer>
            </email>
        </template>
        <cache>
            <types>
                <config translate="label,description" module="core">
                    <label>Configuration</label>
                    <description>System(config.xml, local.xml) and modules configuration files(config.xml).</description>
                    <tags>CONFIG</tags>
                </config>
                <layout translate="label,description" module="core">
                    <label>Layouts</label>
                    <description>Layout building instructions.</description>
                    <tags>LAYOUT_GENERAL_CACHE_TAG</tags>
                </layout>
                <block_html translate="label,description" module="core">
                    <label>Blocks HTML output</label>
                    <description>Page blocks HTML.</description>
                    <tags>BLOCK_HTML</tags>
                </block_html>
                <translate translate="label,description" module="core">
                    <label>Translations</label>
                    <description>Translation files.</description>
                    <tags>TRANSLATE</tags>
                </translate>
                <collections translate="label,description" module="core">
                    <label>Collections Data</label>
                    <description>Collection data files.</description>
                    <tags>COLLECTION_DATA</tags>
                </collections>
            </types>
        </cache>
        <session>
            <validation>
                <http_user_agent_skip>
                    <flash>Shockwave Flash</flash>
                    <flash_mac><![CDATA[Adobe Flash Player\s{1,}\w{1,10}]]></flash_mac>
                </http_user_agent_skip>
            </validation>
        </session>
        <request>
            <direct_front_name/>
        </request>
        <log>
            <core>
                <writer_model>Zend_Log_Writer_Stream</writer_model>
            </core>
        </log>
        <url_rewrite>
            <model>core/url_rewrite</model>
        </url_rewrite>
        <request_rewrite>
            <model>core/url_rewrite_request</model>
        </request_rewrite>
        <events>
            <controller_action_predispatch>
                <observers>
                    <security_domain_policy>
                        <class>Mage_Core_Model_Domainpolicy</class>
                        <method>addDomainPolicyHeader</method>
                    </security_domain_policy>
                </observers>
            </controller_action_predispatch>
            <model_save_before>
                <observers>
                    <secure_var_processing>
                        <class>core/observer</class>
                        <method>secureVarProcessing</method>
                    </secure_var_processing>
                </observers>
            </model_save_before>
            <model_delete_before>
                <observers>
                    <secure_var_processing>
                        <class>core/observer</class>
                        <method>secureVarProcessing</method>
                    </secure_var_processing>
                </observers>
            </model_delete_before>
        </events>
    </global>
    <frontend>
        <routers>
            <core>
                <use>standard</use>
                <args>
                    <module>Mage_Core</module>
                    <frontName>core</frontName>
                </args>
            </core>
        </routers>
        <translate>
            <modules>
                <Mage_Core>
                    <files>
                        <default>Mage_Core.csv</default>
                    </files>
                </Mage_Core>
            </modules>
        </translate>
        <layout>
            <updates>
                <core>
                    <file>core.xml</file>
                </core>
            </updates>
        </layout>
    </frontend>
    <adminhtml>
        <translate>
            <modules>
                <Mage_Core>
                    <files>
                        <default>Mage_Core.csv</default>
                    </files>
                </Mage_Core>
            </modules>
        </translate>
        <events>
            <cms_wysiwyg_config_prepare>
                <observers>
                    <variable_observer>
                        <class>core/variable_observer</class>
                        <method>prepareWysiwygPluginConfig</method>
                    </variable_observer>
                </observers>
            </cms_wysiwyg_config_prepare>
            <controller_action_predispatch_adminhtml>
                <observers>
                    <synchronize_notification>
                        <class>core/observer</class>
                        <method>addSynchronizeNotification</method>
                    </synchronize_notification>
                </observers>
            </controller_action_predispatch_adminhtml>
            <show_synchronize_message>
                <observers>
                    <synchronize_notification>
                        <class>core/observer</class>
                        <method>addSynchronizeNotification</method>
                    </synchronize_notification>
                </observers>
            </show_synchronize_message>
            <clean_cache_by_tags>
                <observers>
                    <clean_cache_observer>
                        <class>core/observer</class>
                        <method>cleanCacheByTags</method>
                    </clean_cache_observer>
                </observers>
            </clean_cache_by_tags>
        </events>
    </adminhtml>
    <install>
        <translate>
            <modules>
                <Mage_Core>
                    <files>
                        <default>Mage_Core.csv</default>
                    </files>
                </Mage_Core>
            </modules>
        </translate>
    </install>
    <default>
        <design>
            <package>
                <area>frontend</area>
                <default_theme>default</default_theme>
                <name>rwd</name>
                <theme>default</theme>
                <translate>default</translate>
            </package>
            <pagination>
                <list_allow_all>1</list_allow_all>
                <pagination_frame>5</pagination_frame>
            </pagination>
            <email>
                <css_non_inline>email-non-inline.css</css_non_inline>
                <header>design_email_header</header>
                <footer>design_email_footer</footer>
            </email>
        </design>
        <dev>
            <restrict>
                <allow_ips/>
            </restrict>
            <debug>
                <profiler>0</profiler>
            </debug>
            <translate_inline>
                <active>0</active>
                <active_admin>0</active_admin>
                <invalid_caches>
                    <block_html/>
                </invalid_caches>
            </translate_inline>
            <log>
                <active>0</active>
                <file>system.log</file>
                <exception_file>exception.log</exception_file>
            </log>
            <js>
                <merge_files>0</merge_files>
            </js>
        </dev>
        <system>
            <csrf>
                <use_form_key>1</use_form_key>
            </csrf>
            <smtp>
                <disable>0</disable>
                <host>localhost</host>
                <port>25</port>
            </smtp>
            <media_storage_configuration>
                <media_storage>0</media_storage>
                <media_database>default_setup</media_database>
                <configuration_update_time>3600</configuration_update_time>
                <allowed_resources>
                    <compiled_css_folder>css</compiled_css_folder>
                    <compiled_css_secure_folder>css_secure</compiled_css_secure_folder>
                    <compiled_js_folder>js</compiled_js_folder>
                </allowed_resources>
                <ignored_resources>.svn,.htaccess</ignored_resources>
            </media_storage_configuration>
        </system>
        <trans_email>
            <ident_custom1>
                <email><EMAIL></email>
                <name>Custom 1</name>
            </ident_custom1>
            <ident_custom2>
                <email><EMAIL></email>
                <name>Custom 2</name>
            </ident_custom2>
            <ident_general>
                <email><EMAIL></email>
                <name>Owner</name>
            </ident_general>
            <ident_sales>
                <email><EMAIL></email>
                <name>Sales</name>
            </ident_sales>
            <ident_support>
                <email><EMAIL></email>
                <name>CustomerSupport</name>
            </ident_support>
        </trans_email>
        <web>
            <routers>
                <admin>
                    <area>admin</area>
                    <class>Mage_Core_Controller_Varien_Router_Admin</class>
                </admin>
                <standard>
                    <area>frontend</area>
                    <class>Mage_Core_Controller_Varien_Router_Standard</class>
                </standard>
            </routers>
            <url>
                <use_store>0</use_store>
                <redirect_to_base>1</redirect_to_base>
            </url>
            <seo>
                <use_rewrites>0</use_rewrites>
            </seo>
            <unsecure>
                <base_url>{{base_url}}</base_url>
                <base_web_url>{{unsecure_base_url}}</base_web_url>
                <base_link_url>{{unsecure_base_url}}</base_link_url>
                <base_js_url>{{unsecure_base_url}}js/</base_js_url>
                <base_skin_url>{{unsecure_base_url}}skin/</base_skin_url>
                <base_media_url>{{unsecure_base_url}}media/</base_media_url>
            </unsecure>
            <secure>
                <base_url>{{base_url}}</base_url>
                <base_web_url>{{secure_base_url}}</base_web_url>
                <base_link_url>{{secure_base_url}}</base_link_url>
                <base_js_url>{{secure_base_url}}js/</base_js_url>
                <base_skin_url>{{secure_base_url}}skin/</base_skin_url>
                <base_media_url>{{secure_base_url}}media/</base_media_url>
                <use_in_frontend>0</use_in_frontend>
                <use_in_adminhtml>0</use_in_adminhtml>
                <offloader_header>SSL_OFFLOADED</offloader_header>
            </secure>
            <cookie>
                <cookie_lifetime>3600</cookie_lifetime>
                <cookie_httponly>1</cookie_httponly>
                <cookie_restriction>0</cookie_restriction>
                <cookie_restriction_lifetime>31536000</cookie_restriction_lifetime>
            </cookie>
            <session>
                <use_remote_addr>0</use_remote_addr>
                <use_http_via>0</use_http_via>
                <use_http_x_forwarded_for>0</use_http_x_forwarded_for>
                <use_http_user_agent>0</use_http_user_agent>
                <use_frontend_sid>1</use_frontend_sid>
                <use_admin_sid>0</use_admin_sid>
            </session>
            <browser_capabilities>
                <cookies>1</cookies>
                <javascript>1</javascript>
            </browser_capabilities>
        </web>
        <admin>
            <startup>
                <page>dashboard</page>
            </startup>
            <url>
                <use_custom>0</use_custom>
                <custom/>
            </url>
            <security>
                <use_form_key>1</use_form_key>
                <domain_policy_backend>2</domain_policy_backend>
                <domain_policy_frontend>2</domain_policy_frontend>
                <extensions_compatibility_mode>1</extensions_compatibility_mode>
                <secure_system_configuration_save_disabled>0</secure_system_configuration_save_disabled>
            </security>
        </admin>
        <general>
            <country>
                <eu_countries>AT,BE,BG,CY,CZ,DK,EE,FI,FR,DE,GR,HU,IE,IT,LV,LT,LU,MT,NL,PL,PT,RO,SK,SI,ES,SE,GB</eu_countries>
            </country>
            <locale>
                <firstday>0</firstday>
                <weekend>0,6</weekend>
            </locale>
            <file>
                <protected_extensions>
                    <!-- BOF PHP script file extensions -->
                    <php>php</php>
                    <php3>php3</php3>
                    <php4>php4</php4>
                    <php5>php5</php5>
                    <php7>php7</php7>
                    <!-- EOF PHP script file extensions -->
                    <!-- File extension of configuration of an Apache Web server -->
                    <htaccess>htaccess</htaccess>
                    <!-- Java script file extension -->
                    <jsp>jsp</jsp>
                    <!-- Perl script file extension -->
                    <pl>pl</pl>
                    <!-- Python script file extension -->
                    <py>py</py>
                    <!-- Active Server Page script file extension -->
                    <asp>asp</asp>
                    <!-- UNIX command prompt file extension -->
                    <sh>sh</sh>
                    <!-- Common Gateway Interface script extension type -->
                    <cgi>cgi</cgi>
                    <!-- BOF HTML file extensions -->
                    <htm>htm</htm>
                    <html>html</html>
                    <pht>pht</pht>
                    <phtml>phtml</phtml>
                    <shtml>shtml</shtml>
                    <!-- EOF HTML file extensions -->
                </protected_extensions>

                <!-- Valid Paths to make public files -->
                <public_files_valid_paths>
                    <protected>
                        <app>/app/*/*</app>
                        <dev>/dev/*/*</dev>
                        <downloader>/downloader/*/*</downloader>
                        <errors>/errors/*/*</errors>
                        <includes>/includes/*/*</includes>
                        <js>/js/*/*</js>
                        <lib>/lib/*/*</lib>
                        <shell>/shell/*/*</shell>
                        <skin>/skin/*/*</skin>
                    </protected>
                </public_files_valid_paths>
            </file>
            <!-- NOTE: If you turn off images reprocessing, then your upload images process may cause security risks. -->
            <reprocess_images>
                <active>1</active>
            </reprocess_images>
            <!-- Additional email for notifications -->
            <additional_notification_emails>
                <!-- On creating a new admin user. You can specify several emails separated by commas. -->
                <admin_user_create></admin_user_create>
            </additional_notification_emails>
        </general>
        <validators>
            <custom_layout>
                <disallowed_block>
                    <Mage_Core_Block_Template_Zend/>
                </disallowed_block>
            </custom_layout>
        </validators>
    </default>
    <stores>
        <default>
            <web>
                <routers>
                    <admin>
                        <area>admin</area>
                        <class>Mage_Core_Controller_Varien_Router_Admin</class>
                    </admin>
                    <standard>
                        <area>frontend</area>
                        <class>Mage_Core_Controller_Varien_Router_Standard</class>
                    </standard>
                </routers>
            </web>
        </default>
    </stores>
    <crontab>
        <jobs>
            <core_clean_cache>
                <schedule>
                    <cron_expr>30 2 * * *</cron_expr>
                </schedule>
                <run>
                    <model>core/observer::cleanCache</model>
                </run>
            </core_clean_cache>
            <core_email_queue_send_all>
                <schedule><cron_expr>*/1 * * * *</cron_expr></schedule>
                <run><model>core/email_queue::send</model></run>
            </core_email_queue_send_all>
            <core_email_queue_clean_up>
                <schedule><cron_expr>0 0 * * *</cron_expr></schedule>
                <run><model>core/email_queue::cleanQueue</model></run>
            </core_email_queue_clean_up>
        </jobs>
    </crontab>
</config>
