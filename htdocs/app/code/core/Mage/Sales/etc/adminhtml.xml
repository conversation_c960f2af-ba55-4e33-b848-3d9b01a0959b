<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Sales
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <menu>
         <sales translate="title" module="sales">
            <title>Sales</title>
            <sort_order>20</sort_order>
            <depends><module>Mage_Sales</module></depends>
            <children>
                <order translate="title" module="sales">
                    <title>Orders</title>
                    <action>adminhtml/sales_order</action>
                    <sort_order>10</sort_order>
                </order>
                <invoice translate="title" module="sales">
                    <title>Invoices</title>
                    <action>adminhtml/sales_invoice</action>
                    <sort_order>20</sort_order>
                </invoice>
                <shipment translate="title" module="sales">
                    <title>Shipments</title>
                    <action>adminhtml/sales_shipment</action>
                    <sort_order>30</sort_order>
                </shipment>
                <creditmemo translate="title" module="sales">
                    <title>Credit Memos</title>
                    <action>adminhtml/sales_creditmemo</action>
                    <sort_order>40</sort_order>
                </creditmemo>
                <transactions translate="title" module="sales">
                    <title>Transactions</title>
                    <action>adminhtml/sales_transactions</action>
                    <sort_order>50</sort_order>
                </transactions>
                <recurring_profile translate="title" module="sales">
                    <title>Recurring Profiles (beta)</title>
                    <action>adminhtml/sales_recurring_profile</action>
                    <sort_order>60</sort_order>
                </recurring_profile>
                <billing_agreement translate="title" module="sales">
                    <title>Billing Agreements</title>
                    <action>adminhtml/sales_billing_agreement</action>
                    <sort_order>70</sort_order>
                </billing_agreement>
            </children>
         </sales>
         <system>
            <children>
                <order_statuses translate="title" module="sales">
                    <title>Order Statuses</title>
                    <action>adminhtml/sales_order_status</action>
                    <sort_order>105</sort_order>
                </order_statuses>
            </children>
         </system>
    </menu>
    <acl>
        <resources>
            <admin>
                <children>
                    <sales translate="title" module="sales">
                        <title>Sales</title>
                        <children>
                            <order translate="title">
                                <title>Orders</title>
                                <children>
                                    <actions translate="title">
                                        <title>Actions</title>
                                        <children>
                                            <create translate="title"><title>Create</title></create>
                                            <view translate="title"><title>View</title></view>
                                            <email translate="title"><title>Send Order Email</title></email>
                                            <reorder translate="title"><title>Reorder</title></reorder>
                                            <edit translate="title"><title>Edit</title></edit>
                                            <cancel translate="title"><title>Cancel</title></cancel>
                                            <review_payment translate="title"><title>Accept or Deny Payment</title></review_payment>
                                            <capture translate="title"><title>Capture</title></capture>
                                            <invoice translate="title"><title>Invoice</title></invoice>
                                            <creditmemo translate="title"><title>Credit Memos</title></creditmemo>
                                            <hold translate="title"><title>Hold</title></hold>
                                            <unhold translate="title"><title>Unhold</title></unhold>
                                            <ship translate="title"><title>Ship</title></ship>
                                            <comment translate="title"><title>Comment</title></comment>
                                            <reorder translate="title"><title>Reorder</title></reorder>
                                            <emails translate="title"><title>Send Sales Emails</title></emails>
                                        </children>
                                    </actions>
                                </children>
                                <sort_order>10</sort_order>
                            </order>
                            <invoice translate="title">
                                <title>Invoices</title>
                                <sort_order>20</sort_order>
                            </invoice>
                            <shipment translate="title">
                                <title>Shipments</title>
                                <sort_order>30</sort_order>
                            </shipment>
                            <creditmemo translate="title">
                                <title>Credit Memos</title>
                                <sort_order>40</sort_order>
                            </creditmemo>
                            <transactions translate="title">
                                <title>Transactions</title>
                                <children>
                                    <fetch translate="title"><title>Fetch</title></fetch>
                                </children>
                                <sort_order>50</sort_order>
                            </transactions>
                            <recurring_profile translate="title">
                                <title>Recurring Profiles</title>
                                <sort_order>60</sort_order>
                            </recurring_profile>
                            <billing_agreement translate="title">
                                <title>Billing Agreements</title>
                                    <children>
                                        <actions translate="title">
                                            <title>Actions</title>
                                            <children>
                                                <view translate="title">
                                                    <title>View</title>
                                                    <sort_order>5</sort_order>
                                                </view>
                                                <manage translate="title">
                                                    <title>Manage</title>
                                                    <sort_order>10</sort_order>
                                                </manage>
                                                <use translate="title">
                                                    <title>Place Order Using Billing Agreements</title>
                                                    <sort_order>15</sort_order>
                                                </use>
                                            </children>
                                        </actions>
                                    </children>
                                <sort_order>70</sort_order>
                            </billing_agreement>
                        </children>
                    </sales>
                    <system>
                        <children>
                            <order_statuses>
                                <title>Order Statuses</title>
                                <sort_order>15</sort_order>
                            </order_statuses>
                            <config>
                                <children>
                                    <sales translate="title">
                                        <title>Sales Section</title>
                                        <sort_order>60</sort_order>
                                    </sales>
                                    <sales_email translate="title">
                                        <title>Sales Emails Section</title>
                                        <sort_order>65</sort_order>
                                    </sales_email>
                                    <sales_pdf translate="title">
                                        <title>PDF Print-outs</title>
                                        <sort_order>66</sort_order>
                                    </sales_pdf>
                                </children>
                            </config>
                        </children>
                    </system>
                </children>
            </admin>
        </resources>
    </acl>
</config>
