<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Core
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <api>
        <resources>
            <core_store translate="title" module="core">
                <model>core/store_api</model>
                <title>Store API</title>
                <acl>core/store</acl>
                <methods>
                    <list translate="title" module="core">
                        <title>Retrieve store list</title>
                        <method>items</method>
                        <acl>core/store/list</acl>
                    </list>
                    <info translate="title" module="core">
                        <title>Retrieve store data</title>
                        <acl>core/store/info</acl>
                    </info>
                </methods>
                <faults module="core">
                    <store_not_exists>
                        <code>100</code>
                        <message>Requested store view not found.</message>
                    </store_not_exists>
                </faults>
            </core_store>
            <core_magento translate="title" module="core">
                <model>core/magento_api</model>
                <title>Magento info API</title>
                <acl>core/magento</acl>
                <methods>
                    <info translate="title" module="core">
                        <title>Get info about current Magento installation</title>
                        <acl>core/magento/info</acl>
                    </info>
                </methods>
            </core_magento>
        </resources>
        <resources_alias>
            <store>core_store</store>
            <magento>core_magento</magento>
        </resources_alias>
        <v2>
            <resources_function_prefix>
                <store>store</store>
                <magento>magento</magento>
            </resources_function_prefix>
        </v2>
        <rest>
            <mapping>
            </mapping>
        </rest>
        <acl>
            <resources>
                <core translate="title" module="core">
                    <title>Core</title>
                    <sort_order>1</sort_order>
                    <store translate="title" module="core">
                        <title>Store</title>
                        <info translate="title" module="core">
                            <title>Retrieve store data</title>
                        </info>
                        <list translate="title" module="core">
                            <title>List of stores</title>
                        </list>
                    </store>
                    <magento translate="title" module="core">
                        <title>Magento info</title>
                        <info translate="title" module="core">
                            <title>Retrieve info about current Magento installation</title>
                        </info>
                    </magento>
                </core>
            </resources>
        </acl>
    </api>
</config>
