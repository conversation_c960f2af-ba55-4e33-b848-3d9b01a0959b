<?php
/** @var Praktis_Order_Block_Adminhtml_Sales_Order_View_Items_Renderer_Default $this */
?>
<?php
/** @var Mage_Sales_Model_Order_Item $_item */
$_item = $this->getItem()
?>
<?php $comment = $this->getItemComment($_item); ?>
<?php $this->setPriceDataObject($_item) ?>
<tr<?php if (!$this->canDisplayGiftmessage()): ?> class="border"<?php endif; ?>>
    <td>
        <?php if ($this->canDisplayContainer()): ?>
        <div id="<?php echo $this->getHtmlId() ?>" class="item-container">
            <?php endif; ?>
            <div class="item-text">
                <?php echo $this->getColumnHtml($_item, 'name') ?>
            </div>
            <?php if ($this->canDisplayContainer()): ?>
        </div>
    <?php endif ?>
    </td>
    <td class="a-center">
        <div id="comment-box<?php echo $_item->getId(); ?>" style="display: none">
            <textarea name="item[<?php echo $_item->getId(); ?>]comment"
                      id="item_comment_<?php echo $_item->getId(); ?>"
                      cols="50" rows="10"><?php echo $comment; ?></textarea>
            <div class="ordersedit-buttons">
                <button type="button" title="<?php echo $this->__("Cancel"); ?>"
                        onclick="orderItemComment.cancelItemCommentEdit('<?php echo $_item->getId() ?>')">
                    <span><span><?php echo $this->__("Cancel"); ?></span></span>
                </button>
                <button type="button" title="<?php echo $this->__("Save"); ?>"
                        onclick="orderItemComment.saveItemComment('<?php echo $_item->getId() ?>')">
                    <span><span><?php echo $this->__("Save"); ?></span></span>
                </button>
            </div>
        </div>
        <div id="comment-text<?php echo $_item->getId(); ?>">
            <?php echo $comment; ?>
        </div>
        <br/>
        <button title="<?php echo $this->__("Edit"); ?>"
                type="button" class="scalable"
                id="edit-comment-button<?php echo $_item->getId(); ?>"
                onclick="orderItemComment.editItemComment('<?php echo $_item->getId() ?>')">
            <span><span><?php echo $this->__("Comment"); ?></span></span>
        </button>
    </td>
    <td class="a-center"><?php echo $_item->getStatus() ?></td>
    <td class="a-right"><?php echo $this->displayPriceAttribute('original_price') ?></td>
    <td class="a-right">
        <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceExclTax()): ?>
            <span class="price-excl-tax">
                <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                    <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
                <?php endif; ?>

                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales', $_item->getStoreId())): ?>
                    <?php
                    echo $this->displayPrices(
                        $_item->getBasePrice()+$_item->getBaseWeeeTaxAppliedAmount()+$_item->getBaseWeeeTaxDisposition(),
                        $_item->getPrice()+$_item->getWeeeTaxAppliedAmount()+$_item->getWeeeTaxDisposition()
                    );
                    ?>
                <?php else: ?>
                    <?php echo $this->displayPrices($_item->getBasePrice(), $_item->getPrice()) ?>
                <?php endif; ?>


                <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                    <br />
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales', $_item->getStoreId())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount'], $tax['amount']); ?></span>
                        <?php endforeach; ?>
                        </small>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount'], $tax['amount']); ?></small></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales', $_item->getStoreId())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount'], $tax['amount']); ?></span>
                        <?php endforeach; ?>
                        </small>
                    <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <br />
                        <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br />
                            <?php
                            echo $this->displayPrices(
                                $_item->getBasePrice()+$_item->getBaseWeeeTaxAppliedAmount()+$_item->getBaseWeeeTaxDisposition(),
                                $_item->getPrice()+$_item->getWeeeTaxAppliedAmount()+$_item->getWeeeTaxDisposition()
                            );
                            ?>
                        </span>
                    <?php endif; ?>
                <?php endif; ?>
            </span>
            <br />
        <?php endif; ?>
        <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceInclTax()): ?>
            <span class="price-incl-tax">
                <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                    <span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
                <?php endif; ?>
                <?php $_incl = $this->helper('checkout')->getPriceInclTax($_item); ?>
                <?php $_baseIncl = $this->helper('checkout')->getBasePriceInclTax($_item); ?>

                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales', $_item->getStoreId())): ?>
                    <?php echo $this->displayPrices($_baseIncl + Mage::helper('weee')->getBaseWeeeTaxInclTax($_item), $_incl + Mage::helper('weee')->getWeeeTaxInclTax($_item)); ?>
                <?php else: ?>
                    <?php echo $this->displayPrices($_baseIncl-$_item->getBaseWeeeTaxDisposition(), $_incl-$_item->getWeeeTaxDisposition()) ?>
                <?php endif; ?>

                <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                    <br />
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales', $_item->getStoreId())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount_incl_tax'], $tax['amount_incl_tax']); ?></span>
                        <?php endforeach; ?>
                        </small>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount_incl_tax'], $tax['amount_incl_tax']); ?></small></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales', $_item->getStoreId())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount_incl_tax'], $tax['amount_incl_tax']); ?></span>
                        <?php endforeach; ?>
                        </small>
                    <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <br />
                        <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br /> <?php echo $this->displayPrices($_baseIncl + Mage::helper('weee')->getBaseWeeeTaxInclTax($_item) , $_incl + Mage::helper('weee')->getWeeeTaxInclTax($_item)); ?></span>
                    <?php endif; ?>
                <?php endif; ?>
            </span>
        <?php endif; ?>

    </td>
    <td><?php echo $this->getColumnHtml($_item, 'qty') ?></td>

    <td class="a-right">
        <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceExclTax()): ?>
            <span class="price-excl-tax">
                <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                    <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
                <?php endif; ?>

                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales', $_item->getStoreId())): ?>
                    <?php
                    echo $this->displayPrices(
                        $_item->getBaseRowTotal()+$_item->getBaseWeeeTaxAppliedRowAmount()+$_item->getBaseWeeeTaxRowDisposition(),
                        $_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()
                    );
                    ?>
                <?php else: ?>
                    <?php echo $this->displayPrices($_item->getBaseRowTotal(), $_item->getRowTotal()) ?>
                <?php endif; ?>


                <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales', $_item->getStoreId())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount'], $tax['row_amount']); ?></span>
                        <?php endforeach; ?>
                        </small>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount'], $tax['row_amount']); ?></small></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales', $_item->getStoreId())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount'], $tax['row_amount']); ?></span>
                        <?php endforeach; ?>
                        </small>
                    <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <br />
                        <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br />
                            <?php
                            echo $this->displayPrices(
                                $_item->getBaseRowTotal()+$_item->getBaseWeeeTaxAppliedRowAmount()+$_item->getBaseWeeeTaxRowDisposition(),
                                $_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()
                            );
                            ?>
                        </span>
                    <?php endif; ?>
                <?php endif; ?>
            </span>
            <br />
        <?php endif; ?>
        <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceInclTax()): ?>
            <span class="price-incl-tax">
                <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                    <span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
                <?php endif; ?>
                <?php $_incl = $this->helper('checkout')->getSubtotalInclTax($_item); ?>
                <?php $_baseIncl = $this->helper('checkout')->getBaseSubtotalInclTax($_item); ?>
                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales', $_item->getStoreId())): ?>
                    <?php echo $this->displayPrices($_baseIncl + Mage::helper('weee')->getBaseRowWeeeTaxInclTax($_item), $_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?>
                <?php else: ?>
                    <?php echo $this->displayPrices($_baseIncl-$_item->getBaseWeeeTaxRowDisposition(), $_incl-$_item->getWeeeTaxRowDisposition()) ?>
                <?php endif; ?>


                <?php if (Mage::helper('weee')->getApplied($_item)): ?>

                    <br />
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales', $_item->getStoreId())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount_incl_tax'], $tax['row_amount_incl_tax']); ?></span>
                        <?php endforeach; ?>
                        </small>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount_incl_tax'], $tax['row_amount_incl_tax']); ?></small></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales', $_item->getStoreId())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount_incl_tax'], $tax['row_amount_incl_tax']); ?></span>
                        <?php endforeach; ?>
                        </small>
                    <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <br /><span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br /> <?php echo $this->displayPrices($_baseIncl + Mage::helper('weee')->getBaseRowWeeeTaxInclTax($_item),$_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?></span>
                    <?php endif; ?>
                <?php endif; ?>
            </span>
        <?php endif; ?>
    </td>
    <td class="a-right"><?php echo $this->displayPriceAttribute('tax_amount') ?></td>
    <td class="a-right"><?php echo $this->displayTaxPercent($_item) ?></td>
    <td class="a-right"><?php echo $this->displayPriceAttribute('discount_amount') ?></td>
    <td class="a-right last">
        <?php echo $this->displayPrices(
            $_item->getBaseRowTotal() + $_item->getBaseTaxAmount() + $_item->getBaseHiddenTaxAmount() + Mage::helper('weee')->getBaseRowWeeeAmountAfterDiscount($_item) - $_item->getBaseDiscountAmount(),
            $_item->getRowTotal() + $_item->getTaxAmount() + $_item->getHiddenTaxAmount() + Mage::helper('weee')->getRowWeeeAmountAfterDiscount($_item) - $_item->getDiscountAmount()
        ); ?>
    </td>
</tr>
