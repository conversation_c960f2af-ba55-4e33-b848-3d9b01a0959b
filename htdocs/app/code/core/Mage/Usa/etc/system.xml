<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Usa
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <sections>
        <carriers>
            <groups>
                <dhl translate="label" module="usa">
                    <label>DHL (Deprecated)</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>130</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <account translate="label">
                            <label>Account Number</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </account>
                        <active translate="label">
                            <label>Enabled for Checkout</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </active>
                        <allowed_methods translate="label">
                            <label>Allowed Methods</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_source_method</source_model>
                            <sort_order>170</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </allowed_methods>
                        <contentdesc translate="label">
                            <label>Package Description</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>120</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </contentdesc>
                        <!--
                        If the free_shipping_enable flag enable, the system will check free_shipping_subtotal to give free shipping
                        otherwise will use shopping cart price rule behaviour
                        -->
                        <free_shipping_enable translate="label">
                            <label>Free Shipping with Minimum Order Amount</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_enabledisable</source_model>
                            <sort_order>1210</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_enable>
                        <free_shipping_subtotal translate="label">
                            <label>Minimum Order Amount for Free Shipping</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>1220</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_subtotal>
                        <dutiable translate="label">
                            <label>Shipment Dutiable</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>130</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </dutiable>
                        <dutypaymenttype translate="label">
                            <label>Shipment Duty Payment Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_source_dutypaymenttype</source_model>
                            <sort_order>140</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </dutypaymenttype>
                        <free_method translate="label">
                            <label>Free Method</label>
                            <frontend_type>select</frontend_type>
                            <frontend_class>free-method</frontend_class>
                            <source_model>usa/shipping_carrier_dhl_source_freemethod</source_model>
                            <sort_order>1200</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_method>
                        <gateway_url translate="label">
                            <label>Gateway URL</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>adminhtml/system_config_backend_gatewayurl</backend_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </gateway_url>
                        <verify_peer translate="label">
                            <label>Enable SSL Verification</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </verify_peer>
                        <handling_type translate="label">
                            <label>Calculate Handling Fee</label>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingType</source_model>
                            <sort_order>100</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_type>
                        <handling_action translate="label">
                            <label>Handling Applied</label>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingAction</source_model>
                            <sort_order>110</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_action>
                        <handling_fee translate="label">
                            <label>Handling Fee</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>120</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_fee>
                        <max_package_weight translate="label">
                            <label>Maximum Package Weight (Please consult your shipping carrier for maximum supported shipping weight)</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>130</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </max_package_weight>
                        <id translate="label">
                            <label>Access ID</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </id>
                        <password translate="label">
                            <label>Password</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </password>
                        <shipment_requesttype translate="label">
                            <label>Packages Request Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_abstract_source_requesttype</source_model>
                            <sort_order>85</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </shipment_requesttype>
                        <shipment_type translate="label">
                            <label>Shipment Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_source_shipmenttype</source_model>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </shipment_type>
                        <shipping_intlkey translate="label">
                            <label>Shipping Key (International)</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </shipping_intlkey>
                        <shipping_key translate="label">
                            <label>Shipping Key</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </shipping_key>
                        <sort_order translate="label">
                            <label>Sort Order</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>2000</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sort_order>
                        <title translate="label">
                            <label>Title</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </title>
                        <sallowspecific translate="label">
                            <label>Ship to Applicable Countries</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>1900</sort_order>
                            <frontend_class>shipping-applicable-country</frontend_class>
                            <source_model>adminhtml/system_config_source_shipping_allspecificcountries</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sallowspecific>
                        <specificcountry translate="label">
                            <label>Ship to Specific Countries</label>
                            <frontend_type>multiselect</frontend_type>
                            <sort_order>1910</sort_order>
                            <source_model>adminhtml/system_config_source_country</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </specificcountry>
                        <showmethod translate="label">
                            <label>Show Method if Not Applicable</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>1940</sort_order>
                            <frontend_class>shipping-skip-hide</frontend_class>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </showmethod>
                        <specificerrmsg translate="label">
                            <label>Displayed Error Message</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>800</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </specificerrmsg>

                        <additional_protection_enabled translate="label">
                            <label>Additional Protection Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>1300</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </additional_protection_enabled>
                        <additional_protection_min_value translate="label">
                            <label>Additional Protection Min Subtotal</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>1310</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </additional_protection_min_value>
                        <additional_protection_use_subtotal translate="label">
                            <label>Additional Protection Value</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_source_protection_value</source_model>
                            <sort_order>1320</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </additional_protection_use_subtotal>
                        <additional_protection_value translate="label comment">
                            <label>Additional Protection Configuration Value</label>
                            <comment>Used only when "Additional Protection Value" is set to "Configuration". Can contain only numeric amount.</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>1330</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </additional_protection_value>

                        <additional_protection_rounding translate="label">
                            <label>Additional Protection Value Rounding Method</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_source_protection_rounding</source_model>
                            <sort_order>1340</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </additional_protection_rounding>

                        <hazardous_materials translate="label">
                            <label>Shipment Contains Hazardous Materials</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>1350</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </hazardous_materials>

                        <default_length translate="label">
                            <label>Default Package Length</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>1360</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </default_length>
                        <default_width translate="label">
                            <label>Default Package Width</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>1370</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </default_width>
                        <default_height translate="label">
                            <label>Default Package Height</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>1380</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </default_height>

                        <shipment_days translate="label">
                            <label>Domestic Shipment Days</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>adminhtml/system_config_source_locale_weekdaycodes</source_model>
                            <sort_order>1390</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </shipment_days>

                        <intl_shipment_days translate="label">
                            <label>International Shipment Days</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>adminhtml/system_config_source_locale_weekdaycodes</source_model>
                            <sort_order>1400</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </intl_shipment_days>
                        <debug translate="label">
                            <label>Debug</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>1950</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </debug>
                    </fields>
                </dhl>
                <fedex translate="label" module="usa">
                    <label>FedEx</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>120</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <active translate="label">
                            <label>Enabled for Checkout</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </active>
                        <title translate="label">
                            <label>Title</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </title>
                        <account translate="label comment">
                            <label>Account ID</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>Please make sure to use only digits here. No dashes are allowed.</comment>
                        </account>
                        <meter_number translate="label">
                            <label>Meter Number</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </meter_number>
                        <key translate="label">
                            <label>Key</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </key>
                        <password translate="label">
                            <label>Password</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </password>
                        <sandbox_mode translate="label">
                            <label>Sandbox Mode</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>75</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sandbox_mode>
                        <shipment_requesttype translate="label">
                            <label>Packages Request Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_abstract_source_requesttype</source_model>
                            <sort_order>77</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </shipment_requesttype>
                        <packaging translate="label">
                            <label>Packaging</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_fedex_source_packaging</source_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </packaging>
                        <dropoff translate="label">
                            <label>Dropoff</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_fedex_source_dropoff</source_model>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </dropoff>
                        <unit_of_measure translate="label">
                            <label>Weight Unit</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_fedex_source_unitofmeasure</source_model>
                            <sort_order>95</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </unit_of_measure>
                        <max_package_weight translate="label">
                            <label>Maximum Package Weight (Please consult your shipping carrier for maximum supported shipping weight)</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>100</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </max_package_weight>
                        <handling_type translate="label">
                            <label>Calculate Handling Fee</label>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingType</source_model>
                            <sort_order>110</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_type>
                        <handling_action translate="label">
                            <label>Handling Applied</label>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingAction</source_model>
                            <sort_order>120</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_action>
                        <handling_fee translate="label">
                            <label>Handling Fee</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>130</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_fee>
                        <residence_delivery translate="label">
                            <label>Residential Delivery</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>140</sort_order>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </residence_delivery>
                        <allowed_methods translate="label">
                            <label>Allowed Methods</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>usa/shipping_carrier_fedex_source_method</source_model>
                            <sort_order>150</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </allowed_methods>
                        <smartpost_hubid translate="label comment">
                            <label>Hub ID</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>155</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>The field is applicable if the Smart Post method is selected.</comment>
                        </smartpost_hubid>
                        <free_method translate="label">
                            <label>Free Method</label>
                            <frontend_type>select</frontend_type>
                            <frontend_class>free-method</frontend_class>
                            <source_model>usa/shipping_carrier_fedex_source_freemethod</source_model>
                            <sort_order>160</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_method>
                        <free_shipping_enable translate="label">
                            <label>Free Shipping with Minimum Order Amount</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_enabledisable</source_model>
                            <sort_order>170</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_enable>
                        <free_shipping_subtotal translate="label">
                            <label>Minimum Order Amount for Free Shipping</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>180</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_subtotal>
                        <specificerrmsg translate="label">
                            <label>Displayed Error Message</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>190</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </specificerrmsg>
                        <sallowspecific translate="label">
                            <label>Ship to Applicable Countries</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>200</sort_order>
                            <frontend_class>shipping-applicable-country</frontend_class>
                            <source_model>adminhtml/system_config_source_shipping_allspecificcountries</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sallowspecific>
                        <specificcountry translate="label">
                            <label>Ship to Specific Countries</label>
                            <frontend_type>multiselect</frontend_type>
                            <sort_order>210</sort_order>
                            <source_model>adminhtml/system_config_source_country</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </specificcountry>
                        <debug translate="label">
                            <label>Debug</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>220</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </debug>
                        <showmethod translate="label">
                            <label>Show Method if Not Applicable</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>230</sort_order>
                            <frontend_class>shipping-skip-hide</frontend_class>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </showmethod>
                        <sort_order translate="label">
                            <label>Sort Order</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>240</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        0</sort_order>
                    </fields>
                </fedex>
                <ups translate="label" module="usa">
                    <label>UPS</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>100</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <access_license_number translate="label">
                            <label>Access License Number</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </access_license_number>
                        <active translate="label">
                            <label>Enabled for Checkout</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </active>
                        <allowed_methods translate="label">
                            <label>Allowed Methods</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>usa/shipping_carrier_ups_source_method</source_model>
                            <sort_order>170</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </allowed_methods>
                        <shipment_requesttype translate="label">
                            <label>Packages Request Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_abstract_source_requesttype</source_model>
                            <sort_order>47</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </shipment_requesttype>
                        <container translate="label">
                            <label>Container</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_ups_source_container</source_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </container>
                        <free_shipping_enable translate="label">
                            <label>Free Shipping with Minimum Order Amount</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_enabledisable</source_model>
                            <sort_order>210</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_enable>
                        <free_shipping_subtotal translate="label">
                            <label>Minimum Order Amount for Free Shipping</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>220</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_subtotal>
                        <dest_type translate="label">
                            <label>Destination Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_ups_source_destType</source_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </dest_type>
                        <free_method translate="label">
                            <label>Free Method</label>
                            <frontend_type>select</frontend_type>
                            <frontend_class>free-method</frontend_class>
                            <source_model>usa/shipping_carrier_ups_source_freemethod</source_model>
                            <backend_model>usa/shipping_carrier_ups_backend_freemethod</backend_model>
                            <sort_order>200</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_method>
                        <gateway_url translate="label">
                            <label>Gateway URL</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </gateway_url>
                        <verify_peer translate="label">
                            <label>Enable SSL Verification</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>45</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </verify_peer>
                        <gateway_xml_url translate="label">
                            <label>Gateway XML URL</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>adminhtml/system_config_backend_gatewayurl</backend_model>
                            <sort_order>22</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </gateway_xml_url>
                        <tracking_xml_url translate="label">
                            <label>Tracking XML URL</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>adminhtml/system_config_backend_gatewayurl</backend_model>
                            <sort_order>24</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </tracking_xml_url>
                        <shipconfirm_xml_url translate="label">
                            <label>Shipping Confirm XML URL</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>adminhtml/system_config_backend_gatewayurl</backend_model>
                            <sort_order>26</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </shipconfirm_xml_url>
                        <shipaccept_xml_url translate="label">
                            <label>Shipping Accept XML URL</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>adminhtml/system_config_backend_gatewayurl</backend_model>
                            <sort_order>28</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </shipaccept_xml_url>
                        <handling_type translate="label">
                            <label>Calculate Handling Fee</label>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingType</source_model>
                            <sort_order>110</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_type>
                        <handling_action translate="label">
                            <label>Handling Applied</label>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingAction</source_model>
                            <sort_order>120</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_action>
                        <handling_fee translate="label">
                            <label>Handling Fee</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>130</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_fee>
                        <max_package_weight translate="label">
                            <label>Maximum Package Weight (Please consult your shipping carrier for maximum supported shipping weight)</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </max_package_weight>
                        <min_package_weight translate="label">
                            <label>Minimum Package Weight (Please consult your shipping carrier for minimum supported shipping weight)</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </min_package_weight>
                        <origin_shipment translate="label">
                            <label>Origin of the Shipment</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_ups_source_originShipment</source_model>
                            <backend_model>usa/shipping_carrier_ups_backend_originShipment</backend_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </origin_shipment>
                        <password translate="label">
                            <label>Password</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </password>
                        <pickup translate="label">
                            <label>Pickup Method</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_ups_source_pickup</source_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </pickup>
                        <sort_order translate="label">
                            <label>Sort Order</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>1000</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sort_order>
                        <title translate="label">
                            <label>Title</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </title>
                        <type translate="label">
                            <label>UPS Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_ups_source_type</source_model>
                            <backend_model>usa/shipping_carrier_ups_backend_type</backend_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </type>
                        <unit_of_measure translate="label comment">
                            <label>Weight Unit</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_ups_source_unitofmeasure</source_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </unit_of_measure>
                        <username translate="label">
                            <label>User ID</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </username>
                        <negotiated_active translate="label">
                            <label>Enable Negotiated Rates</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </negotiated_active>
                        <shipper_number translate="label comment">
                            <label>Shipper Number</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>Required for negotiated rates; 6-character UPS.</comment>
                        </shipper_number>
                        <sallowspecific translate="label">
                            <label>Ship to Applicable Countries</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>900</sort_order>
                            <frontend_class>shipping-applicable-country</frontend_class>
                            <source_model>adminhtml/system_config_source_shipping_allspecificcountries</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sallowspecific>
                        <specificcountry translate="label">
                            <label>Ship to Specific Countries</label>
                            <frontend_type>multiselect</frontend_type>
                            <sort_order>910</sort_order>
                            <source_model>adminhtml/system_config_source_country</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </specificcountry>
                        <showmethod translate="label">
                            <label>Show Method if Not Applicable</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>920</sort_order>
                            <frontend_class>shipping-skip-hide</frontend_class>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </showmethod>
                        <specificerrmsg translate="label">
                            <label>Displayed Error Message</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>800</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </specificerrmsg>
                        <mode_xml translate="label comment">
                            <label>Mode</label>
                            <comment>Enables/Disables SSL verification of Magento server by UPS.</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_abstract_source_mode</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </mode_xml>
                        <debug translate="label">
                            <label>Debug</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>920</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </debug>
                    </fields>
                </ups>
                <usps translate="label" module="usa">
                    <label>USPS</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>110</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <active translate="label">
                            <label>Enabled for Checkout</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </active>
                        <gateway_url translate="label">
                            <label>Gateway URL</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </gateway_url>
                        <gateway_secure_url translate="label">
                            <label>Secure Gateway URL</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </gateway_secure_url>
                        <title translate="label">
                            <label>Title</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </title>
                        <userid translate="label">
                            <label>User ID</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </userid>
                        <password translate="label">
                            <label>Password</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>53</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </password>
                        <mode translate="label">
                            <label>Mode</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>54</sort_order>
                            <source_model>usa/shipping_carrier_abstract_source_mode</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </mode>
                        <shipment_requesttype translate="label">
                            <label>Packages Request Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_abstract_source_requesttype</source_model>
                            <sort_order>55</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </shipment_requesttype>
                        <container translate="label">
                            <label>Container</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_usps_source_container</source_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </container>
                        <size translate="label">
                            <label>Size</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_usps_source_size</source_model>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </size>
                        <width translate="label">
                            <label>Width</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>73</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends><size>LARGE</size></depends>
                        </width>
                        <length translate="label">
                            <label>Length</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>72</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends><size>LARGE</size></depends>
                        </length>
                        <height translate="label">
                            <label>Height</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>74</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends><size>LARGE</size></depends>
                        </height>
                        <girth translate="label">
                            <label>Girth</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>76</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends><size>LARGE</size></depends>
                        </girth>
                        <machinable translate="label">
                            <label>Machinable</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_usps_source_machinable</source_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </machinable>
                        <max_package_weight translate="label">
                            <label>Maximum Package Weight (Please consult your shipping carrier for maximum supported shipping weight)</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </max_package_weight>
                        <handling_type translate="label">
                            <label>Calculate Handling Fee</label>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingType</source_model>
                            <sort_order>100</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_type>
                        <handling_action translate="label">
                            <label>Handling Applied</label>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingAction</source_model>
                            <sort_order>110</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_action>
                        <handling_fee translate="label">
                            <label>Handling Fee</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>120</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_fee>
                        <allowed_methods translate="label">
                            <label>Allowed Methods</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>usa/shipping_carrier_usps_source_method</source_model>
                            <sort_order>130</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </allowed_methods>
                        <free_method translate="label">
                            <label>Free Method</label>
                            <frontend_type>select</frontend_type>
                            <frontend_class>free-method</frontend_class>
                            <source_model>usa/shipping_carrier_usps_source_freemethod</source_model>
                            <sort_order>140</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_method>
                        <free_shipping_enable translate="label">
                            <label>Free Shipping with Minimum Order Amount</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_enabledisable</source_model>
                            <sort_order>1500</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_enable>
                        <free_shipping_subtotal translate="label">
                            <label>Minimum Order Amount for Free Shipping</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>160</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_subtotal>
                        <specificerrmsg translate="label">
                            <label>Displayed Error Message</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>170</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </specificerrmsg>
                        <sallowspecific translate="label">
                            <label>Ship to Applicable Countries</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>180</sort_order>
                            <frontend_class>shipping-applicable-country</frontend_class>
                            <source_model>adminhtml/system_config_source_shipping_allspecificcountries</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sallowspecific>
                        <specificcountry translate="label">
                            <label>Ship to Specific Countries</label>
                            <frontend_type>multiselect</frontend_type>
                            <sort_order>190</sort_order>
                            <source_model>adminhtml/system_config_source_country</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </specificcountry>
                        <debug translate="label">
                            <label>Debug</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>200</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </debug>
                        <showmethod translate="label">
                            <label>Show Method if Not Applicable</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>210</sort_order>
                            <frontend_class>shipping-skip-hide</frontend_class>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </showmethod>
                        <sort_order translate="label">
                            <label>Sort Order</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>220</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        0</sort_order>
                    </fields>
                </usps>
                <dhlint translate="label" module="usa">
                    <label>DHL</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>140</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <active translate="label">
                            <label>Enabled for Checkout</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </active>
                        <gateway_url translate="label">
                            <label>Gateway URL</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </gateway_url>
                        <verify_peer translate="label">
                            <label>Enable SSL Verification</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </verify_peer>
                        <title translate="label">
                            <label>Title</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </title>
                        <id translate="label">
                            <label>Access ID</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </id>
                        <password translate="label">
                            <label>Password</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </password>
                        <account translate="label">
                            <label>Account Number</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </account>
                        <content_type translate="label">
                            <label>Content Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_international_source_contenttype</source_model>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </content_type>
                        <handling_type translate="label">
                            <label>Calculate Handling Fee</label>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingType</source_model>
                            <sort_order>100</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_type>
                        <handling_action translate="label comment">
                            <label>Handling Applied</label>
                            <comment>"Per Order" allows single handling fee for entire order. "Per Package" allows individual  handling fee for each package.</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>shipping/source_handlingAction</source_model>
                            <sort_order>110</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_action>
                        <handling_fee translate="label">
                            <label>Handling Fee</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>120</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </handling_fee>
                        <divide_order_weight translate="label comment">
                            <label>Divide Order Weight</label>
                            <comment>Allows breaking total order weight into smaller pieces if it exeeds 70 kg to ensure accurate calculation of shipping charges.</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>130</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </divide_order_weight>
                        <unit_of_measure translate="label">
                            <label>Weight Unit</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_international_source_method_unitofmeasure</source_model>
                            <frontend_model>usa/adminhtml_dhl_unitofmeasure</frontend_model>
                            <sort_order>140</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </unit_of_measure>
                        <size translate="label">
                            <label>Size</label>
                            <frontend_type>select</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_international_source_method_size</source_model>
                            <sort_order>150</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </size>
                        <height translate="label">
                            <label>Height</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>151</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><size>1</size></depends>
                        </height>
                        <depth translate="label">
                            <label>Depth</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>152</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><size>1</size></depends>
                        </depth>
                        <width translate="label">
                            <label>Width</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>153</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><size>1</size></depends>
                        </width>
                        <doc_methods translate="label">
                            <label>Allowed Methods</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_international_source_method_doc</source_model>
                            <sort_order>170</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends><content_type>D</content_type></depends>
                        </doc_methods>
                        <nondoc_methods translate="label">
                            <label>Allowed Methods</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>usa/shipping_carrier_dhl_international_source_method_nondoc</source_model>
                            <sort_order>170</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends><content_type>N</content_type></depends>
                        </nondoc_methods>
                        <ready_time>
                            <label>Ready time</label>
                            <comment>Package ready time after order submission (in hours)</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>180</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </ready_time>
                        <specificerrmsg translate="label">
                            <label>Displayed Error Message</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>800</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </specificerrmsg>
                        <!--
                        If the free_shipping_enable flag enable, the system will check free_shipping_subtotal to give free shipping
                        otherwise will use shopping cart price rule behaviour
                        -->
                        <free_method_doc translate="label">
                            <label>Free Method</label>
                            <frontend_type>select</frontend_type>
                            <frontend_class>free-method</frontend_class>
                            <source_model>usa/shipping_carrier_dhl_international_source_method_freedoc</source_model>
                            <sort_order>1200</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends><content_type>D</content_type></depends>
                        </free_method_doc>
                        <free_method_nondoc translate="label">
                            <label>Free Method</label>
                            <frontend_type>select</frontend_type>
                            <frontend_class>free-method</frontend_class>
                            <source_model>usa/shipping_carrier_dhl_international_source_method_freenondoc</source_model>
                            <sort_order>1200</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends><content_type>N</content_type></depends>
                        </free_method_nondoc>
                        <free_shipping_enable translate="label">
                            <label>Free Shipping with Minimum Order Amount</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_enabledisable</source_model>
                            <sort_order>1210</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_enable>
                        <free_shipping_subtotal translate="label">
                            <label>Minimum Order Amount for Free Shipping</label>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>1220</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </free_shipping_subtotal>
                        <sallowspecific translate="label">
                            <label>Ship to Applicable Countries</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>1900</sort_order>
                            <frontend_class>shipping-applicable-country</frontend_class>
                            <source_model>adminhtml/system_config_source_shipping_allspecificcountries</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sallowspecific>
                        <specificcountry translate="label">
                            <label>Ship to Specific Countries</label>
                            <frontend_type>multiselect</frontend_type>
                            <sort_order>1910</sort_order>
                            <source_model>adminhtml/system_config_source_country</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </specificcountry>
                        <showmethod translate="label">
                            <label>Show Method if Not Applicable</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>1940</sort_order>
                            <frontend_class>shipping-skip-hide</frontend_class>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </showmethod>
                        <sort_order translate="label">
                            <label>Sort Order</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>2000</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </sort_order>
                        <debug translate="label">
                            <label>Debug</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>1950</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </debug>
                    </fields>
                </dhlint>
            </groups>
        </carriers>
    </sections>
</config>
