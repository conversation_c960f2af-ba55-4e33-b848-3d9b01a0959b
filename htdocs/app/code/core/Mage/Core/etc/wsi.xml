<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:typens="urn:{{var wsdl.name}}"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
             xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
             name="{{var wsdl.name}}"
             targetNamespace="urn:{{var wsdl.name}}">
    <wsdl:types>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:{{var wsdl.name}}">
            <xsd:complexType name="storeEntity">
                <xsd:sequence>
                    <xsd:element name="store_id" type="xsd:int" />
                    <xsd:element name="code" type="xsd:string" />
                    <xsd:element name="website_id" type="xsd:int" />
                    <xsd:element name="group_id" type="xsd:int" />
                    <xsd:element name="name" type="xsd:string" />
                    <xsd:element name="sort_order" type="xsd:int" />
                    <xsd:element name="is_active" type="xsd:int" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="storeEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:storeEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="magentoInfoEntity">
                <xsd:sequence>
                    <xsd:element minOccurs="1" maxOccurs="1" name="magento_version" type="xsd:string" />
                    <xsd:element minOccurs="1" maxOccurs="1" name="magento_edition" type="xsd:string" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:element name="storeListRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="storeListResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:storeEntityArray" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="storeInfoRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="storeId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="storeInfoResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:storeEntity" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="magentoInfoRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="magentoInfoResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:magentoInfoEntity" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="storeListRequest">
        <wsdl:part name="parameters" element="typens:storeListRequestParam" />
    </wsdl:message>
    <wsdl:message name="storeListResponse">
        <wsdl:part name="parameters" element="typens:storeListResponseParam" />
    </wsdl:message>
    <wsdl:message name="storeInfoRequest">
        <wsdl:part name="parameters" element="typens:storeInfoRequestParam" />
    </wsdl:message>
    <wsdl:message name="storeInfoResponse">
        <wsdl:part name="parameters" element="typens:storeInfoResponseParam" />
    </wsdl:message>
    <wsdl:message name="magentoInfoRequest">
        <wsdl:part name="parameters" element="typens:magentoInfoRequestParam" />
    </wsdl:message>
    <wsdl:message name="magentoInfoResponse">
        <wsdl:part name="parameters" element="typens:magentoInfoResponseParam" />
    </wsdl:message>
    <wsdl:portType name="{{var wsdl.handler}}PortType">
        <wsdl:operation name="storeList">
            <wsdl:documentation>List of stores</wsdl:documentation>
            <wsdl:input message="typens:storeListRequest" />
            <wsdl:output message="typens:storeListResponse" />
        </wsdl:operation>
        <wsdl:operation name="storeInfo">
            <wsdl:documentation>Store view info</wsdl:documentation>
            <wsdl:input message="typens:storeInfoRequest" />
            <wsdl:output message="typens:storeInfoResponse" />
        </wsdl:operation>
        <wsdl:operation name="magentoInfo">
            <wsdl:documentation>Info about current Magento installation</wsdl:documentation>
            <wsdl:input message="typens:magentoInfoRequest" />
            <wsdl:output message="typens:magentoInfoResponse" />
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="{{var wsdl.handler}}Binding" type="typens:{{var wsdl.handler}}PortType">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="storeList">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="storeInfo">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="magentoInfo">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="{{var wsdl.name}}Service">
        <wsdl:port name="{{var wsdl.handler}}Port" binding="typens:{{var wsdl.handler}}Binding">
            <soap:address location="{{var wsdl.url}}" />
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
