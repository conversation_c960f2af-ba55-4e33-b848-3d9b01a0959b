<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Tag
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <menu>
        <catalog>
            <children>
                <tag translate="title" module="tag">
                    <title>Tags</title>
                    <children>
                        <!--
                            childrens should be in the order (All Tags, Pending Tags)
                            for correct menu building in "None Secret Key" mode
                        -->
                        <all translate="title" module="tag">
                            <title>All Tags</title>
                            <action>adminhtml/tag/index</action>
                        </all>
                        <pending translate="title" module="tag">
                            <title>Pending Tags</title>
                            <action>adminhtml/tag/pending</action>
                        </pending>
                    </children>
                    <sort_order>60</sort_order>
                </tag>
             </children>
        </catalog>
        <report translate="title" module="reports">
            <children>
                <tags translate="title" module="tag">
                    <title>Tags</title>
                    <sort_order>50</sort_order>
                    <children>
                        <customer translate="title" module="tag">
                            <title>Customers</title>
                            <action>adminhtml/report_tag/customer</action>
                        </customer>
                        <product translate="title" module="tag">
                            <title>Products</title>
                            <action>adminhtml/report_tag/product</action>
                        </product>
                        <popular translate="title" module="tag">
                            <title>Popular</title>
                            <action>adminhtml/report_tag/popular</action>
                        </popular>
                    </children>
                </tags>
            </children>
        </report>
    </menu>
    <acl>
        <resources>
            <admin>
                <children>
                    <catalog>
                        <children>
                            <tag translate="title" module="tag">
                                <title>Tags</title>
                                <children>
                                    <all translate="title">
                                        <title>All Tags</title>
                                    </all>
                                    <pending translate="title">
                                        <title>Pending Tags</title>
                                    </pending>
                                </children>
                            </tag>
                        </children>
                    </catalog>
                </children>
            </admin>
        </resources>
    </acl>
</config>
