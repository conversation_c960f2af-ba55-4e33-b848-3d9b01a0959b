<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns:typens="urn:{{var wsdl.name}}" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
    xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns="http://schemas.xmlsoap.org/wsdl/"
    name="{{var wsdl.name}}" targetNamespace="urn:{{var wsdl.name}}">
    <types>
        <schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:Magento">
            <import namespace="http://schemas.xmlsoap.org/soap/encoding/" schemaLocation="http://schemas.xmlsoap.org/soap/encoding/" />
            <complexType name="catalogProductTagListEntity">
                <all>
                    <element name="tag_id" type="xsd:string" minOccurs="1" />
                    <element name="name" type="xsd:string" minOccurs="1" />
                </all>
            </complexType>
            <complexType name="catalogProductTagListEntityArray">
                <complexContent>
                    <restriction base="soapenc:Array">
                        <attribute ref="soapenc:arrayType" wsdl:arrayType="typens:catalogProductTagListEntity[]" />
                    </restriction>
                </complexContent>
            </complexType>
            <complexType name="catalogProductTagAddEntity">
                <all>
                    <element name="tag" type="xsd:string" minOccurs="1" />
                    <element name="product_id" type="xsd:string" minOccurs="1" />
                    <element name="customer_id" type="xsd:string" minOccurs="1" />
                    <element name="store" type="xsd:string" minOccurs="1" />
                </all>
            </complexType>
            <complexType name="catalogProductTagUpdateEntity">
                <all>
                    <element name="name" type="xsd:string" minOccurs="0" />
                    <element name="status" type="xsd:string" minOccurs="0" />
                    <element name="base_popularity" type="xsd:string" minOccurs="0" />
                </all>
            </complexType>
            <complexType name="catalogProductTagInfoEntity">
                <all>
                    <element name="name" type="xsd:string" minOccurs="1" />
                    <element name="status" type="xsd:string" minOccurs="1" />
                    <element name="base_popularity" type="xsd:string" minOccurs="1" />
                    <element name="products" type="typens:associativeArray" minOccurs="1" />
                </all>
            </complexType>
        </schema>
    </types>

    <message name="catalogProductTagListRequest">
        <part name="sessionId" type="xsd:string" />
        <part name="productId" type="xsd:string" />
        <part name="store" type="xsd:string" />
    </message>
    <message name="catalogProductTagListResponse">
        <part name="result" type="typens:catalogProductTagListEntityArray" />
    </message>
    <message name="catalogProductTagInfoRequest">
        <part name="sessionId" type="xsd:string" />
        <part name="tagId" type="xsd:string" />
        <part name="store" type="xsd:string" />
    </message>
    <message name="catalogProductTagInfoResponse">
        <part name="result" type="typens:catalogProductTagInfoEntity" />
    </message>
    <message name="catalogProductTagAddRequest">
        <part name="sessionId" type="xsd:string" />
        <part name="data" type="typens:catalogProductTagAddEntity" />
    </message>
    <message name="catalogProductTagAddResponse">
        <part name="result" type="typens:associativeArray" />
    </message>
    <message name="catalogProductTagUpdateRequest">
        <part name="sessionId" type="xsd:string" />
        <part name="tagId" type="xsd:string" />
        <part name="data" type="typens:catalogProductTagUpdateEntity" />
        <part name="store" type="xsd:string" />
    </message>
    <message name="catalogProductTagUpdateResponse">
        <part name="result" type="xsd:boolean" />
    </message>
    <message name="catalogProductTagRemoveRequest">
        <part name="sessionId" type="xsd:string" />
        <part name="tagId" type="xsd:string" />
    </message>
    <message name="catalogProductTagRemoveResponse">
        <part name="result" type="xsd:boolean" />
    </message>

    <portType name="{{var wsdl.handler}}PortType">
        <operation name="catalogProductTagList">
            <documentation>Retrieve list of tags by product</documentation>
            <input message="typens:catalogProductTagListRequest" />
            <output message="typens:catalogProductTagListResponse" />
        </operation>
    </portType>
    <portType name="{{var wsdl.handler}}PortType">
        <operation name="catalogProductTagInfo">
            <documentation>Retrieve product tag info</documentation>
            <input message="typens:catalogProductTagInfoRequest" />
            <output message="typens:catalogProductTagInfoResponse" />
        </operation>
    </portType>
    <portType name="{{var wsdl.handler}}PortType">
        <operation name="catalogProductTagAdd">
            <documentation>Add tag(s) to product</documentation>
            <input message="typens:catalogProductTagAddRequest" />
            <output message="typens:catalogProductTagAddResponse" />
        </operation>
    </portType>
    <portType name="{{var wsdl.handler}}PortType">
        <operation name="catalogProductTagUpdate">
            <documentation>Update product tag</documentation>
            <input message="typens:catalogProductTagUpdateRequest" />
            <output message="typens:catalogProductTagUpdateResponse" />
        </operation>
    </portType>
    <portType name="{{var wsdl.handler}}PortType">
        <operation name="catalogProductTagRemove">
            <documentation>Remove product tag</documentation>
            <input message="typens:catalogProductTagRemoveRequest" />
            <output message="typens:catalogProductTagRemoveResponse" />
        </operation>
    </portType>

    <binding name="{{var wsdl.handler}}Binding" type="typens:{{var wsdl.handler}}PortType">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="catalogProductTagList">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action" />
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </output>
        </operation>
    </binding>
    <binding name="{{var wsdl.handler}}Binding" type="typens:{{var wsdl.handler}}PortType">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="catalogProductTagInfo">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action" />
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </output>
        </operation>
    </binding>
    <binding name="{{var wsdl.handler}}Binding" type="typens:{{var wsdl.handler}}PortType">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="catalogProductTagAdd">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action" />
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </output>
        </operation>
    </binding>
    <binding name="{{var wsdl.handler}}Binding" type="typens:{{var wsdl.handler}}PortType">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="catalogProductTagUpdate">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action" />
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </output>
        </operation>
    </binding>
    <binding name="{{var wsdl.handler}}Binding" type="typens:{{var wsdl.handler}}PortType">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http" />
        <operation name="catalogProductTagRemove">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action" />
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" />
            </output>
        </operation>
    </binding>

    <service name="{{var wsdl.name}}Service">
        <port name="{{var wsdl.handler}}Port" binding="typens:{{var wsdl.handler}}Binding">
            <soap:address location="{{var wsdl.url}}" />
        </port>
    </service>
</definitions>
