<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Rss
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_Rss>
            <version>1.6.0.0</version>
        </Mage_Rss>
    </modules>
    <global>
        <models>
            <rss>
                <class>Mage_Rss_Model</class>
                <resourceModel>rss_resource</resourceModel>
            </rss>
            <rss_resource>
                <class>Mage_Rss_Model_Resource</class>
                <deprecatedNode>rss_mysql4</deprecatedNode>
            </rss_resource>
        </models>
        <blocks>
            <rss>
                <class>Mage_Rss_Block</class>
            </rss>
        </blocks>
        <resources>
            <rss_setup>
                <setup>
                    <module>Mage_Rss</module>
                </setup>
            </rss_setup>
        </resources>
    </global>
    <adminhtml>
        <translate>
            <modules>
                <Mage_Rss>
                    <files>
                        <default>Mage_Rss.csv</default>
                    </files>
                </Mage_Rss>
            </modules>
        </translate>
        <layout>
            <updates>
                <rss>
                    <file>rss.xml</file>
                </rss>
            </updates>
        </layout>
    </adminhtml>
    <frontend>
        <events>
            <sales_order_save_after>
                <observers>
                    <notifystock>
                        <class>rss/observer</class>
                        <method>salesOrderItemSaveAfterNotifyStock</method>
                    </notifystock>
                </observers>
            </sales_order_save_after>
            <sales_order_save_after>
                <observers>
                    <ordernew>
                        <class>rss/observer</class>
                        <method>salesOrderItemSaveAfterOrderNew</method>
                    </ordernew>
                </observers>
            </sales_order_save_after>
        </events>
        <translate>
            <modules>
                <Mage_Rss>
                    <files>
                        <default>Mage_Rss.csv</default>
                    </files>
                </Mage_Rss>
            </modules>
        </translate>
        <secure_url>
            <rss_catalog_review>/rss/catalog/review</rss_catalog_review>
            <rss_order_new>/rss/order/new</rss_order_new>
            <rss_catalog_notifystock>/rss/catalog/notifystock</rss_catalog_notifystock>
        </secure_url>
        <routers>
            <rss>
                <use>standard</use>
                <args>
                    <module>Mage_Rss</module>
                    <frontName>rss</frontName>
                </args>
            </rss>
        </routers>
        <layout>
            <updates>
                <rss>
                    <file>rss.xml</file>
                </rss>
            </updates>
        </layout>
    </frontend>
    <default>
        <validators>
            <custom_layout>
                <disallowed_block>
                    <Mage_Rss_Block_Order_New/>
                </disallowed_block>
            </custom_layout>
        </validators>
    </default>
</config>
