<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php $_divId = 'tree-div_' . time() ?>
<div id="<?php echo $_divId ?>" class="tree"></div>

<script type="text/javascript">
//<![CDATA[

// TODO: cleanup this script. It was copypasted from catalog/category/tree

var tree;

/**
 * Fix ext compatibility with prototype 1.6
 */
Ext.lib.Event.getTarget = function(e) {
    var ee = e.browserEvent || e;
    return ee.target ? Event.element(ee) : null;
};

Ext.tree.TreePanel.Enhanced = function(el, config)
{
    Ext.tree.TreePanel.Enhanced.superclass.constructor.call(this, el, config);
};

Ext.extend(Ext.tree.TreePanel.Enhanced, Ext.tree.TreePanel, {

    loadTree : function(config, firstLoad)
    {
        var parameters = config['parameters'];
        var data = config['data'];

        if ((typeof parameters['root_visible']) != 'undefined') {
            this.rootVisible = parameters['root_visible']*1;
        }

        var root = new Ext.tree.TreeNode(parameters);

        this.nodeHash = {};
        this.setRootNode(root);

        if (firstLoad) {
            this.addListener('click', this.categoryClick.createDelegate(this));
        }

        this.loader.buildCategoryTree(root, data);
        this.el.dom.innerHTML = '';
        // render the tree
        this.render();
    },

    categoryClick : function(node, e)
    {
        node.getUI().check(!node.getUI().checked());
    }
});

Ext.onReady(function()
{
    var categoryLoader = new Ext.tree.TreeLoader({
       dataUrl: '<?php echo $this->getLoadTreeUrl() ?>'
    });

    categoryLoader.createNode = function(config) {
        config.uiProvider = Ext.tree.CheckboxNodeUI;
        var node;
        var _node = Object.clone(config);
        if (config.children && !config.children.length) {
            delete(config.children);
            node = new Ext.tree.AsyncTreeNode(config);
        } else {
            node = new Ext.tree.TreeNode(config);
        }

        return node;
    };

    categoryLoader.buildCategoryTree = function(parent, config)
    {
        if (!config) return null;

        if (parent && config && config.length){
            for (var i = 0; i < config.length; i++) {
                config[i].uiProvider = Ext.tree.CheckboxNodeUI;
                var node;
                var _node = Object.clone(config[i]);
                if (_node.children && !_node.children.length) {
                    delete(_node.children);
                    node = new Ext.tree.AsyncTreeNode(_node);
                } else {
                    node = new Ext.tree.TreeNode(config[i]);
                }
                parent.appendChild(node);
                node.loader = node.getOwnerTree().loader;
                if (_node.children) {
                    this.buildCategoryTree(node, _node.children);
                }
            }
        }
    };

    categoryLoader.buildHash = function(node)
    {
        var hash = {};

        hash = this.toArray(node.attributes);

        if (node.childNodes.length>0 || (node.loaded==false && node.loading==false)) {
            hash['children'] = new Array;

            for (var i = 0, len = node.childNodes.length; i < len; i++) {
                if (!hash['children']) {
                    hash['children'] = new Array;
                }
                hash['children'].push(this.buildHash(node.childNodes[i]));
            }
        }

        return hash;
    };

    categoryLoader.toArray = function(attributes) {
        var data = {};
        for (var key in attributes) {
            var value = attributes[key];
            data[key] = value;
        }

        return data;
    };

    categoryLoader.on("beforeload", function(treeLoader, node) {
        treeLoader.baseParams.id = node.attributes.id;
    });

    categoryLoader.on("load", function(treeLoader, node, config) {
        varienWindowOnload();
    });

    tree = new Ext.tree.TreePanel.Enhanced('<?php echo $_divId ?>', {
        animate:          false,
        loader:           categoryLoader,
        enableDD:         false,
        containerScroll:  true,
        selModel:         new Ext.tree.CheckNodeMultiSelectionModel(),
        rootVisible:      '<?php echo $this->getRoot()->getIsVisible() ?>',
        useAjax:          <?php echo $this->getUseAjax() ?>,
        currentNodeId:    <?php echo (int) $this->getCategoryId() ?>,
        addNodeTo:        false,
        rootUIProvider:   Ext.tree.CheckboxNodeUI
    });

    tree.on('check', function(node, checked) {
        <?php echo $this->getJsFormObject() ?>.updateElement.value = this.getChecked().join(', ');
        varienElementMethods.setHasChanges(node.getUI().checkbox);
    }, tree);

    // set the root node
    var parameters = {
        text:        '<?php echo htmlentities($this->getRoot()->getName()) ?>',
        draggable:   false,
        checked:'<?php echo $this->getRoot()->getChecked() ?>',
        uiProvider: Ext.tree.CheckboxNodeUI,
        allowDrop:   <?php if ($this->getRoot()->getIsVisible()): ?>true<?php else : ?>false<?php endif; ?>,
        id:          <?php echo (int) $this->getRoot()->getId() ?>,
        expanded:    <?php echo (int) $this->getIsWasExpanded() ?>,
        category_id: <?php echo (int) $this->getCategoryId() ?>
    };

    tree.loadTree({parameters:parameters, data:<?php echo $this->getTreeJson() ?>},true);

});
//]]>
</script>
