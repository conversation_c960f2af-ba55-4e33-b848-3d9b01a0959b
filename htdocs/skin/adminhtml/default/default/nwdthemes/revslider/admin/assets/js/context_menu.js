var tpLayerContextMenu;!function(e){tpLayerContextMenu=new function(){function t(e){var t=0,a=0;if(!e)var e=window.event;return e.pageX||e.pageY?(t=e.pageX,a=e.pageY):(e.clientX||e.clientY)&&(t=e.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,a=e.clientY+document.body.scrollTop+document.documentElement.scrollTop),{x:t,y:a}}function a(){s(),l(),n(),c()}function s(){document.addEventListener("contextmenu",function(t){h=V.clickInsideElement(t,"slide_layer"),e(h).hasClass("demo_layer")||(h?(V.setLayerSelected(e(h).data("serial")),t.preventDefault(),C.toggleMenuOn("layer"),_(t)):(h=V.clickInsideElement(t,"bg_context_listener"))?(t.preventDefault(),C<PERSON>toggleMenuOn("background"),_(t)):(h=V.clickInsideElement(t,"ignorecontextmenu"))?(t.preventDefault(),C.toggleMenuOn(),_(t)):(h=null,C.toggleMenuOff()))})}function l(){e("body").on("click",function(e){var t=V.clickInsideElement(e,S);t?(e.preventDefault(),b(t)):1===(e.which||e.button)&&C.toggleMenuOff()})}function n(){window.onkeyup=function(e){27===e.keyCode&&C.toggleMenuOff()}}function c(){window.onresize=function(e){C.toggleMenuOff()}}function o(e){var t="rs-icon-layerimage_n";switch(e){case"image":t="rs-icon-layerimage_n";break;case"text":t="rs-icon-layerfont_n";break;case"button":t="rs-icon-layerbutton_n";break;case"audio":t="rs-icon-layeraudio_n";break;case"video":t="rs-icon-layervideo_n";break;case"svg":t="rs-icon-layersvg_n";break;case"row":t="rs-icon-layergroup_n";break;case"column":t="rs-icon-layercolumn_n";break;case"group":t="rs-icon-layergroup_n"}return t}function r(t,a){t?e(a).removeClass("ctx-td-s-off"):e(a).addClass("ctx-td-s-off")}function d(){e(".not_in_ctx_bg").hide(),e(".not_in_ctx_layer").show();var t=e("#ctx_list_of_layers");t.html("");for(i in V.arrLayers)if(V.arrLayers.hasOwnProperty(i)){var a=V.arrLayers[i],s=o(a.type);t.append('<li class="ctx-dyn-avl-list-item context-menu__item" data-serial="'+a.serial+'"><div class="ctx_item_inner"><div class="context-menu__link" data-serial="'+a.serial+'" data-action="selectonelayer"><i class="'+s+'"></i><span class="cx-layer-name">'+a.alias+"</span></div></div></li>")}}function y(){var t=V.getLayer(V.selectedLayerSerial),a=t.references.htmlLayer;e(".not_in_ctx_bg").show(),e(".not_in_ctx_layer").hide(),e("#cx-selected-layer-name").html(t.alias),e("#cx-selected-layer-icon").attr("class",o(t.type));var s=e("#cx-selected-layer-visible"),l=e("#cx-selected-layer-locked");O.isLayerVisible(a)?(s.find("i").attr("class","eg-icon-eye-off"),s.find(".cx-layer-name").html("Hide Layer")):(s.find("i").attr("class","eg-icon-eye"),s.find(".cx-layer-name").html("Show Layer")),O.isLayerLocked(a)?(l.find("i").attr("class","eg-icon-lock-open"),l.find(".cx-layer-name").html("Unlock Layer")):(l.find("i").attr("class","eg-icon-lock"),l.find(".cx-layer-name").html("Lock Layer"));var i=e("#ctx_list_of_invisibles"),n=e("#ctx_list_of_layers"),c=!0;i.find(".ctx-dyn-inv-list-item").remove(),n.html("");for(h in V.arrLayers)if(V.arrLayers.hasOwnProperty(h)){var d=o((a=V.arrLayers[h]).type),y=void 0!==a.references&&void 0!==a.references.htmlLayer?a.references.htmlLayer:void 0;if(void 0!==y&&y.hasClass("currently_not_visible")){var _=c?"ctx-m-top-divider":"";c=!1,i.append('<li class="ctx-dyn-inv-list-item '+_+' context-menu__item"><div class="ctx_item_inner"><div class="context-menu__link" data-serial="'+a.serial+'" data-action="showonelayer"><i class="eg-icon-eye"></i><span class="cx-layer-name">'+a.alias+"</span></div></div></li>")}n.append('<li class="ctx-dyn-avl-list-item context-menu__item" data-serial="'+a.serial+'"><div class="ctx_item_inner"><div class="context-menu__link" data-serial="'+a.serial+'" data-action="selectonelayer"><i class="'+d+'"></i><span class="cx-layer-name">'+a.alias+"</span></div></div></li>")}"grid"===t.basealign?(e("#ctx_gridbased").addClass("selected"),e("#ctx_slidebased").removeClass("selected")):(e("#ctx_gridbased").removeClass("selected"),e("#ctx_slidebased").addClass("selected")),r(t["resize-full"],"#ctx_autoresponsive"),r(t.resizeme,"#ctx_childrenresponsive"),r(t.responsive_offset,"#ctx_responsiveoffset"),r(t["visible-desktop"],"#ctx_showhideondesktop"),r(t["visible-notebook"],"#ctx_showhideonnotebook"),r(t["visible-tablet"],"#ctx_showhideontablet"),r(t["visible-mobile"],"#ctx_showhideonmobile"),r(t.autolinebreak,"#ctx_linebreak"),r(t.scaleProportional,"#ctx_keepaspect"),t.displaymode||"block"===t.display?(e("#ctx_displayblock").addClass("selected"),e("#ctx_displayinline").removeClass("selected")):(e("#ctx_displayblock").removeClass("selected"),e("#ctx_displayinline").addClass("selected")),e("#ctx-list-of-layer-links").data("uniqueid",t.unique_id);for(var u=void 0!==t.groupLink?t.groupLink:0,b=e("#ctx-layer-link-type-element-cs"),h=0;h<6;h++)b.removeClass("ctx-layer-link-type-"+h);b.addClass("ctx-layer-link-type-"+u),e(k).data("current_layer",y);var p="column"===V.getObjLayerType(t.p_uid);switch(k.className="context-menu layer_type_"+t.type+" in_column_"+p,e("#ctx-inheritdesktop").show(),e("#ctx-inheritnotebook").show(),e("#ctx-inherittablet").show(),e("#ctx-inheritmobile").show(),V.getLayout()){case"desktop":e("#ctx-inheritdesktop").hide();break;case"notebook":e("#ctx-inheritnotebook").hide();break;case"tablet":e("#ctx-inherittablet").hide();break;case"mobile":e("#ctx-inheritmobile").hide()}}function _(a){p=t(a),m=p.x,f=p.y;var s=e("#viewWrapper").offset(),l=e("#divbgholder").offset(),i=e(window).scrollTop();x=k.offsetWidth+4,v=k.offsetHeight+4,w=window.innerWidth;var n=i+(L=window.innerHeight),c=15+m-s.left,o=f-(s.top+25);p.x=m-l.left,p.y=f-l.top,c=c+250>w-s.left?w-(260+s.left):c,o=o+300+s.top>n?n-300-s.top:o,k.style.left=c+"px",k.style.top=o+"px",c>w-(s.left+500)?e("#context-menu-first-ul").addClass("submenustoleft"):e("#context-menu-first-ul").removeClass("submenustoleft"),o+300+s.top+200>n?e("#context-menu-first-ul").addClass("submenustobottom"):e("#context-menu-first-ul").removeClass("submenustobottom")}function u(t){return!e(t).hasClass("ctx-td-s-off")}function b(t){var a,s=!0,l={},i=!1,n=!1;if("delegate"!==t.getAttribute("data-action")){switch(t.getAttribute("data-action")){case"delete":e("#button_delete_layer").click();break;case"duplicate":e("#button_duplicate_layer").click();break;case"showhide":e("#layer-short-toolbar .quick-layer-view").click();break;case"lockunlock":e("#layer-short-toolbar .quick-layer-lock").click();break;case"showalllayer":V.showAllLayers();break;case"showonlycurrent":V.hideAllLayers(V.selectedLayerSerial);break;case"showonelayer":var c=V.getLayer(e(t).data("serial"));O.showLayer(c);break;case"selectonelayer":V.setLayerSelected(e(t).data("serial"));break;case"autoresponsive":s=!1,l["resize-full"]=u(t),l["resize-full"]||(l.resizeme=!1,e("#ctx_childrenresponsive").addClass("ctx-td-s-off")),i=!0;break;case"childrenresponsive":s=!1,l.resizeme=u(t),l.resizeme&&(l["resize-full"]=!0,e("#ctx_autoresponsive").removeClass("ctx-td-s-off")),i=!0;break;case"responsiveoffset":s=!1,l.responsive_offset=u(t),i=!0;break;case"gridbased":s=!1,l.basealign="grid",i=!0;break;case"slidebased":s=!1,l.basealign="slide",i=!0;break;case"nothing":s=!1;break;case"linebreak":s=!1,e("#layer_auto_line_break")[0].checked?e("#layer_auto_line_break")[0].checked=!1:e("#layer_auto_line_break")[0].checked=!0,V.clickOnAutoLineBreak();break;case"displayblock":s=!1,l.displaymode=!0,l.display="block",i=!0,a=e(k).data("current_layer"),n=!0;break;case"displayinline":s=!1,l.displaymode=!1,l.display="inline-block",i=!0,a=e(k).data("current_layer"),n=!0;break;case"advancedcss":e("#advanced-css-layer").click();break;case"resetsize":e("#reset-scale").click();break;case"aspectratio":s=!1,e("#layer_proportional_scale")[0].checked?(e("#layer_proportional_scale")[0].checked=!1,e("#ctx_keepaspect").addClass("ctx-td-s-off")):(e("#layer_proportional_scale")[0].checked=!0,e("#ctx_keepaspect").removeClass("ctx-td-s-off")),l.scaleProportional=e("#layer_proportional_scale")[0].checked,i=!0;break;case"copystyle":s=!1;o=V.getLayer(V.selectedLayerSerial);C.stylecache=UniteAdminRev.duplicateObject(o),UniteAdminRev.showInfo({type:"success",content:"Layer Style Successfull Copied to Clipboard",hidedelay:3});break;case"pastestyle":s=!1;o=V.getLayer(V.selectedLayerSerial);!1===e.isEmptyObject(C.stylecache)?(l.deformation=C.stylecache.deformation,l["deformation-hover"]=C.stylecache["deformation-hover"],l.display=C.stylecache.display,l.displaymode=C.stylecache.displaymode,l.margin=C.stylecache.margin,l.autolinebreak=C.stylecache.autolinebreak,l.padding=C.stylecache.padding,l.whitespace=C.stylecache.whitespace,l.static_styles=C.stylecache.static_styles,l["2d_rotation"]=C.stylecache["2d_rotation"],l["image-size"]=C.stylecache["image-size"],i=!0,a=o.references.htmlLayer,n=!0):UniteAdminRev.showInfo({type:"warning",content:"No Style saved to Clipboard",hidedelay:3});break;case"inheritfromdesktop":case"inheritfromnotebook":case"inheritfromtablet":case"inheritfrommobile":var o=V.getLayer(V.selectedLayerSerial),r=t.getAttribute("data-size");(l={}).static_styles={},l.static_styles.color={},l.static_styles["font-size"]={},l.static_styles["line-height"]={},l.static_styles["font-weight"]={},l.padding={},l.margin={},l.max_height={},l.max_width={},l.scaleX={},l.scaleY={},l["text-align"]={},l.vieo_width={},l.video_height={},l.whitespace={},l.static_styles=V.setVal(l.static_styles,"color",V.getVal(o.static_styles,"color",r),!1),l.static_styles=V.setVal(l.static_styles,"font-size",V.getVal(o.static_styles,"font-size",r),!1),l.static_styles=V.setVal(l.static_styles,"line-height",V.getVal(o.static_styles,"line-height",r),!1),l.static_styles=V.setVal(l.static_styles,"font-weight",V.getVal(o.static_styles,"font-weight",r),!1),l=V.setVal(l,"padding",V.getVal(o,"padding",r),!1),l=V.setVal(l,"margin",V.getVal(o,"margin",r),!1),l=V.setVal(l,"max_height",V.getVal(o,"max_height",r),!1),l=V.setVal(l,"max_width",V.getVal(o,"max_width",r),!1),l=V.setVal(l,"scaleX",V.getVal(o,"scaleX",r),!1),l=V.setVal(l,"scaleY",V.getVal(o,"scaleY",r),!1),l=V.setVal(l,"text-align",V.getVal(o,"text-align",r),!1),l=V.setVal(l,"vieo_width",V.getVal(o,"vieo_width",r),!1),l=V.setVal(l,"video_height",V.getVal(o,"video_height",r),!1),l=V.setVal(l,"whitespace",V.getVal(o,"whitespace",r),!1),i=!0,a=o.references.htmlLayer,n=!0;break;case"showhideondesktop":s=!1,l["visible-desktop"]=u(t),i=!0;break;case"showhideonnotebook":s=!1,l["visible-notebook"]=u(t),i=!0;break;case"showhideontablet":s=!1,l["visible-tablet"]=u(t),i=!0;break;case"showhideonmobile":s=!1,l["visible-mobile"]=u(t),i=!0;break;case"grouplinkchange":s=!1,l.groupLink=t.getAttribute("data-linktype");for(var d=(o=V.getLayer(V.selectedLayerSerial)).references.sorttable.layer.find(".layer-link-type-element-cs").first(),y=e("#ctx-layer-link-type-element-cs"),_=0;_<6;_++)d.removeClass("layer-link-type-"+_),y.removeClass("ctx-layer-link-type-"+_);d.addClass("layer-link-type-"+l.groupLink),y.addClass("ctx-layer-link-type-"+l.groupLink);break;case"addtextlayer":V.nextNewLayerToPosition(p),e("#button_add_layer").click();break;case"addimagelayer":V.nextNewLayerToPosition(p),e("#button_add_layer_image").click();break;case"addaudiolayer":V.nextNewLayerToPosition(p),e("#button_add_layer_audio").click();break;case"addvideolayer":V.nextNewLayerToPosition(p),e("#button_add_layer_video").click();break;case"addbuttonlayer":V.nextNewLayerToPosition(p),e("#button_add_layer_button").click();break;case"addshapelayer":V.nextNewLayerToPosition(p),e("#button_add_layer_shape").click();break;case"addobjectlayer":V.nextNewLayerToPosition(p),e("#button_add_layer_svg").click()}!1===e.isEmptyObject(l)&&V.updateLayer(V.selectedLayerSerial,l),i&&V.updateLayerFormFields(V.selectedLayerSerial),n&&O.rebuildLayerIdle(a),s&&C.toggleMenuOff()}else e(document.getElementById(t.getAttribute("data-delegate"))).click()}var h,p,m,f,k,g,x,v,w,L,C=this,V=new Object,O=new Object,S="context-menu__link",z=0;C.stylecache={},C.init=function(){V=UniteLayersRev,O=tpLayerTimelinesRev,k=document.querySelector("#context-menu"),g=k.querySelectorAll(".context-menu__item"),a(),e(".context-submenu").perfectScrollbar({wheelPropagation:!1,suppressScrollX:!0}),e(".context-submenu").each(function(){e(this).on("mouseenter",function(){e(this).perfectScrollbar("update")})}),e("body").on("mouseenter",".ctx-dyn-avl-list-item",function(){e(".layer_due_list_element_selected").removeClass("layer_due_list_element_selected"),e("#slide_layer_"+e(this).data("serial")).addClass("layer_due_list_element_selected")}),e("body").on("mouseleave",".ctx-dyn-avl-list-item",function(){e(".layer_due_list_element_selected").removeClass("layer_due_list_element_selected")}),e("body").on("click",".ctx-td-switcher",function(){e(this).toggleClass("ctx-td-s-off")}),e("body").on("click",".ctx-td-option-selector",function(){var t=e(this);t.parent().find(".selected").removeClass("selected"),t.addClass("selected")})},C.toggleMenuOn=function(e){punchgs.TweenLite.fromTo(k,.2,{x:10,autoAlpha:0},{x:0,autoAlpha:1,display:"block",ease:punchgs.Power2.easeInOut,delay:.2}),1!==z&&(z=1),"layer"===e?-1!=V.selectedLayerSerial&&y():"background"===e&&d()},C.toggleMenuOff=function(){0!==z&&(z=0,punchgs.TweenLite.to(k,.2,{y:25,autoAlpha:0}))}}}($nwd_jQuery);