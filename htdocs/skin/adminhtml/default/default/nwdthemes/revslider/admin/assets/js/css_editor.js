var UniteCssEditorRev;!function(e){UniteCssEditorRev=new function(){var t=this,a=[],n=".tp-caption",r=new Object,s=new Object,i=null,o=null,l=null,c={"background-color":"backgroundColor","border-color":"borderColor","border-radius":"borderRadius","border-style":"borderStyle","border-width":"borderWidth",color:"color","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","line-height":"lineHeight",opacity:"opacity",padding:"padding","text-decoration":"textDecoration"},d="idle",_="idle";t.setInitCssStyles=function(t){a=e.parseJSON(t)},t.setInitCssStylesObj=function(e){a=e},t.init=function(){p(),t.initAdvancedEditor()};var v=function(){s.params=r},p=function(){e(".rev-advanced-css-idle, .rev-advanced-css-hover").click(function(){return-1!==UniteLayersRev.get_current_selected_layer()&&(!0!==UniteLayersRev.getCurrentLayer().hover?e("#change_acea_wrappers").hide():e("#change_acea_wrappers").show(),t.checkIfHandleExists(e("#layer_caption").val())?(d=e(this).hasClass("rev-advanced-css-idle")?"idle":"hover",_=e(this).hasClass("rev-advanced-css-idle")?"idle":"hover",void e("#dialog_advanced_css").dialog({modal:!0,resizable:!1,title:"Currently editing: "+e("#layer_caption").val(),minWidth:1024,minHeight:500,closeOnEscape:!0,open:function(){e(".current-advance-edited-class").text(e("#layer_caption").val()),e(this).closest(".ui-dialog").addClass("tp-css-editor-dialog"),null!=o&&o.refresh(),null!=i&&i.refresh(),t.setTemplateCssUneditable(),t.setTemplateCssEditable()},close:function(e){d=_},buttons:{Save:function(){t.saveTemplateStylesInDb(),v(),d=_},Cancel:function(){e(this).dialog("close"),d=_}}})):(alert(rev_lang.please_select_first_an_existing_style),!1))}),e(".rev-advanced-css-idle-layer, .rev-advanced-css-hover-layer").click(function(){if(-1===UniteLayersRev.get_current_selected_layer())return!1;!0!==UniteLayersRev.getCurrentLayer().hover?e("#change_ace_wrappers").hide():e("#change_ace_wrappers").show(),d=e(this).hasClass("rev-advanced-css-idle-layer")?"idle":"hover",_=e(this).hasClass("rev-advanced-css-idle-layer")?"idle":"hover",e("#dialog_advanced_layer_css").dialog({modal:!0,resizable:!1,minWidth:1024,minHeight:500,closeOnEscape:!0,open:function(a){e(this).closest(".ui-dialog").addClass("tp-css-editor-dialog"),null!=l&&l.refresh(),t.setTemplateCssLayer()},close:function(e){d=_},buttons:{Save:function(){u(),d=_,e(this).dialog("close")},Cancel:function(){d=_,e(this).dialog("close")}}})})};e(document).ready(function(){e("body").on("click","#change_ace_toidle",function(){confirm(rev_lang.save_changes)&&u(),d="idle",t.setTemplateCssLayer()}),e("body").on("click","#change_ace_tohover",function(){confirm(rev_lang.save_changes)&&u(),d="hover",t.setTemplateCssLayer()}),e("body").on("click","#change_acea_toidle",function(){confirm(rev_lang.save_changes)&&(t.saveTemplateStylesInDb(),v()),d="idle",t.setTemplateCssUneditable(),t.setTemplateCssEditable()}),e("body").on("click","#change_acea_tohover",function(){confirm(rev_lang.save_changes)&&(t.saveTemplateStylesInDb(),v()),d="hover",t.setTemplateCssUneditable(),t.setTemplateCssEditable()})}),t.initAdvancedEditor=function(){o=CodeMirror.fromTextArea(document.getElementById("textarea_advanced_css_editor"),{onChange:function(){},lineNumbers:!0}),i=CodeMirror.fromTextArea(document.getElementById("textarea_template_css_editor_uneditable"),{onChange:function(){},lineNumbers:!0,readOnly:!0}),l=CodeMirror.fromTextArea(document.getElementById("textarea_template_css_editor_layer"),{onChange:function(){},lineNumbers:!0,readOnly:!1})},t.setPreviewTextClass=function(){if(void 0!==s.handle){var t=s.handle.split(".");for(var a in t)t.hasOwnProperty(a)&&e("#rev-example-style-layer").addClass(t[a])}e("#rev-example-style-layer").parent().show()},t.clearPreviewText=function(){e("#rev-example-style-layer").attr("style",""),e("#rev-example-style-layer").attr("class","")},t.setTemplateCssUneditable=function(){e("#textarea_template_css_editor_uneditable").val("");var a="{\n";if(y(),"idle"===d?e(".acsa_idle_or_hover").html("- IDLE"):e(".acsa_idle_or_hover").html("- HOVER"),r="idle"==d&&void 0!==s.params?s.params:"hover"==d&&void 0!==s.hover?s.hover:[],!e.isEmptyObject(r))for(var n in c)if(c.hasOwnProperty(n)){var o="";"undefined"!==(o="object"==typeof r[n]?r[n].join(" "):r[n])&&void 0!==o&&""!==o&&"none"!==o&&(a+="\t"+n+": "+o+";\n")}a+="}",null!=i?i.setValue(a):(e("#textarea_template_css_editor_uneditable").val(a),t.initAdvancedEditor()),i.refresh()},t.setTemplateCssEditable=function(){e("#textarea_advanced_css_editor").val("");var a="{\n";if(y(),"idle"==d&&void 0!==s.advanced&&void 0!==s.advanced.idle)var n=e.extend({},s.advanced.idle);else if("hover"==d&&void 0!==s.advanced&&void 0!==s.advanced.hover)n=e.extend({},s.advanced.hover);else n=[];for(var r in n)if(n.hasOwnProperty(r)){var i="";"undefined"!==(i="object"==typeof n[r]?n[r].join(" "):n[r])&&void 0!==i&&""!==i&&(a+="\t"+r+": "+i+";\n")}a+="}",null!=o?o.setValue(a):(e("#textarea_advanced_css_editor").val(a),t.initAdvancedEditor()),o.refresh()},t.setTemplateCssLayer=function(){e("#textarea_template_css_editor_layer").val("");var a="{\n";y();var n=UniteLayersRev.getCurrentLayer();if(null===n)return!1;if("idle"===d?e("#acs_idle_or_hover").html("- IDLE"):e("#acs_idle_or_hover").html("- HOVER"),n)if("idle"==d&&void 0!==n.inline&&void 0!==n.inline.idle)var r=e.extend({},n.inline.idle);else if("hover"==d&&void 0!==n.inline&&void 0!==n.inline.hover)r=e.extend({},n.inline.hover);else r=[];for(var s in r)if(r.hasOwnProperty(s)){var i="";"undefined"!==(i="object"==typeof r[s]?r[s].join(" "):r[s])&&void 0!==i&&""!==i&&(a+="\t"+s+": "+i+";\n")}a+="}",null!=l?l.setValue(a):(e("#textarea_template_css_editor_layer").val(a),t.initAdvancedEditor()),l.refresh()},t.saveStylesInDb=function(a,n,r){var i={idle:{},hover:{}},o=UniteLayersRev.getCurrentLayer();i.handle=a,i.idle.color=e('input[name="color_static"]').val(),i.idle["color-transparency"]=e('input[name="css_font-transparency"]').val(),i.idle["font-size"]=e('input[name="font_size_static"]').val(),i.idle["line-height"]=e('input[name="line_height_static"]').val(),i.idle["font-weight"]=e('select[name="font_weight_static"] option:selected').val(),i.idle["font-style"]=e('input[name="css_font-style"]').is(":checked")?"italic":"normal",i.idle["font-family"]=e('input[name="css_font-family"]').val()?e('input[name="css_font-family"]').val():"Arial",i.idle["text-decoration"]=e('select[name="css_text-decoration"] option:selected').val(),i.idle["text-align"]=o["text-align"],i.idle.margin=o.margin,i.idle.padding=o.padding,i.idle["background-color"]=e('input[name="css_background-color"]').val(),i.idle["background-transparency"]=e('input[name="css_background-transparency"]').val(),i.idle["border-color"]=e('input[name="css_border-color-show"]').val(),i.idle["border-transparency"]=e('input[name="css_border-transparency"]').val(),i.idle["border-style"]=e('select[name="css_border-style"] option:selected').val(),i.idle["border-width"]={},e('input[name="css_border-width[]"]').each(function(t){i.idle["border-width"][t]=e(this).val()}),i.idle["border-radius"]={},e('input[name="css_border-radius[]"]').each(function(t){i.idle["border-radius"][t]=e(this).val()}),i.idle.x=e('input[name="layer__x"]').val(),i.idle.y=e('input[name="layer__y"]').val(),i.idle.z=e('input[name="layer__z"]').val(),i.idle.skewx=e('input[name="layer__skewx"]').val(),i.idle.skewy=e('input[name="layer__skewy"]').val(),i.idle.scalex=e('input[name="layer__scalex"]').val(),i.idle.scaley=e('input[name="layer__scaley"]').val(),i.idle.opacity=e('input[name="layer__opacity"]').val(),i.idle.xrotate=e('input[name="layer__xrotate"]').val(),i.idle.yrotate=e('input[name="layer__yrotate"]').val(),i.idle["2d_rotation"]=e('input[name="layer_2d_rotation"]').val(),i.idle["2d_origin_x"]=e('input[name="layer_2d_origin_x"]').val(),i.idle["2d_origin_y"]=e('input[name="layer_2d_origin_y"]').val(),i.idle.pers=e('input[name="layer__pers"]').val(),i.idle.corner_left=e('select[name="layer_cornerleft"] option:selected').val(),i.idle.corner_right=e('select[name="layer_cornerright"] option:selected').val(),i.idle.parallax=e('select[name="parallax_level"] option:selected').val(),i.hover.color=e('input[name="hover_color_static"]').val(),i.hover["color-transparency"]=e('input[name="hover_css_font-transparency"]').val(),i.hover["text-decoration"]=e('select[name="hover_css_text-decoration"] option:selected').val(),i.hover["background-color"]=e('input[name="hover_css_background-color"]').val(),i.hover["background-transparency"]=e('input[name="hover_css_background-transparency"]').val(),i.hover["border-color"]=e('input[name="hover_css_border-color-show"]').val(),i.hover["border-transparency"]=e('input[name="hover_css_border-transparency"]').val(),i.hover["border-style"]=e('select[name="hover_css_border-style"] option:selected').val(),i.hover["border-width"]={},e('input[name="hover_css_border-width[]"]').each(function(t){i.hover["border-width"][t]=e(this).val()}),i.hover["border-radius"]={},e('input[name="hover_css_border-radius[]"]').each(function(t){i.hover["border-radius"][t]=e(this).val()}),i.hover.opacity=e('input[name="hover_layer__opacity"]').val(),i.hover.scalex=e('input[name="hover_layer__scalex"]').val(),i.hover.scaley=e('input[name="hover_layer__scaley"]').val(),i.hover.skewx=e('input[name="hover_layer__skewx"]').val(),i.hover.skewy=e('input[name="hover_layer__skewy"]').val(),i.hover.xrotate=e('input[name="hover_layer__xrotate"]').val(),i.hover.yrotate=e('input[name="hover_layer__yrotate"]').val(),i.hover["2d_rotation"]=e('input[name="hover_layer_2d_rotation"]').val(),i.hover.css_cursor=e('select[name="css_cursor"] option:selected').val(),i.hover.pointer_events=e('select[name="pointer_events"] option:selected').val(),i.hover.speed=e('input[name="hover_speed"]').val(),i.hover.easing=e('select[name="hover_easing"] option:selected').val(),o=UniteLayersRev.getCurrentLayer(),i.settings=new Object,i.settings.hover=e('input[name="hover_allow"]').is(":checked"),i.settings.type=o.type,i.advanced={idle:{},hover:{}},y(),void 0!==s.advanced&&(void 0!==s.advanced.idle&&(i.advanced.idle=s.advanced.idle),void 0!==s.advanced.hover&&(i.advanced.hover=s.advanced.hover));var l=!0===n?"insert_captions_css":"update_captions_css";UniteAdminRev.ajaxRequest(l,i,function(n){UniteLayersRev.setCaptionClasses(n.arrCaptions),t.updateCaptionsInput(n.arrCaptions),t.setInitCssStylesObj(n.initstyles),"undefined"!==n.compressed_css&&t.refresh_css(n.compressed_css),void 0!==r&&r.dialog("close"),e("#layer_caption").val(a),e("#layer_caption").change()})},t.saveTemplateStylesInDb=function(){for(var a={},r=n+"."+e("#layer_caption").val(),i=o.getValue();-1!==i.indexOf("/*");){if(-1===i.indexOf("*/"))return!1;var l=i.indexOf("/*"),c=i.indexOf("*/")+2;i=i.replace(i.substr(l,c-l),"")}if(i.indexOf("{")>-1){var _=i.substr(0,i.indexOf("{"));i=i.replace(_,"")}i.indexOf("}")>-1&&(i=i.substr(0,i.indexOf("}")));var v=(i=i.replace(/{/g,"").replace(/}/g,"").replace(/	/g,"").replace(/\n/g,"")).split(";"),p={};for(var u in v)if(v.hasOwnProperty(u)){if(""==e.trim(v[u]))continue;var y=v[u].split(":");if(2!==y.length)continue;p[e.trim(y[0])]=e.trim(y[1])}e.isEmptyObject(p)&&(p=""),a.handle=r,a.styles=p,a.type=d,UniteAdminRev.ajaxRequest("update_captions_advanced_css",a,function(n){if(!1!==n.success){UniteLayersRev.setCaptionClasses(n.arrCaptions),t.updateCaptionsInput(n.arrCaptions),t.setInitCssStylesObj(n.initstyles),"undefined"!==n.compressed_css&&t.refresh_css(n.compressed_css),s.advanced[a.type]=a.styles;var r=t.checkIfHandleExists(e("#layer_caption").val());t.updateInitCssStyles(e("#layer_caption").val(),r)}})};var u=function(){var t=UniteLayersRev.getCurrentLayer();if(null===t)return!1;for(var a=l.getValue();-1!==a.indexOf("/*");){if(-1===a.indexOf("*/"))return!1;var n=a.indexOf("/*"),r=a.indexOf("*/")+2;a=a.replace(a.substr(n,r-n),"")}if(a.indexOf("{")>-1){var s=a.substr(0,a.indexOf("{"));a=a.replace(s,"")}a.indexOf("}")>-1&&(a=a.substr(0,a.indexOf("}")));var i=(a=a.replace(/{/g,"").replace(/}/g,"").replace(/	/g,"").replace(/\n/g,"")).split(";"),o={};for(var c in i)if(i.hasOwnProperty(c)){if(""==e.trim(i[c]))continue;var _=i[c].split(":");if(2!==_.length)continue;o[e.trim(_[0])]=e.trim(_[1])}void 0===t.inline&&(t.inline={}),void 0===t.inline[d]&&(t.inline[d]={}),t.inline[d]=o,tpLayerTimelinesRev.rebuildLayerIdle(e(".slide_layer.layer_selected"))};t.renameStylesInDb=function(a,n){var i={};i.old_name=a,i.new_name=n,UniteAdminRev.ajaxRequest("rename_captions_css",i,function(r){UniteLayersRev.setCaptionClasses(r.arrCaptions),t.updateCaptionsInput(r.arrCaptions),t.setInitCssStylesObj(r.initstyles),"undefined"!==r.compressed_css&&t.refresh_css(r.compressed_css);var s={};s.style=n;var i=UniteLayersRev.getSimpleLayers();for(var o in i)i.hasOwnProperty(o)&&i[o].style==a&&UniteLayersRev.updateLayer(o,s);e("#layer_caption").val(n),e("#layer_caption").change(),e("#dialog_rename_css").dialog("close")}),t.updateInitCssStyles(a,-1),s=new Object,r=new Object},t.deleteStylesInDb=function(a,n){UniteAdminRev.setErrorMessageID("dialog_error_message"),UniteAdminRev.ajaxRequest("delete_captions_css",a,function(e){UniteLayersRev.setCaptionClasses(e.arrCaptions),t.updateCaptionsInput(e.arrCaptions),t.setInitCssStylesObj(e.initstyles),"undefined"!==e.compressed_css&&t.refresh_css(e.compressed_css)}),t.updateInitCssStyles(a,n,!0),e("#layer_caption").val(""),s=new Object,r=new Object};var y=function(){for(var t in s=new Object,a)if(a.hasOwnProperty(t)&&a[t].handle==n+"."+e("#layer_caption").val()){s=e.extend({},a[t]);break}};t.checkIfHandleExists=function(e){for(var t in a)if(a.hasOwnProperty(t)&&a[t].handle==n+"."+e)return t;return!1},t.updateCaptionsInput=function(t){layer=UniteLayersRev.getCurrentLayer();var a=[];if(!1!==layer)switch(layer.type){case"image":for(var n in t)t.hasOwnProperty(n)&&"image"==t[n].type&&a.push(t[n]);break;case"button":for(var n in t)t.hasOwnProperty(n)&&"button"==t[n].type&&a.push(t[n]);break;case"shape":for(var n in t)t.hasOwnProperty(n)&&"shape"==t[n].type&&a.push(t[n]);break;default:for(var n in t)t.hasOwnProperty(n)&&"text"==t[n].type&&a.push(t[n])}e("#layer_caption").catcomplete("option","source",a)},t.updateInitCssStyles=function(e,t,r){var i=!1;for(var o in a)if(a.hasOwnProperty(o)&&a[o].handle==n+"."+e){i=o;break}return void 0!==r?(delete a[i],!0):(!1===i&&(i=a.length),!1===t&&(t=a.length,a[i]=new Object,a[i].id=t,a[i].handle=n+"."+e,a[i].params=[],a[i].hover=[],a[i].settings=[],a[i].advanced={hover:{},idle:{}}),a[i].params=s.params,a[i].hover=s.hover,a[i].advanced=s.advanced,void 0===a[i].settings&&(a[i].settings=new Object),a[i].settings.hover=!1,a[i])},t.refresh_css=function(t){var a=e("#rs-inline-captions-css");a.is("style")&&a.html(t)},t.getStyleSettingsByHandle=function(e){if(""!=e)for(var t in a)if(a.hasOwnProperty(t)&&a[t].handle==n+"."+e)return a[t];return!1},t.set_state=function(t,a,n,r,s,i){if(void 0===i)switch(s){case"select":i=e(n+" option:selected").val();break;case"multi":i={},e(n).each(function(t){i[t]=e(this).val()});break;default:i=e(n).val()}var o="";if(void 0!==e(n).data("suffix")&&(o=e(n).data("suffix")),void 0===t[a]){if(r==i||r+o==i?(e(n).removeClass("differentthandefault"),e(n).css("color","#777")):e(n).addClass("differentthandefault"),"object"==typeof r){var l="",c="",d="";for(var _ in r)if(r.hasOwnProperty(_)){if(""==r[_])continue;l+=r[_]+o}for(var _ in i)if(i.hasOwnProperty(_)){if(""==i[_])continue;c+=i[_],d+=i[_]+o}l!==c&&l!==d||(e(n).removeClass("differentthandefault"),e(n).css("color","#777"))}}else if("object"==typeof t[a]){l="",c="",d="";for(var _ in t[a])if(t[a].hasOwnProperty(_)){if(""==t[a][_])continue;l+=t[a][_]+o}for(var _ in i)if(i.hasOwnProperty(_)){if(""==i[_])continue;c+=i[_],d+=i[_]+o}l===c||l===d?(e(n).removeClass("differentthandefault"),e(n).css("color","#777")):e(n).addClass("differentthandefault")}else if(t[a]!==i&&t[a]+o!==i)if("object"==typeof i){var v=t[a].split(" "),p="";switch(v.length){case 1:p=v[0]+v[0]+v[0]+v[0];break;case 2:p=v[0]+v[1]+v[0]+v[1];break;case 3:p=v[0]+v[1]+v[2]+v[1]}for(var _ in c="",d="",i)if(i.hasOwnProperty(_)){if(""==i[_])continue;c+=i[_],d+=i[_]+o}p==c||p==d?(e(n).removeClass("differentthandefault"),e(n).css("color","#777")):e(n).addClass("differentthandefault")}else e(n).addClass("differentthandefault");else e(n).removeClass("differentthandefault"),e(n).css("color","#777")},t.compare_to_original=function(){var a=e("#layer_caption").val();if(orig_styles=t.getStyleSettingsByHandle(a),!1===orig_styles)return!1;if(void 0!==orig_styles.params){var n=orig_styles.params,r=e('input[name="css_font-style"]').is(":checked")?"italic":"normal";t.set_state(n,"color-transparency",'input[name="css_font-transparency"]',"1"),t.set_state(n,"font-style",'input[name="css_font-style"]',"normal","checkbox",r),t.set_state(n,"font-family",'input[name="css_font-family"]',""),t.set_state(n,"padding",'input[name="css_padding[]"]',{0:"0",1:"0",2:"0",3:"0"},"multi"),t.set_state(n,"text-decoration",'select[name="css_text-decoration"]',"none","select"),t.set_state(n,"background-color",'input[name="css_background-color"]',"transparent"),t.set_state(n,"background-transparency",'input[name="css_background-transparency"]',"1"),t.set_state(n,"border-color",'input[name="css_border-color-show"]',"transparent"),t.set_state(n,"border-transparency",'input[name="css_border-transparency"]',"1"),t.set_state(n,"border-style",'select[name="css_border-style"]',"none","select"),t.set_state(n,"border-width",'input[name="css_border-width"]',"0"),t.set_state(n,"border-radius",'input[name="css_border-radius[]"]',{0:"0",1:"0",2:"0",3:"0"},"multi"),t.set_state(n,"x",'input[name="layer__x"]',"0"),t.set_state(n,"y",'input[name="layer__y"]',"0"),t.set_state(n,"z",'input[name="layer__z"]',"0"),t.set_state(n,"skewx",'input[name="layer__skewx"]',"0"),t.set_state(n,"skewy",'input[name="layer__skewy"]',"0"),t.set_state(n,"scalex",'input[name="layer__scalex"]',"1"),t.set_state(n,"scaley",'input[name="layer__scaley"]',"1"),t.set_state(n,"opacity",'input[name="layer__opacity"]',"1"),t.set_state(n,"xrotate",'input[name="layer__xrotate"]',"0"),t.set_state(n,"yrotate",'input[name="layer__yrotate"]',"0"),t.set_state(n,"2d_rotation",'input[name="layer_2d_rotation"]',"0"),t.set_state(n,"2d_origin_x",'input[name="layer_2d_origin_x"]',"50"),t.set_state(n,"2d_origin_y",'input[name="layer_2d_origin_y"]',"50"),t.set_state(n,"pers",'input[name="layer__pers"]',"600"),t.set_state(n,"corner_left",'select[name="layer_cornerleft"]',"nothing","select"),t.set_state(n,"corner_right",'select[name="layer_cornerright"]',"nothing","select")}if(void 0!==orig_styles.hover&&!e.isEmptyObject(orig_styles.hover)){var s=orig_styles.hover;t.set_state(s,"color",'input[name="hover_color_static"]'),t.set_state(s,"color-transparency",'input[name="hover_css_font-transparency"]',"1"),t.set_state(s,"text-decoration",'select[name="hover_css_text-decoration"]',"none","select"),t.set_state(s,"background-color",'input[name="hover_css_background-color"]',"transparent"),t.set_state(s,"background-transparency",'input[name="hover_css_background-transparency"]',"1"),t.set_state(s,"border-color",'input[name="hover_css_border-color-show"]',"transparent"),t.set_state(s,"border-transparency",'input[name="hover_css_border-transparency"]',"1"),t.set_state(s,"border-style",'select[name="hover_css_border-style"]',"none","select"),t.set_state(s,"border-width",'input[name="hover_css_border-width"]',"0","select"),t.set_state(s,"border-radius",'input[name="hover_css_border-radius[]"]',{0:"0",1:"0",2:"0",3:"0"},"multi"),t.set_state(s,"skewx",'input[name="hover_layer__skewx"]',"0"),t.set_state(s,"skewy",'input[name="hover_layer__skewy"]',"0"),t.set_state(s,"scalex",'input[name="hover_layer__scalex"]',"1"),t.set_state(s,"scaley",'input[name="hover_layer__scaley"]',"1"),t.set_state(s,"opacity",'input[name="hover_layer__opacity"]',"1"),t.set_state(s,"xrotate",'input[name="hover_layer__xrotate"]',"0"),t.set_state(s,"yrotate",'input[name="hover_layer__yrotate"]',"0"),t.set_state(s,"2d_rotation",'input[name="hover_layer_2d_rotation"]',"0"),t.set_state(s,"css_cursor",'select[name="css_cursor"]',"auto","select"),t.set_state(s,"pointer_events",'select[name="pointer_events"]',"auto","select"),t.set_state(s,"speed",'input[name="hover_speed"]',"0"),t.set_state(s,"easing",'select[name="hover_easing"]',"50","select")}return!0}}}($nwd_jQuery);