<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<form id="edit_form" method="post" action="<?php echo $this->getSaveUrl() ?>">
    <?php echo $this->getBlockHtml('formkey')?>
    <?php  $_order = $this->getCreditmemo()->getOrder() ?>
    <?php echo $this->getChildHtml('order_info') ?>

    <?php if (!$_order->getIsVirtual()): ?>
    <div class="box-left">
    <?php else: ?>
    <div class="box-right">
    <?php endif; ?>
        <!--Billing Address-->
        <div class="entry-edit">
            <div class="entry-edit-head">
                <h4 class="icon-head head-payment-method"><?php echo Mage::helper('sales')->__('Payment Information') ?></h4>
            </div>
            <fieldset>
                <div><?php echo $this->getChildHtml('order_payment') ?></div>
                <div><?php echo Mage::helper('sales')->__('Order was placed using %s', $_order->getOrderCurrencyCode()) ?></div>
                <?php /*if ($this->getCreditmemo()->canRefund()): ?>
                <input type="checkbox" name="creditmemo[do_refund]" id="creditmemo_do_refund" value="1" checked/>
                <label for="creditmemo_do_refund" class="normal"><?php echo Mage::helper('sales')->__('Refund Amount') ?></label>
                <?php endif;*/ ?>
            </fieldset>
        </div>
    </div>
    <?php if (!$_order->getIsVirtual()): ?>
    <div class="box-right">
        <!--Shipping Address-->
        <div class="entry-edit">
            <div class="entry-edit-head">
                <h4 class="icon-head head-shipping-method"><?php echo Mage::helper('sales')->__('Shipping Information') ?></h4>
            </div>
            <fieldset>
                <strong><?php echo $this->escapeHtml($_order->getShippingDescription()) ?></strong>
                <?php echo $this->helper('sales')->__('Total Shipping Charges'); ?>:

                <?php if ($this->helper('tax')->displaySalesPriceInclTax($this->getSource()->getStoreId())): ?>
                    <?php $_excl = $this->displayShippingPriceInclTax($_order); ?>
                <?php else: ?>
                    <?php $_excl = $this->displayPriceAttribute('shipping_amount', false, ' '); ?>
                <?php endif; ?>
                <?php $_incl = $this->displayShippingPriceInclTax($_order); ?>

                <?php echo $_excl; ?>
                <?php if ($this->helper('tax')->displaySalesBothPrices($this->getSource()->getStoreId()) && $_incl != $_excl): ?>
                    (<?php echo $this->__('Incl. Tax'); ?> <?php echo $_incl; ?>)
                <?php endif; ?>
            </fieldset>
        </div>
    </div>
    <?php endif; ?>
    <div class="clear"></div>

    <div class="entry-edit">
      <div class="entry-edit-head">
          <h4 class="icon-head head-products"><?php echo Mage::helper('sales')->__('Items to Refund') ?></h4>
      </div>
    </div>
    <div id="creditmemo_item_container">
      <?php echo $this->getChildHtml('order_items') ?>
    </div>
</form>
