<?php
    $_productCollection=$this->getLoadedProductCollection();
    $_helper = $this->helper('catalog/output');
    $_image_helper = $this->helper('catalog/image');
    $store = Mage::app()->getStore();
    $code  = $store->getCode();

    $lazyload = 1;//Mage::getStoreConfig("porto_settings/optimization/lazyload",$code);

    $aspect_ratio = $this->getData("aspect_ratio");
    if($aspect_ratio == null) {
        $aspect_ratio = Mage::getStoreConfig("porto_settings/category/aspect_ratio",$code);
    }
    $ratio_width = $this->getData("image_width");
    if(!$ratio_width) {
        $ratio_width = Mage::getStoreConfig("porto_settings/category/ratio_width",$code);
    }
    if(!$ratio_width)
        $ratio_width = 300;
    $ratio_height = $this->getData("image_height");
    if($aspect_ratio) {
        $ratio_height = $ratio_width;
    }
    if(!$ratio_height) {
        $ratio_height = Mage::getStoreConfig("porto_settings/category/ratio_height",$code);
    }
    if(!$ratio_height)
        $ratio_height = 300;

    $product_type = $this->getData("product_type");
    if($product_type == null) {
        $product_type = Mage::getStoreConfig("porto_settings/category_grid/product_type",$code);
    }
    if($product_type == null) {
        $product_type = 1;
    }
    if($product_type > 3) {
        $product_type = 1;
    }

    $masonry_layout = $this->getData("masonry_layout");
    if($masonry_layout == null) {
        $masonry_layout = '1';
    }
    $masonry_id = $this->getData("name");
    if($masonry_id == null) {
        $masonry_id = "masonry_1";
    }
    if($product_type == 1) {
        $product_type = 7;
    }else if($product_type == 2) {
        $product_type = 8;
    }else if($product_type == 3) {
        $product_type = 10;
    }
    $gap_width = $this->getData("gap_width");
    if($gap_width == null) {
        $gap_width = 0;
    }
    $gap_width = $gap_width / 2;

    $grid_height = $this->getData("grid_height");
    if($grid_height == null) {
        $grid_height = 600;
    }
    $product_grid_layout_arr = array();
    if ( '1' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '1', 'width' => '1-2', 'width_md' => '1' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
        );
    }
    if ( '2' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '2-3', 'width' => '1-2', 'width_md' => '1' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1-3', 'width' => '1-2', 'width_md' => '1' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
        );
    }
    if ( '3' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '1', 'width' => '1-2', 'width_md' => '1' ),
            array( 'height' => '1', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
        );
    }
    if ( '4' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '1-2', 'width' => '1-3', 'width_md' => '1' ),
            array( 'height' => '1-2', 'width' => '5-12', 'width_md' => '1' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1' ),
            array( 'height' => '1-2', 'width' => '1-3', 'width_md' => '1' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1' ),
            array( 'height' => '1-2', 'width' => '5-12', 'width_md' => '1' ),
        );
    }
    if ( '5' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '1', 'width' => '2-5', 'width_md' => '1', 'width_lg' => '1-2' ),
            array( 'height' => '1', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-2' ),
        );
    }
    if ( '6' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '2-3', 'width' => '1-2', 'width_md' => '1', 'width_lg' => '1-2' ),
            array( 'height' => '2-3', 'width' => '1-4', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1', 'width' => '1-4', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-3', 'width' => '1-4', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-3', 'width' => '1-4', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-3', 'width' => '1-4', 'width_md' => '1-2', 'width_lg' => '1-2' ),
        );
    }
    if ( '7' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '1', 'width' => '1-2', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-2', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-2', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-4', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-2', 'width_md' => '1-2' ),
            array( 'height' => '1', 'width' => '1-2', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-2', 'width_md' => '1-2' ),
        );
    }
    if ( '8' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '1', 'width' => '1-3', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-3', 'width_md' => '1-2' ),
            array( 'height' => '1', 'width' => '1-3', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-3', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-6', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-3', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-3', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'width' => '1-6', 'width_md' => '1-2' ),
        );
    }
    if ( '9' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '1', 'width' => '2-5', 'width_md' => '1', 'width_lg' => '2-3' ),
            array( 'height' => '1-2', 'height_md' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-3' ),
            array( 'height' => '1-2', 'height_md' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-3' ),
            array( 'height' => '1-2', 'height_md' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-2', 'height_md' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-2', 'height_md' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-2' ),
            array( 'height' => '1-2', 'height_md' => '1-2', 'width' => '1-5', 'width_md' => '1-2', 'width_lg' => '1-2' ),
        );
    }
    if ( '10' == $masonry_layout ) {
        $product_grid_layout_arr = array(
            array( 'height' => '1-2', 'height_md' => '1-2', 'width' => '2-3', 'width_md' => '1' ),
            array( 'height' => '1', 'height_md' => '1', 'width' => '1-3', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'height_md' => '1-2', 'width' => '1-3', 'width_md' => '1-2' ),
            array( 'height' => '1-2', 'height_md' => '1-2', 'width' => '1-3', 'width_md' => '1-2' ),
        );
    }
    $product_limit = count($product_grid_layout_arr);
?>
<?php if(!$_productCollection->count()): ?>
<p class="note-msg"><?php echo $this->__('There are no products matching the selection.') ?></p>
<?php else: ?>
<div class="category-products">
    <?php // Grid Mode ?>
    <?php
        $rnd_str = "";
        $base_str = "0123456789abcdefghijklmnopqrstuvwxyz";
        $su = strlen($base_str) - 1;
        $rnd_str = substr($base_str, rand(0,$su), 1).substr($base_str, rand(0,$su), 1).substr($base_str, rand(0,$su), 1);
    ?>
    <?php $_collectionSize = $_productCollection->count() ?>
    <div id="<?php echo $masonry_id; ?>" class="products-grid masonry-grid">
    <?php $i=0; foreach ($_productCollection as $_product): ?>
    <?php if ($product_limit > $i): ?>
    <?php $product = $_product; ?>
        <div class="item <?php echo $this->helper('porto')->getMasonryItemClass($product_grid_layout_arr[$i]); ?>">
            <div class="item-area type<?php echo $product_type; ?>">
                <div class="product-image-area">
                    <div class="loader-container">
                        <div class="loader">
                            <i class="ajax-loader medium animate-spin"></i>
                        </div>
                    </div>
                    <?php
                    if( Mage::getStoreConfig("quickview/general/enableview", $code) ){
                        $base_url = $this->getUrl();
                        if(strpos($base_url,'?')!==false)
                            $base_url = explode("?",$this->getUrl());
                        if(is_array($base_url))
                            $base_url = $base_url[0];
                        $base_url .= "quickview/index/view/";
                        $quickview_url = $base_url."id/".$_product->getId();
                        ?>
                        <a href="<?php echo $quickview_url; ?>" class="quickview-icon"><i class="icon-export"></i><span><?php echo $this->__("Quick View"); ?></span></a>
                    <?php
                    }
                    ?>
                    <a href="<?php echo $_product->getProductUrl() ?>" title="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?>" class="product-image">
                    <?php
                        if(Mage::getStoreConfig("porto_settings/category/alternative_image", $code)){
                    ?>
                        <img class="defaultImage <?php if(!$lazyload): ?>porto-lazyload<?php endif;?>" <?php if(!$lazyload): ?>data-<?php endif; ?>src="<?php if($aspect_ratio):?><?php echo $_image_helper->init($_product, 'small_image')->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($ratio_width);?><?php else: ?><?php echo $_image_helper->init($_product, 'small_image')->resize($ratio_width,$ratio_height); ?><?php endif; ?>" width="<?php echo $ratio_width; ?>" height="<?php echo $ratio_height; ?>"/>
                        <img class="hoverImage" src="<?php if($aspect_ratio):?><?php echo $_image_helper->init($_product, 'thumbnail')->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($ratio_width);?><?php else: ?><?php echo $_image_helper->init($_product, 'thumbnail')->resize($ratio_width,$ratio_height); ?><?php endif; ?>" width="<?php echo $ratio_width; ?>" <?php if(!$aspect_ratio):?>height="<?php echo $ratio_height; ?>"<?php endif; ?> alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'thumbnail'), null, true) ?>"/>
                    <?php
                        } else {
                    ?>
                        <img <?php if(!$lazyload): ?>class="porto-lazyload" data-<?php endif; ?>src="<?php if($aspect_ratio):?><?php echo $_image_helper->init($_product, 'small_image')->constrainOnly(FALSE)->keepAspectRatio(TRUE)->keepFrame(FALSE)->resize($ratio_width);?><?php else: ?><?php echo $_image_helper->init($_product, 'small_image')->resize($ratio_width,$ratio_height); ?><?php endif; ?>" width="<?php echo $ratio_width; ?>" height="<?php echo $ratio_height; ?>"/>
                    <?php
                        }
                    ?>
                    </a>
                    <?php 
                        
                        $sale_label = false;
                        
                        
                        // Get the Special Price
                        $specialprice = $product->getFinalPrice();
                        $orgprice = $product->getPrice();
                        // Get the Special Price FROM date
                        $now = date("Y-m-d");
                        $specialPriceFromDate = substr($product->getSpecialFromDate(),0,10);
                        // Get the Special Price TO date
                        $specialPriceToDate = substr($product->getSpecialToDate(),0,10);
                        // Get Current date
                        $today =  time();
                     
                        if ($specialprice<$orgprice){
                            $save_percent = 100-round(($specialprice/$orgprice)*100);
                            if ($specialPriceToDate != '' || $specialPriceFromDate != ''){
                            if (($specialPriceToDate != '' && $specialPriceFromDate != '' && $now>=$specialPriceFromDate && $now<=$specialPriceToDate) || ($specialPriceToDate == '' && $now >=$specialPriceFromDate) || ($specialPriceFromDate == '' && $now<=$specialPriceToDate)){
                                if(Mage::getStoreConfig("porto_settings/product_label/sale", $code)){
                                    $sale_label = true;
                    ?>
                                <div class="product-label" style="right: 10px;"><span class="sale-product-icon"><?php if(Mage::getStoreConfig("porto_settings/product_label/sale_label_type", $code)):?><?php echo "-".$save_percent."%"; ?><?php else: ?><?php echo Mage::getStoreConfig("porto_settings/product_label/sale_label_text", $code);?><?php endif; ?></span></div>
                    <?php       
                                }
                            }
                        }
                        }
                    ?>
                    <?php
                        $now = date("Y-m-d");
                        $newsFrom= substr($_product->getData('news_from_date'),0,10);
                        $newsTo=  substr($_product->getData('news_to_date'),0,10);
                        if ($newsTo != '' || $newsFrom != ''){
                            if (($newsTo != '' && $newsFrom != '' && $now>=$newsFrom && $now<=$newsTo) || ($newsTo == '' && $now >=$newsFrom) || ($newsFrom == '' && $now<=$newsTo))
                            {
                                if(Mage::getStoreConfig("porto_settings/product_label/new", $code)){
                        ?> 
                                <div class="product-label" style="right: 10px; <?php echo ($sale_label)?"top: 40px;":""; ?>"><span class="new-product-icon"><?php echo Mage::getStoreConfig("porto_settings/product_label/new_label_text", $code);?></span></div>
                        <?php 
                                }
                            }
                        }
                    ?>
                    <div class="actions">
                        <?php if($product->isSaleable()): ?>
                            <?php  if ( !($product->getTypeInstance(true)->hasOptions($product) || $product->isGrouped()) ) :  ?>
                                <a href="<?php if(!Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code) && !Mage::getStoreConfig("porto_settings/category/qty_field", $code)): ?><?php echo $this->getAddToCartUrl($_product) ?><?php else: ?>javascript:void(0)<?php endif; ?>" class="addtocart" title="<?php echo $this->__('Add to Cart') ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>onclick="setLocationAjax(this,'<?php echo $this->getAddToCartUrl($_product) ?>','<?php echo $_product->getId()."_".$rnd_str; ?>')"<?php elseif(Mage::getStoreConfig("porto_settings/category/qty_field", $code)): ?>onclick="document.getElementById('addtocart_form_<?php echo $_product->getId(); ?>').submit()"<?php endif; ?>><i class="icon-cart"></i><span>&nbsp;<?php echo $this->__('Add to Cart') ?></span></a>
                            <?php else : ?>
                                <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtocart/enablecategory", $code)):?>javascript:showOptions('<?php echo $_product->getId()?>')<?php else: ?><?php echo $this->getAddToCartUrl($_product) ?><?php endif; ?>" class="addtocart" title="<?php echo $this->__('Add to Cart') ?>"><i class="icon-cart"></i><span>&nbsp;<?php echo $this->__('Add to Cart') ?></span></a>
                                <a href='<?php echo $this->getUrl('ajaxcart/index/options',array('product_id'=>$_product->getId()));?>' class='fancybox' id='fancybox<?php echo $_product->getId()?>' style='display:none'>Options</a>
                            <?php endif;?>
                        <?php else: ?>
                            <a href="javascript:void(0);" class="addtocart outofstock" title="<?php echo $this->__('Out of stock') ?>"><?php echo $this->__('Out of stock') ?></span></a>
                        <?php endif; ?>
                        <?php if ($this->helper('wishlist')->isAllow()) : ?>
                            <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>javascript:void(0)<?php else: ?><?php echo $this->helper('wishlist')->getAddUrl($_product) ?><?php endif; ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>onclick="ajaxWishlist(this,'<?php echo $this->helper('wishlist')->getAddUrl($_product) ?>','<?php echo $_product->getId()?>');"<?php endif;?> class="addtowishlist" title="<?php echo $this->__('Add to Wishlist') ?>"><i class="icon-wishlist"></i></a>
                        <?php endif; ?>
                        <?php if(Mage::getStoreConfig("porto_settings/category/compare", $code) && $_compareUrl=$this->getAddToCompareUrl($_product)): ?>
                            <a href="<?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>javascript:void(0)<?php else: ?><?php echo $_compareUrl ?><?php endif; ?>" <?php if(Mage::getStoreConfig("ajaxcart/addtolinks/enablecategory", $code)):?>onclick="ajaxCompare(this,'<?php echo $_compareUrl ?>','<?php echo $_product->getId()?>');"<?php endif; ?> class="comparelink" title="<?php echo $this->__('Add to Compare') ?>"><i class="icon-compare"></i></a>
                        <?php endif; ?>
                        <div class="clearer"></div>
                    </div>
                </div>
                <div class="details-area">
                    <h2 class="product-name"><a href="<?php echo $_product->getProductUrl() ?>" title="<?php echo $this->stripTags($_product->getName(), null, true) ?>"><?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?></a></h2>
                    <?php if(Mage::getStoreConfig("porto_settings/category/rating_star", $code)):?>
                    <?php 
                    if (Mage::helper('catalog')->isModuleEnabled('Mage_Review')):
                    ?>
                    <?php 
                        $review_html = $this->getReviewsSummaryHtml($product, 'short');
                        if($review_html){
                            echo $review_html;
                        }else{
                    ?>
                        <div class="ratings">
                            <div class="rating-box">
                                <div class="rating" style="width:0"></div>
                            </div>
                        </div>
                    <?php                            
                        }
                    ?>
                    <?php else: ?>
                        <div class="ratings">
                            <div class="rating-box">
                                <div class="rating" style="width:0"></div>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php endif; ?>
                    <?php if(Mage::getStoreConfig("porto_settings/category/product_price", $code)):?>
                    <?php echo $this->getPriceHtml($_product, true) ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?php $i++; endforeach; ?>
    </div>
    <style>
    .category-products .products-grid.masonry-grid#<?php echo $masonry_id; ?> {
        margin: 0 -<?php echo $gap_width; ?>px;
    }
    .category-products .products-grid.masonry-grid#<?php echo $masonry_id; ?> .item {
        padding: <?php echo $gap_width; ?>px;
    }
    .category-products .products-grid.masonry-grid#<?php echo $masonry_id; ?> .item.height-1 {
        height: <?php echo $grid_height; ?>px;
    }
    .category-products .products-grid.masonry-grid#<?php echo $masonry_id; ?> .item.height-1-2 {
        height: <?php echo $grid_height / 2 ?>px;
    }
    .category-products .products-grid.masonry-grid#<?php echo $masonry_id; ?> .item.height-1-3 {
        height: <?php echo $grid_height / 3; ?>px;
    }
    .category-products .products-grid.masonry-grid#<?php echo $masonry_id; ?> .item.height-2-3 {
        height: <?php echo $grid_height * 2 / 3; ?>px;
    }
    @media (max-width: 767px) {
        .category-products .products-grid.masonry-grid#<?php echo $masonry_id; ?> .item.height_md-1 {
            height: <?php echo $grid_height ?>px;
        }
        .category-products .products-grid.masonry-grid#<?php echo $masonry_id; ?> .item.height_md-1-2 {
            height: <?php echo $grid_height / 2 ?>px;
        }
    }
    </style>
    <script type="text/javascript">
    jQuery( function($) {
        var $container = $('.category-products .products-grid.masonry-grid#<?php echo $masonry_id; ?>').imagesLoaded(function(){
            $container.packery({
                itemSelector: ".item",
                percentPosition: true
            });
        });
    });
    </script>
</div>
<?php endif; ?>
