<?php
class PFG_AltCurrency_Model_Observer
{
    protected $templates = [
        [
            // Match entire price-box with <sup> tags format
            'template' => '/(<div class="price-box">\s*<span class="regular-price" id="product-price-(\d+)">.*?<span class="price">(\d+),<sup>(\d+)<\/sup>(?:&nbsp;|\s)*лв\.<\/span>.*?<span class="z-measure">\s*(.*?)\s*<\/span>.*?<\/span>\s*<\/div>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '<div class="price-box">
    <span class="regular-price" id="product-price-{{product_id}}">
        <span class="price">{{original_price}}&nbsp;лв.</span>
        <span class="z-measure">{{unit}}</span>
    </span>
</div>
<div class="price-box">
    <span class="regular-price alt-currency" id="product-price-eur-{{product_id}}">
        <span class="price">{{price}}&nbsp;{{symbol}}</span>
        <span class="z-measure">{{unit}}</span>
    </span>
</div>',
        ],
        [
            // Match entire price-box without <sup> tags format
            'template' => '/(<div class="price-box">\s*<span class="regular-price" id="product-price-(\d+)">.*?<span class="price">(\d+),(\d+)(?:&nbsp;|\s)*лв\.<\/span>.*?<span class="z-measure">\s*(.*?)\s*<\/span>.*?<\/span>\s*<\/div>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '<div class="price-box">
    <span class="regular-price" id="product-price-{{product_id}}">
        <span class="price">{{original_price}}&nbsp;лв.</span>
        <span class="z-measure">{{unit}}</span>
    </span>
</div>
<div class="price-box">
    <span class="regular-price alt-currency" id="product-price-eur-{{product_id}}">
        <span class="price">{{price}}&nbsp;{{symbol}}</span>
        <span class="z-measure">{{unit}}</span>
    </span>
</div>',
        ],
        [
            // Flexible regex to match price-box with any лв. format
            'template' => '/(<div class="price-box">.*?<span class="regular-price" id="product-price-(\d+)">.*?лв\..*?<span class="z-measure">\s*(.*?)\s*<\/span>.*?<\/span>.*?<\/div>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '<div class="price-box">
    {{original_content}}
</div>
<div class="price-box">
    <span class="regular-price alt-currency" id="product-price-eur-{{product_id}}">
        <span class="price">{{price}}&nbsp;{{symbol}}</span>
        <span class="z-measure">{{unit}}</span>
    </span>
</div>',
        ],
        [
            // Fallback: Match just the span if price-box structure is not complete
            'template' => '/(<span class="regular-price" id="product-price-(\d+)">.*?<span class="price">(\d+),<sup>(\d+)<\/sup>(?:&nbsp;|\s)*лв\.<\/span>.*?<span class="z-measure">\s*(.*?)\s*<\/span>.*?<\/span>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '<div class="price-box">
    <span class="regular-price" id="product-price-{{product_id}}">
        <span class="price">{{original_price}}&nbsp;лв.</span>
        <span class="z-measure">{{unit}}</span>
    </span>
</div>
<div class="price-box">
    <span class="regular-price alt-currency" id="product-price-eur-{{product_id}}">
        <span class="price">{{price}}&nbsp;{{symbol}}</span>
        <span class="z-measure">{{unit}}</span>
    </span>
</div>',
        ],
    ];

    public function appendConvertedPrice(Varien_Event_Observer $observer)
    {
        Mage::log("==== Observer triggered at " . date('Y-m-d H:i:s') . " (UPDATED VERSION) ====", null, 'altcurrency_debug.log', true);

        try {
            $transport = $observer->getTransport();
            if (!$transport) {
                Mage::log("❌ Transport is missing.", null, 'altcurrency_debug.log', true);
                return;
            }

            $originalHtmlFromTransport = $transport->getHtml();
            $html = html_entity_decode($originalHtmlFromTransport, ENT_QUOTES, 'UTF-8');
            $html = str_replace(['\\<', '\\>'], ['<', '>'], $html);

            // Additional cleanup for escaped HTML that might be double-escaped
            $html = str_replace(['\\\\<', '\\\\>'], ['<', '>'], $html);

            // Log the transformation for debugging
            if ($originalHtmlFromTransport !== $html) {
                Mage::log("🔧 HTML preprocessing applied. Original contained escaped characters.", null, 'altcurrency_debug.log', true);
            }

            if (!$html) {
                Mage::log("❌ HTML is empty.", null, 'altcurrency_debug.log', true);
                return;
            }

            if (strpos($html, 'alt-currency') !== false) {
                Mage::log("⚠️ alt-currency tag already present. Skipping.", null, 'altcurrency_debug.log', true);
                return;
            }

            if (strlen($html) < 10) {
                Mage::log("⚠️ HTML too short. Skipping.", null, 'altcurrency_debug.log', true);
                return;
            }

            // Only process HTML that contains price-related elements
            if (strpos($html, 'regular-price') === false && strpos($html, 'product-price') === false) {
                Mage::log("⚠️ HTML doesn't contain price elements. Skipping.", null, 'altcurrency_debug.log', true);
                return;
            }

            // TEMPORARY: Add a simple test replacement to verify the observer is working
            if (strpos($html, 'regular-price') !== false && strpos($html, 'alt-currency') === false) {
                Mage::log("🧪 TESTING: Found regular-price, will add test marker", null, 'altcurrency_debug.log', true);
                $html = str_replace('regular-price', 'regular-price TEST-MARKER-ADDED', $html);
                $transport->setHtml($html);
                Mage::log("🧪 TESTING: Test marker added to HTML", null, 'altcurrency_debug.log', true);
            }

            $rate = 1.95583;
            $symbol = '€';

            $originalHtml = $html;

            // Log a sample of the HTML to help debug the structure
            $htmlSample = substr($html, 0, 1000);
            Mage::log("📄 HTML sample (first 1000 chars): " . $htmlSample, null, 'altcurrency_debug.log', true);

            // Check if HTML contains price-box structure
            if (strpos($html, 'price-box') !== false) {
                Mage::log("✅ HTML contains price-box structure", null, 'altcurrency_debug.log', true);
            } else {
                Mage::log("⚠️ HTML does NOT contain price-box structure", null, 'altcurrency_debug.log', true);
            }

            // Try each template until one matches
            foreach ($this->templates as $templateIndex => $template) {
                Mage::log("✅ Trying template #$templateIndex: " . $template['template'], null, 'altcurrency_debug.log', true);

                $tempHtml = preg_replace_callback($template['template'], function ($matches) use ($rate, $symbol, $template, $templateIndex) {
                    Mage::log("🔄 Match found with template #$templateIndex!", null, 'altcurrency_debug.log', true);
                    Mage::log(print_r($matches, true), null, 'altcurrency_debug.log', true);

                    $fullMatch = $matches[1];
                    $productId = $matches[2];

                    // Handle different regex patterns
                    if ($templateIndex == 2) { // Flexible regex pattern for price-box
                        // Extract price from the entire matched content
                        $fullContent = $matches[1];

                        // Handle <sup> tags: 4,<sup>66</sup> лв.
                        if (preg_match('/(\d+),<sup>(\d+)<\/sup>.*?лв\./', $fullContent, $priceMatches)) {
                            $majorPart = $priceMatches[1];
                            $minorPart = $priceMatches[2];
                        }
                        // Fallback: try simple comma format: 4,66 лв.
                        elseif (preg_match('/(\d+)[,.](\d+).*?лв\./', $fullContent, $priceMatches)) {
                            $majorPart = $priceMatches[1];
                            $minorPart = $priceMatches[2];
                        } else {
                            Mage::log("⚠️ Could not extract price from full content: $fullContent", null, 'altcurrency_debug.log', true);
                            return $matches[0]; // Return original if we can't parse
                        }
                        $unit = trim($matches[3]);

                        // For flexible template, preserve original content
                        $originalContent = preg_replace('/<div class="price-box">\s*/', '', $fullContent);
                        $originalContent = preg_replace('/\s*<\/div>$/', '', $originalContent);

                    } elseif ($templateIndex == 3) { // Fallback span-only pattern
                        $majorPart = $matches[3];
                        $minorPart = $matches[4];
                        $unit = trim($matches[5]);
                        $originalContent = null; // Will be generated from components
                    } else { // Standard regex patterns (templates 0 and 1)
                        $majorPart = $matches[3];
                        $minorPart = $matches[4];
                        $unit = trim($matches[5]);
                        $originalContent = null; // Will be generated from components
                    }

                    $priceBGN = floatval($majorPart . '.' . $minorPart);
                    $priceEUR = $priceBGN / $rate;
                    $formattedEUR = number_format($priceEUR, 2, ',', ' ');
                    $formattedBGN = $majorPart . ',' . $minorPart;

                    Mage::log("💰 BGN: $priceBGN, EUR: $formattedEUR, ID: $productId, UNIT: $unit", null, 'altcurrency_debug.log', true);

                    // Build converted HTML using the template
                    $convertedHtml = $template['converted_template'];

                    // Replace placeholders
                    $replacements = [
                        '{{product_id}}' => htmlspecialchars($productId, ENT_QUOTES, 'UTF-8'),
                        '{{price}}' => $formattedEUR,
                        '{{symbol}}' => $symbol,
                        '{{unit}}' => htmlspecialchars($unit, ENT_QUOTES, 'UTF-8'),
                        '{{original_price}}' => $formattedBGN,
                    ];

                    // For flexible template, also replace original content
                    if ($templateIndex == 2 && $originalContent) {
                        $replacements['{{original_content}}'] = $originalContent;
                    }

                    $convertedHtml = str_replace(array_keys($replacements), array_values($replacements), $convertedHtml);

                    Mage::log("🧱 Generated price-box structure:\n" . $convertedHtml, null, 'altcurrency_debug.log', true);

                    return $convertedHtml;
                }, $html);

                if ($tempHtml !== $html) {
                    Mage::log("✅ HTML successfully converted with template #$templateIndex.", null, 'altcurrency_debug.log', true);
                    $html = $tempHtml;
                    break; // Exit the loop once we find a match
                } else {
                    Mage::log("⚠️ Template #$templateIndex: No matches found.", null, 'altcurrency_debug.log', true);
                }
            }

            if ($html === $originalHtml) {
                Mage::log("⚠️ No templates matched — HTML unchanged.", null, 'altcurrency_debug.log', true);
            }

            $transport->setHtml($html);

        } catch (Exception $e) {
            Mage::log('❌ Exception in appendConvertedPrice: ' . $e->getMessage(), null, 'altcurrency_error.log', true);
        }

        Mage::log("==== Observer completed ====\n", null, 'altcurrency_debug.log', true);
    }
}

