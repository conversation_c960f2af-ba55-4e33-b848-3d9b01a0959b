<?php
$slide_general_addon = Mage::helper('nwdrevslider/framework')->apply_filters('revslider_slide_settings_addons', array(), $slide, $slider);
?>
<!-- THE CONTEXT MENU -->
<div id="context_menu_underlay" class="ignorecontextmenu"></div>
<nav id="context-menu" class="context-menu">
    <ul id="context-menu-first-ul" class="context-menu__items">
        <!-- CURRENT LAYER -->
        <li class="context-menu__item not_in_ctx_bg" id="ctx-m-activelayer">
            <div class="ctx_item_inner"><i id="cx-selected-layer-icon" class="rs-icon-layerimage_n context-menu__link" data-action="nothing"></i><span id="cx-selected-layer-name"><?php echo $this->__('Black Canon DSLR'); ?></span>
                <span data-uniqueid="4" id="ctx-list-of-layer-links" class="ctx-list-of-layer-links">
            <span id="ctx-layer-link-type-element-cs" class="ctx-layer-link-type-element ctx-layer-link-type-element-cs ctx-layer-link-type-3"></span>
            <span class="ctx-list-of-layer-links-inner">
                <span data-linktype="1" data-action="grouplinkchange" class="context-menu__link ctx-layer-link-type-element ctx-layer-link-type-1"></span>
                <span data-linktype="2" data-action="grouplinkchange" class="context-menu__link ctx-layer-link-type-element ctx-layer-link-type-2"></span>
                <span data-linktype="3" data-action="grouplinkchange" class="context-menu__link ctx-layer-link-type-element ctx-layer-link-type-3"></span>
                <span data-linktype="4" data-action="grouplinkchange" class="context-menu__link ctx-layer-link-type-element ctx-layer-link-type-4"></span>
                <span data-linktype="5" data-action="grouplinkchange" class="context-menu__link ctx-layer-link-type-element ctx-layer-link-type-5"></span>
                <span data-linktype="0" data-action="grouplinkchange" class="context-menu__link ctx-layer-link-type-element ctx-layer-link-type-0"></span>
            </span>
        </span>
            </div>
        </li>
        <!-- BACKGROUND CONTEXT - ADD LAYER -->
        <li class="context-menu__item context-with-sub not_in_ctx_layer">
            <div class="ctx_item_inner"><div class="context-menu__link"><i class="rs-icon-addlayer2"></i><span class="cx-layer-name"><?php echo $this->__('Add Layer'); ?></span></div><i class="fa-icon-chevron-right"></i></div>
            <ul class="context-submenu">
                <li class="context-menu__item"><div class="ctx_item_inner"><div class="context-menu__link" data-action="addtextlayer"><i class="rs-icon-layerfont_n"></i><span class="cx-layer-name"><?php echo $this->__('Add Text/Html Layer'); ?></span></div></div></li>
                <li class="context-menu__item"><div class="ctx_item_inner"><div class="context-menu__link" data-action="addimagelayer"><i class="rs-icon-layerimage_n"></i><span class="cx-layer-name"><?php echo $this->__('Add Image Layer'); ?></span></div></div></li>
                <li class="context-menu__item"><div class="ctx_item_inner"><div class="context-menu__link" data-action="addaudiolayer"><i class="rs-icon-layeraudio_n"></i><span class="cx-layer-name"><?php echo $this->__('Add Audio Layer'); ?></span></div></div></li>
                <li class="context-menu__item"><div class="ctx_item_inner"><div class="context-menu__link" data-action="addvideolayer"><i class="rs-icon-layervideo_n"></i><span class="cx-layer-name"><?php echo $this->__('Add Video Layer'); ?></span></div></div></li>
                <li class="context-menu__item"><div class="ctx_item_inner"><div class="context-menu__link" data-action="addbuttonlayer"><i class="rs-icon-layerbutton_n"></i><span class="cx-layer-name"><?php echo $this->__('Add Button Layer'); ?></span></div></div></li>
                <li class="context-menu__item"><div class="ctx_item_inner"><div class="context-menu__link" data-action="addshapelayer"><i class="rs-icon-layershape_n"></i><span class="cx-layer-name"><?php echo $this->__('Add Shape Layer'); ?></span></div></div></li>
                <li class="context-menu__item"><div class="ctx_item_inner"><div class="context-menu__link" data-action="addobjectlayer"><i class="rs-icon-layersvg_n"></i><span class="cx-layer-name"><?php echo $this->__('Add Object Layer'); ?></span></div></div></li>


            </ul>
        </li>
        <!-- ALL LAYERS -->
        <li class="context-menu__item ctx-m-top-divider context-with-sub" id="ctx-select-layer">
            <div class="ctx_item_inner"><div class="context-menu__link" data-action="select layer"><i class="eg-icon-menu"></i><span class="cx-layer-name"><?php echo $this->__('Select Layer'); ?></span></div><i class="fa-icon-chevron-right"></i></div>
            <ul class="context-submenu" id="ctx_list_of_layers">

            </ul>
        </li>
        <!-- LAYER MANIPULATION -->
        <li class="context-menu__item not_in_ctx_bg">
            <div class="ctx_item_inner"><div class="context-menu__link" data-action="delete"><i class="rs-lighttrash"></i><span class="cx-layer-name"><?php echo $this->__('Delete Layer'); ?></span></div></div>
        </li>

        <li class="context-menu__item not_in_ctx_bg">
            <div class="ctx_item_inner"><div class="context-menu__link" data-action="duplicate"><i class="rs-lightcopy"></i><span class="cx-layer-name"><?php echo $this->__('Duplicate Layer'); ?></span></div></div>
        </li>
        <!-- LAYER VISIBILTY AND LOCK -->
        <li class="context-menu__item ctx-m-top-divider context-with-sub">
            <div class="ctx_item_inner"><div class="context-menu__link"><i class="eg-icon-eye"></i><span class="cx-layer-name"><?php echo $this->__('Show Layers'); ?></span></div><i class="fa-icon-chevron-right"></i></div>
            <ul class="context-submenu" id="ctx_list_of_invisibles">
                <li class="context-menu__item">
                    <div class="ctx_item_inner"><div class="context-menu__link" data-action="showalllayer"><i class="fa-icon-asterisk"></i><span class="cx-layer-name"><?php echo $this->__('Show All Layers'); ?></span></div></div>
                </li>
                <li class="context-menu__item">
                    <div class="ctx_item_inner"><div class="context-menu__link" data-action="showonlycurrent"><i class="fa-icon-hand-o-right"></i><span class="cx-layer-name"><?php echo $this->__('Show Only Current Layer'); ?></span></div></div>
                </li>
            </ul>
        </li>

        <li class="context-menu__item not_in_ctx_bg" id="cx-selected-layer-visible">
            <div class="ctx_item_inner"><div class="context-menu__link" data-action="showhide"><i class="eg-icon-eye-off"></i><span class="cx-layer-name"><?php echo $this->__('Hide Layer'); ?></span></div></div>
        </li>

        <li class="context-menu__item not_in_ctx_bg" id="cx-selected-layer-locked">
            <div class="ctx_item_inner"><div class="context-menu__link" data-action="lockunlock"><i class="eg-icon-lock-open"></i><span class="cx-layer-name"><?php echo $this->__('Lock Layer'); ?></span></div></div>
        </li>

        <!-- LAYER SPECIALS -->
        <!-- STYLE OF LAYERS -->
        <li class="context-menu__item ctx-m-top-divider context-with-sub not_in_ctx_bg">
            <div class="ctx_item_inner"><div class="context-menu__link"><i class="fa-icon-paint-brush"></i><span class="cx-layer-name"><?php echo $this->__('Style'); ?></span></div><i class="fa-icon-chevron-right"></i></div>
            <ul class="context-submenu">
                <li class="context-menu__item">
                    <div class="ctx_item_inner"><div class="context-menu__link" data-action="copystyle"><i class="fa-icon-cut"></i><span class="cx-layer-name"><?php echo $this->__('Copy Style'); ?></span></div></div>
                </li>
                <li class="context-menu__item">
                    <div class="ctx_item_inner"><div class="context-menu__link" data-action="pastestyle"><i class="fa-icon-edit"></i><span class="cx-layer-name"><?php echo $this->__('Paste Style'); ?></span></div></div>
                </li>
                <li class="context-menu__item">
                    <div class="ctx_item_inner">
                        <div style="display:inline-block" class="context-menu__link" data-action="nothing"><i class="fa-icon-edit"></i><span class="cx-layer-name"><?php echo $this->__('Inherit Style from'); ?></span></div>
                        <div style="display:inline-block; float:right; margin-top:3px; height:20px" data-action="nothing">
                            <div id="ctx-inheritdesktop" class="ctx-in-one-row context-menu__link" data-size="desktop" data-action="inheritfromdesktop"><i style="width:19px; margin:0px;" class="rs-displays-icon rs-slide-ds-desktop"></i></div>
                            <div id="ctx-inheritnotebook" class="ctx-in-one-row context-menu__link" data-size="notebook" data-action="inheritfromnotebook"><i style="width:26px; margin:0px;" class="rs-displays-icon rs-slide-ds-notebook"></i></div>
                            <div id="ctx-inherittablet" class="ctx-in-one-row context-menu__link" data-size="tablet" data-action="inheritfromtablet"><i style="width:15px; margin:0px;"class="rs-displays-icon rs-slide-ds-tablet"></i></div>
                            <div id="ctx-inheritmobile" class="ctx-in-one-row context-menu__link" data-size="mobile" data-action="inheritfrommobile"><i style="width:17px; margin:0px;"class="rs-displays-icon rs-slide-ds-mobile"></i></div>
                        </div>
                    </div>
                </li>
                <li class="context-menu__item">
                    <div class="ctx_item_inner"><div class="context-menu__link" data-action="advancedcss"><i class="fa-icon-code"></i><span class="cx-layer-name"><?php echo $this->__('Advanced Layer CSS'); ?></span></div></div>
                </li>
                <li class="context-menu__item ctx-m-top-divider _ho_image _ho_group _ho_row _ho_column _ho_svg _ho_audio _ho_video _ho_group _ho_shape _ho_button">
                    <div class="ctx_item_inner context-menu__link noleftmargin" data-action="delegate" data-delegate="ctx_linebreak"><i class="fa-icon-level-down"></i><span class="cx-layer-name"><?php echo $this->__('Line Break'); ?></span><div id="ctx_linebreak" class="ctx-td-switcher context-menu__link" data-action="linebreak"></div></div>
                </li>
                <li class="context-menu__item _ho_image _ho_group _ho_row _ho_column _ho_svg _ho_audio _ho_video _ho_group _ho_notincolumn">
                    <div class="ctx_item_inner context-menu__link noleftmargin" data-action="nothing"><i class="fa-icon-text-width"></i><span class="cx-layer-name"><?php echo $this->__('Display Mode'); ?></span><div class="context-menu__link ctx-td-option-selector-wrapper" data-action="nothing"><div id="ctx_displayblock" class="ctx-td-option-selector context-menu__link selected" data-action="displayblock">Block</div><div id="ctx_displayinline" class="ctx-td-option-selector context-menu__link" data-action="displayinline">Inline</div></div></div>
                </li>

                <li class="context-menu__item ctx-m-top-divider _ho_text  _ho_row _ho_column _ho_audio _ho_shape _ho_button">
                    <div class="ctx_item_inner context-menu__link noleftmargin" data-action="delegate" data-delegate="ctx_keepaspect"><i class="fa-icon-expand"></i><span class="cx-layer-name"><?php echo $this->__('Keep Aspect Ratio'); ?></span><div id="ctx_keepaspect" class="ctx-td-switcher context-menu__link" data-action="aspectratio"></div></div>
                </li>
                <li class="context-menu__item _ho_text _ho_group _ho_row _ho_column _ho_audio _ho_video _ho_shape _ho_button">
                    <div class="ctx_item_inner"><div class="context-menu__link" data-action="resetsize"><i class="fa-icon-rotate-left"></i><span class="cx-layer-name"><?php echo $this->__('Reset Size'); ?></span></div></div>
                </li>
            </ul>
        </li>
        <!-- RESPONSIVENESS -->
        <li class="context-menu__item context-with-sub not_in_ctx_bg">
            <div class="ctx_item_inner"><div class="context-menu__link"><i class="fa-icon-compress"></i><span class="cx-layer-name"><?php echo $this->__('Layer Responsiveness'); ?></span></div><i class="fa-icon-chevron-right"></i></div>
            <ul class="context-submenu">
                <li class="context-menu__item">
                    <div class="ctx_item_inner context-menu__link" data-action="nothing"><span class="cx-layer-name"><?php echo $this->__('Alignment'); ?></span><div class="context-menu__link ctx-td-option-selector-wrapper" data-action="nothing"><div id="ctx_gridbased" class="ctx-td-option-selector context-menu__link selected" data-action="gridbased">Grid Based</div><div id="ctx_slidebased" class="ctx-td-option-selector context-menu__link" data-action="slidebased">Slide Based</div></div></div>
                </li>
                <li class="context-menu__item _ho_row _ho_column">
                    <div class="ctx_item_inner context-menu__link" data-action="delegate" data-delegate="ctx_autoresponsive"><span class="cx-layer-name"><?php echo $this->__('Auto Responsive'); ?></span><div id="ctx_autoresponsive" class="ctx-td-switcher context-menu__link" data-action="autoresponsive"></div></div>
                </li>
                <li class="context-menu__item _ho_row _ho_column">
                    <div class="ctx_item_inner context-menu__link" data-action="delegate" data-delegate="ctx_childrenresponsive"><span class="cx-layer-name"><?php echo $this->__('Children Responsive'); ?></span><div id="ctx_childrenresponsive" class="ctx-td-switcher context-menu__link" data-action="childrenresponsive"></div></div>
                </li>
                <li class="context-menu__item">
                    <div class="ctx_item_inner context-menu__link" data-action="delegate" data-delegate="ctx_responsiveoffset"><span class="cx-layer-name"><?php echo $this->__('Responsive Offset'); ?></span><div id="ctx_responsiveoffset" class="ctx-td-switcher context-menu__link" data-action="responsiveoffset"></div></div>
                </li>
            </ul>
        </li>

        <!-- VISIBILITY -->
        <li class="context-menu__item context-with-sub not_in_ctx_bg">
            <div class="ctx_item_inner"><div class="context-menu__link"><i class="fa-icon-eye"></i><span class="cx-layer-name"><?php echo $this->__('Visibility'); ?></span></div><i class="fa-icon-chevron-right"></i></div>
            <ul class="context-submenu">
                <li class="context-menu__item">
                    <div class="ctx_item_inner context-menu__link noleftmargin" data-action="delegate" data-delegate="ctx_showhideondesktop"><i class="rs-displays-icon rs-slide-ds-desktop"></i><span class="cx-layer-name"><?php echo $this->__('Desktop'); ?></span><div id="ctx_showhideondesktop" class="ctx-td-switcher context-menu__link" data-action="showhideondesktop"></div></div>
                </li>
                <li class="context-menu__item">
                    <div class="ctx_item_inner context-menu__link noleftmargin" data-action="delegate" data-delegate="ctx_showhideonnotebook"><i class="rs-displays-icon rs-slide-ds-notebook"></i><span class="cx-layer-name"><?php echo $this->__('Notebook'); ?></span><div id="ctx_showhideonnotebook" class="ctx-td-switcher context-menu__link" data-action="showhideonnotebook"></div></div>
                </li>
                <li class="context-menu__item">
                    <div class="ctx_item_inner context-menu__link noleftmargin" data-action="delegate" data-delegate="ctx_showhideontablet"><i class="rs-displays-icon rs-slide-ds-tablet"></i><span class="cx-layer-name"><?php echo $this->__('Tablet'); ?></span><div id="ctx_showhideontablet" class="ctx-td-switcher context-menu__link" data-action="showhideontablet"></div></div>
                </li>
                <li class="context-menu__item">
                    <div class="ctx_item_inner context-menu__link noleftmargin" data-action="delegate" data-delegate="ctx_showhideonmobile"><i class="rs-displays-icon rs-slide-ds-mobile"></i><span class="cx-layer-name"><?php echo $this->__('Mobile'); ?></span><div id="ctx_showhideonmobile" class="ctx-td-switcher context-menu__link" data-action="showhideonmobile"></div></div>
                </li>

            </ul>
        </li>

    </ul>
</nav>


<div id="slide_main_settings_wrapper" class="editor_buttons_wrapper  postbox unite-postbox">
	<div class="box-closed tp-accordion" style="border-bottom:5px solid #ddd;">
		<ul class="rs-slide-settings-tabs">
            <?php
            if(!$slide->isStaticSlide()){
                ?>
                <li id="v_sgs_mp_1" data-content="#slide-main-image-settings-content" class="selected"><i style="height:45px" class="rs-mini-layer-icon eg-icon-picture-1 rs-toolbar-icon"></i><span><?php echo $this->__("Main Background"); ?></span></li>
                <?php
            }
            ?>
            <li id="v_sgs_mp_2" class="<?php echo ($slide->isStaticSlide()) ? ' selected' : ''; ?>" data-content="#slide-general-settings-content"><i style="height:45px" class="rs-mini-layer-icon rs-icon-chooser-2 rs-toolbar-icon"></i><?php echo $this->__("General Settings"); ?></li>
            <?php
            if(!$slide->isStaticSlide()){
                ?>
                <li id="v_sgs_mp_3" data-content="#slide-thumbnail-settings-content"><i style="height:45px" class="rs-mini-layer-icon eg-icon-flickr-1 rs-toolbar-icon"></i><?php echo $this->__("Thumbnail"); ?></li>
                <li id="v_sgs_mp_4" data-content="#slide-animation-settings-content" id="slide-animation-settings-content-tab"><i style="height:45px" class="rs-mini-layer-icon rs-icon-chooser-3 rs-toolbar-icon"></i><?php echo $this->__("Slide Animation"); ?></li>
                <li id="v_sgs_mp_5" data-content="#slide-seo-settings-content"><i style="height:45px" class="rs-mini-layer-icon rs-icon-advanced rs-toolbar-icon"></i><?php echo $this->__("Link & Seo"); ?></li>
                <li id="v_sgs_mp_6" data-content="#slide-info-settings-content"><i style="height:45px; font-size:16px;" class="rs-mini-layer-icon eg-icon-info-circled rs-toolbar-icon"></i><?php echo $this->__("Slide Info"); ?></li>
                <li id="main-menu-nav-settings-li" data-content="#slide-nav-settings-content"><i style="height:45px; font-size:16px;" class="rs-mini-layer-icon eg-icon-magic rs-toolbar-icon"></i><?php echo $this->__("Nav. Overwrite"); ?></li>
                <?php
            }
            ?>
            <?php if(!empty($slide_general_addon)){ ?>
                <li data-content="#slide-addon-wrapper"><i style="height:45px; font-size:16px;" class="rs-mini-layer-icon eg-icon-plus-circled rs-toolbar-icon"></i><?php echo $this->__("AddOns"); ?></li>
            <?php } ?>
		</ul>

		<div style="clear:both"></div>
		<script type="text/javascript">
			(function(jQuery) {
			jQuery('document').ready(function() {
				jQuery('.rs-slide-settings-tabs li').click(function() {
					var tw = jQuery('.rs-slide-settings-tabs .selected'),
						tn = jQuery(this);
					jQuery(tw.data('content')).hide(0);
					tw.removeClass("selected");
					tn.addClass("selected");
					jQuery(tn.data('content')).show(0);
				});
			});
			})($nwd_jQuery);
		</script>
	</div>
	<div style="padding:15px">
		<form name="form_slide_params" id="form_slide_params" class="slide-main-settings-form">
            <?php
            if(!$slide->isStaticSlide()){
                ?>
                <div id="slide-main-image-settings-content" class="slide-main-settings-form">

                    <ul class="rs-layer-main-image-tabs" style="display:inline-block; ">
                        <li data-content="#mainbg-sub-source" class="selected"><?php echo $this->__('Source'); ?></li>
                        <li class="mainbg-sub-settings-selector" data-content="#mainbg-sub-setting"><?php echo $this->__('Source Settings'); ?></li>
                        <li class="mainbg-sub-filtres-selector" data-content="#mainbg-sub-filters"><?php echo $this->__('Filters'); ?></li>
                        <li class="mainbg-sub-parallax-selector" data-content="#mainbg-sub-parallax"><?php echo $this->__('Parallax / 3D'); ?></li>
                        <li class="mainbg-sub-kenburns-selector" data-content="#mainbg-sub-kenburns"><?php echo $this->__('Ken Burns'); ?></li>
                    </ul>

                    <div class="tp-clearfix"></div>

                    <script type="text/javascript">
                        (function(jQuery) {
                        jQuery('document').ready(function() {
                            jQuery('.rs-layer-main-image-tabs li').click(function() {
                                var tw = jQuery('.rs-layer-main-image-tabs .selected'),
                                    tn = jQuery(this);
                                jQuery(tw.data('content')).hide(0);
                                tw.removeClass("selected");
                                tn.addClass("selected");
                                jQuery(tn.data('content')).show(0);
                            });
						});
                        })($nwd_jQuery);
                    </script>


                    <!-- SLIDE MAIN IMAGE -->
                    <span id="mainbg-sub-source" style="display:block">
                        <div style="float:none; clear:both; margin-bottom: 10px;"></div>
                        <input type="hidden" name="rs-gallery-type" value="<?php echo Mage::helper('nwdrevslider/framework')->esc_attr($slider_type); ?>" />
                        <span class="diblock bg-settings-block">
                            <!-- IMAGE FROM MEDIAGALLERY -->                                                
							<?php
                            if($slider_type == 'posts' || $slider_type == 'specific_posts' || $slider_type == 'current_post' || $slider_type == 'woocommerce'){
								?>
                                <label><?php echo $this->__("Product Image"); ?></label>
                                <input type="radio" name="background_type" value="image" class="bgsrcchanger" data-callid="tp-bgimagewpsrc" data-imgsettings="on" data-bgtype="image" id="radio_back_image" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'image'); ?>>
                                <?php
                            }elseif($slider_type !== 'gallery'){
                                ?>
                                <label><?php echo $this->__("Stream Image"); ?></label>
                                <input type="radio" name="background_type" value="image" class="bgsrcchanger" data-callid="tp-bgimagewpsrc" data-imgsettings="on" data-bgtype="image" id="radio_back_image" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'image'); ?>>
                                <?php
                                if($slider_type == 'vimeo' || $slider_type == 'youtube' || $slider_type == 'instagram' || $slider_type == 'twitter'){
                                    ?>
                                    <div class="tp-clearfix"></div>
                                    <label><?php echo $this->__("Stream Video"); ?></label>
                                    <input type="radio" name="background_type" value="stream<?php echo $slider_type; ?>" class="bgsrcchanger" data-callid="tp-bgimagewpsrc" data-imgsettings="on" data-bgtype="stream<?php echo $slider_type; ?>" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'stream'.$slider_type); ?>>
                                    <span id="streamvideo_cover" class="streamvideo_cover" style="display:none;margin-left:20px;">
                                        <span style="margin-right: 10px"><?php echo $this->__("Use Cover"); ?></span>
                                        <input type="checkbox" class="tp-moderncheckbox" id="stream_do_cover" name="stream_do_cover" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($stream_do_cover, 'on'); ?>>
                                    </span>
                                    
                                    <div class="tp-clearfix"></div>
                                    <label><?php echo $this->__("Stream Video + Image"); ?></label>
                                    <input type="radio" name="background_type" value="stream<?php echo $slider_type; ?>both" class="bgsrcchanger" data-callid="tp-bgimagewpsrc" data-imgsettings="on" data-bgtype="stream<?php echo $slider_type; ?>both" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'stream'.$slider_type.'both'); ?>>
                                    <span id="streamvideo_cover_both" class="streamvideo_cover_both" style="display:none;margin-left:20px;">
                                        <span style="margin-right: 10px"><?php echo $this->__("Use Cover"); ?></span>
                                        <input type="checkbox" class="tp-moderncheckbox" id="stream_do_cover_both" name="stream_do_cover_both" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($stream_do_cover_both, 'on'); ?>>
                                    </span>
                                    <?php
                                }
                            }else{
                                ?>
                                <label ><?php echo $this->__("Main / Background Image"); ?></label>
                                <input type="radio" name="background_type" value="image" class="bgsrcchanger" data-callid="tp-bgimagewpsrc" data-imgsettings="on" data-bgtype="image" id="radio_back_image" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'image'); ?>>
                                
								<?php
							}
							?>
                            <!-- THE BG IMAGE CHANGED DIV -->
                            <span id="tp-bgimagewpsrc" class="bgsrcchanger-div" style="display:none;margin-left:20px;">
                                <a href="javascript:void(0)" id="button_change_image" class="button-primary revblue" ><i class="fa-icon-photo"></i><?php echo $this->__("Media Library"); ?></a>
                                <a href="javascript:void(0)" id="button_change_image_objlib" class="button-primary revpurple" ><i class="fa-icon-book"></i><?php echo $this->__("Object Library"); ?></a>
                            </span>
							
                            </span>
                            <div class="tp-clearfix"></div>
                            
                            <!-- IMAGE FROM EXTERNAL -->
                            <label><?php echo $this->__("External URL"); ?></label>
                            <input type="radio" name="background_type" value="external" data-callid="tp-bgimageextsrc" data-imgsettings="on" class="bgsrcchanger" data-bgtype="external" id="radio_back_external" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'external'); ?>>

                            <!-- THE BG IMAGE FROM EXTERNAL SOURCE -->
                            <span id="tp-bgimageextsrc" class="bgsrcchanger-div" style="display:none;margin-left:20px;">
                                <input type="text" name="bg_external" id="slide_bg_external" value="<?php echo Mage::helper('nwdrevslider/framework')->esc_url($slideBGExternal); ?>" <?php echo ($bgType != 'external') ? ' class="disabled"' : ''; ?>>
                                <a href="javascript:void(0)" id="button_change_external" class="button-primary revblue" ><?php echo $this->__("Get External"); ?></a>
                            </span>
                            
							<div class="tp-clearfix"></div>
                            
                            <!-- TRANSPARENT BACKGROUND -->
                            <label><?php echo $this->__("Transparent"); ?></label>
                            <input type="radio" name="background_type" value="trans" data-callid="" class="bgsrcchanger" data-bgtype="trans" id="radio_back_trans" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'trans'); ?>>
							<div class="tp-clearfix"></div>

                            <!-- COLORED BACKGROUND -->
                            <label><?php echo $this->__("Colored"); ?></label>
                            <input type="radio" name="background_type" value="solid"  data-callid="tp-bgcolorsrc" class="bgsrcchanger" data-bgtype="solid" id="radio_back_solid" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'solid'); ?>>
                            
                            <!-- THE COLOR SELECTOR -->
                            <span id="tp-bgcolorsrc"  class="bgsrcchanger-div"  style="display:none;margin-left:20px;">
                                <input type="text" data-editing="Background Color" name="bg_color" id="slide_bg_color" class="my-color-field" value="<?php echo $slideBGColor; ?>">
							</span>
							<div class="tp-clearfix"></div>

                            <!-- THE YOUTUBE SELECTOR -->
                            <label id="label_radio_back_youtube"><?php echo $this->__("YouTube Video"); ?></label>
                            <input type="radio" name="background_type" value="youtube"  data-callid="tp-bgyoutubesrc" class="bgsrcchanger" data-bgtype="youtube" id="radio_back_youtube" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'youtube'); ?>>
                            <div class="tp-clearfix"></div>
                            
                            <!-- THE BG IMAGE FROM YOUTUBE SOURCE -->
                            <span id="tp-bgyoutubesrc" class="bgsrcchanger-div" style="display:none; margin-left:20px;">
                                <label style="min-width:180px"><?php echo $this->__("ID:"); ?></label>
                                <input type="text" name="slide_bg_youtube" id="slide_bg_youtube" value="<?php echo $slideBGYoutube; ?>" <?php echo ($bgType != 'youtube') ? ' class="disabled"' : ''; ?>>
                                <?php echo $this->__('example: T8--OggjJKQ'); ?>
                                <div class="tp-clearfix"></div>
                                <label style="min-width:180px"><?php echo $this->__("Cover Image:"); ?></label>
                                <span id="youtube-image-picker"><a href="javascript:void(0)" id="button_change_image_yt" class="button-primary revgreen" ><i class="fa-icon-photo"></i><?php echo $this->__("YouTube Video Poster"); ?></a></span>
							</span>
							<div class="tp-clearfix"></div>

                            <!-- THE VIMEO SELECTOR -->
                            <label id="label_radio_back_vimeo"><?php echo $this->__("Vimeo Video"); ?></label>
                            <input type="radio" name="background_type" value="vimeo"  data-callid="tp-bgvimeosrc" class="bgsrcchanger" data-bgtype="vimeo" id="radio_back_vimeo" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'vimeo'); ?>>
                            <div class="tp-clearfix"></div>

                            <!-- THE BG IMAGE FROM VIMEO SOURCE -->
                            <span id="tp-bgvimeosrc" class="bgsrcchanger-div" style="display:none; margin-left:20px;">
                                <label style="min-width:180px"><?php echo $this->__("ID:"); ?></label>
                                <input type="text" name="slide_bg_vimeo" id="slide_bg_vimeo" value="<?php echo $slideBGVimeo; ?>" <?php echo ($bgType != 'vimeo') ? ' class="disabled"' : ''; ?>>                            
                                <?php echo $this->__('example: 30300114'); ?>
                                <div class="tp-clearfix"></div>
                                <label style="min-width:180px"><?php echo $this->__("Cover Image:"); ?></label>
                                <span id="vimeo-image-picker"></span>
							</span>
							<div class="tp-clearfix"></div>

                            <!-- THE HTML5 SELECTOR -->
                            <label><?php echo $this->__("HTML5 Video"); ?></label>
                            <input type="radio" name="background_type" value="html5"  data-callid="tp-bghtmlvideo" class="bgsrcchanger" data-bgtype="html5" id="radio_back_htmlvideo" <?php Mage::helper('nwdrevslider/framework')->checked($bgType, 'html5'); ?>>
                            <div class="tp-clearfix"></div>
                            <!-- THE BG IMAGE FROM HTML5 SOURCE -->
                            <span id="tp-bghtmlvideo" class="bgsrcchanger-div" style="display:none; margin-left:20px;">
                                
                                <label style="min-width:180px"><?php echo $this->__('MPEG:'); ?></label>
                                <input type="text" name="slide_bg_html_mpeg" id="slide_bg_html_mpeg" value="<?php echo $slideBGhtmlmpeg; ?>" <?php echo ($bgType != 'html5') ? ' class="disabled"' : ''; ?>>
                                <span class="vidsrcchanger-div" style="margin-left:20px;">
                                    <a href="javascript:void(0)" data-inptarget="slide_bg_html_mpeg" class="button_change_video button-primary revblue" ><?php echo $this->__('Change Video'); ?></a>
                                </span>
                                <div class="tp-clearfix"></div>
                                <label style="min-width:180px"><?php echo $this->__('WEBM:'); ?></label>
                                <input type="text" name="slide_bg_html_webm" id="slide_bg_html_webm" value="<?php echo $slideBGhtmlwebm; ?>" <?php echo ($bgType != 'html5') ? ' class="disabled"' : ''; ?>>
                                <span class="vidsrcchanger-div" style="margin-left:20px;">
                                    <a href="javascript:void(0)" data-inptarget="slide_bg_html_webm" class="button_change_video button-primary revblue" ><?php echo $this->__('Change Video'); ?></a>
                                </span>
                                <div class="tp-clearfix"></div>
                                <label style="min-width:180px"><?php echo $this->__('OGV:'); ?></label>
                                <input type="text" name="slide_bg_html_ogv" id="slide_bg_html_ogv" value="<?php echo $slideBGhtmlogv; ?>" <?php echo ($bgType != 'html5') ? ' class="disabled"' : ''; ?>>                            
                                <span class="vidsrcchanger-div" style="margin-left:20px;">
                                    <a href="javascript:void(0)" data-inptarget="slide_bg_html_ogv" class="button_change_video button-primary revblue" ><?php echo $this->__('Change Video'); ?></a>
                                </span>
                                <div class="tp-clearfix"></div>
                                <label style="min-width:180px"><?php echo $this->__('Cover Image:'); ?></label>
                                <span id="html5video-image-picker"></span>
                            </span>
						</span>
					</span>
                    <div id="mainbg-sub-setting" style="display:none">
                        <div style="float:none; clear:both; margin-bottom: 10px;"></div>
                        <div class="rs-img-source-url">
                            <label><?php echo $this->__('Source Info:'); ?></label>
                            <span class="text-selectable" id="the_image_source_url" style="margin-right:20px"></span>
                            <span class="description"><?php echo $this->__('Read Only ! Image can be changed from "Source Tab"'); ?></span>
                        </div>

                        <div class="rs-img-source-size">

                            <label><?php echo $this->__('Image Source Size:'); ?></label>
                            <span style="margin-right:20px">
                                <select name="image_source_type">
                                    <?php
                                    foreach($img_sizes as $imghandle => $imgSize){
                                        $sel = ($bg_image_size == $imghandle) ? ' selected="selected"' : '';
                                        echo '<option value="'.Mage::helper('nwdrevslider/framework')->sanitize_title($imghandle).'"'.$sel.'>'.$imgSize.'</option>';
                                    }
                                    ?>
                                </select>
                            </span>
                        </div>
                        
                        <div id="tp-bgimagesettings" class="bgsrcchanger-div" style="display:none;">
                            <!-- ALT -->
                            <div>
                                <?php $alt_option = RevSliderFunctions::getVal($slideParams, 'alt_option', 'media_library'); ?>
                                <label><?php echo $this->__("Alt:"); ?></label>
                                <select id="alt_option" name="alt_option">
                                    <option value="media_library" <?php Mage::helper('nwdrevslider/framework')->selected($alt_option, 'media_library'); ?>><?php echo $this->__('From Media Library'); ?></option>
                                    <option value="file_name" <?php Mage::helper('nwdrevslider/framework')->selected($alt_option, 'file_name'); ?>><?php echo $this->__('From Filename'); ?></option>
                                    <option value="custom" <?php Mage::helper('nwdrevslider/framework')->selected($alt_option, 'custom'); ?>><?php echo $this->__('Custom'); ?></option>
                                </select>
                                <?php $alt_attr = RevSliderFunctions::getVal($slideParams, 'alt_attr', ''); ?>
                                <input style="<?php echo ($alt_option !== 'custom') ? 'display:none;' : ''; ?>" type="text" id="alt_attr" name="alt_attr" value="<?php echo $alt_attr; ?>">
                            </div>
                            <div class="ext_setting" style="display: none;">
                                <label><?php echo $this->__('Width:')?></label>
                                <input type="text" name="ext_width" value="<?php echo $ext_width; ?>" />
                            </div>
                            <div class="ext_setting" style="display: none;">
                                <label><?php echo $this->__('Height:')?></label>
                                <input type="text" name="ext_height" value="<?php echo $ext_height; ?>" />
                            </div>
                        
                            <!-- TITLE -->
                            <div>
                                <?php $title_option = RevSliderFunctions::getVal($slideParams, 'title_option', 'media_library'); ?>
                                <label><?php echo $this->__('Title:'); ?></label>
                                <select id="title_option" name="title_option">
                                    <option value="media_library" <?php Mage::helper('nwdrevslider/framework')->selected($title_option, 'media_library'); ?>><?php echo $this->__('From Media Library'); ?></option>
                                    <option value="file_name" <?php Mage::helper('nwdrevslider/framework')->selected($title_option, 'file_name'); ?>><?php echo $this->__('From Filename'); ?></option>
                                    <option value="custom" <?php Mage::helper('nwdrevslider/framework')->selected($title_option, 'custom'); ?>><?php echo $this->__('Custom'); ?></option>
                                </select>
                                <?php $title_attr = RevSliderFunctions::getVal($slideParams, 'title_attr', ''); ?>
                                <input style="<?php echo ($title_option !== 'custom') ? 'display:none;' : ''; ?>" type="text" id="title_attr" name="title_attr" value="<?php echo $title_attr; ?>">
                            </div>
                        </div>                    
                        
                        <div id="video-settings" style="display: block;">
                            <div>
                                <label for="video_force_cover" class="video-label"><?php echo $this->__('Force Cover:'); ?></label>
                                <input type="checkbox" class="tp-moderncheckbox" id="video_force_cover" name="video_force_cover" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($video_force_cover, 'on'); ?>>
                            </div>
                            <span id="video_dotted_overlay_wrap">
                                <label for="video_dotted_overlay">
                                    <?php echo $this->__('Dotted Overlay:'); ?>
                                </label>                
                                <select id="video_dotted_overlay" name="video_dotted_overlay" style="width:100px">
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($video_dotted_overlay, 'none'); ?> value="none"><?php echo $this->__('none'); ?></option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($video_dotted_overlay, 'twoxtwo'); ?> value="twoxtwo"><?php echo $this->__('2 x 2 Black'); ?></option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($video_dotted_overlay, 'twoxtwowhite'); ?> value="twoxtwowhite"><?php echo $this->__('2 x 2 White'); ?></option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($video_dotted_overlay, 'threexthree'); ?> value="threexthree"><?php echo $this->__('3 x 3 Black'); ?></option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($video_dotted_overlay, 'threexthreewhite'); ?> value="threexthreewhite"><?php echo $this->__('3 x 3 White'); ?></option>
                                </select>
                                <div style="clear: both;"></div>
                            </span>
							<label for="video_ratio">
                                <?php echo $this->__("Aspect Ratio:"); ?>
							</label>				
                            <select id="video_ratio" name="video_ratio" style="width:100px">
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($video_ratio, '16:9');?> value="16:9"><?php echo $this->__('16:9'); ?></option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($video_ratio, '4:3');?> value="4:3"><?php echo $this->__('4:3'); ?></option>
							</select>
                            <div style="clear: both;"></div>
                            <div>
                                <label for="video_ratio">
                                    <?php echo $this->__("Start At:"); ?>
                                </label>                
                                <input type="text" value="<?php echo $video_start_at; ?>" name="video_start_at"> <?php echo $this->__('For Example: 00:17'); ?>
                                <div style="clear: both;"></div>
                            </div>
                            <div>
                                <label for="video_ratio">
                                    <?php echo $this->__("End At:"); ?>
                                </label>                
                                <input type="text" value="<?php echo $video_end_at; ?>" name="video_end_at"> <?php echo $this->__('For Example: 02:17'); ?>
                                <div style="clear: both;"></div>
                            </div>
                            <div>
                                <label for="video_loop"><?php echo $this->__('Loop Video:'); ?></label>
                                <select id="video_loop" name="video_loop" style="width: 200px;">
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($video_loop, 'none');?> value="none"><?php echo $this->__('Disable'); ?></option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($video_loop, 'loop');?> value="loop"><?php echo $this->__('Loop, Slide is paused'); ?></option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($video_loop, 'loopandnoslidestop');?> value="loopandnoslidestop"><?php echo $this->__('Loop, Slide does not stop'); ?></option>
                                </select>
                            </div>
                            
                            <div>    
                                <label for="video_nextslide"><?php echo $this->__('Next Slide On End:'); ?></label>
                                <input type="checkbox" class="tp-moderncheckbox" id="video_nextslide" name="video_nextslide" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($video_nextslide, 'on'); ?>>
                            </div>
                            <div>
                                <label for="video_force_rewind"><?php echo $this->__('Rewind at Slide Start:'); ?></label>
                                <input type="checkbox" class="tp-moderncheckbox" id="video_force_rewind" name="video_force_rewind" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($video_force_rewind, 'on'); ?>>
                            </div>
                            
                            <div>    
                                <label for="video_mute"><?php echo $this->__('Mute Video:'); ?></label>
                                <input type="checkbox" class="tp-moderncheckbox" id="video_mute" name="video_mute" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($video_mute, 'on'); ?>>
                            </div>
                            
                            <div class="vid-rev-vimeo-youtube video_volume_wrapper">
                                <label for="video_volume"><?php echo $this->__('Video Volume:'); ?></label>
                                <input type="text" id="video_volume" name="video_volume" value="<?php echo Mage::helper('nwdrevslider/framework')->esc_attr($video_volume); ?>">
                            </div>

                            <span id="vid-rev-youtube-options">
                                <div>
                                    <label for="video_speed"><?php echo $this->__('Video Speed:'); ?></label>
                                    <select id="video_speed" name="video_speed" style="width:75px">
                                        <option <?php Mage::helper('nwdrevslider/framework')->selected($video_speed, '0.25');?> value="0.25"><?php echo $this->__('0.25'); ?></option>
                                        <option <?php Mage::helper('nwdrevslider/framework')->selected($video_speed, '0.50');?> value="0.50"><?php echo $this->__('0.50'); ?></option>
                                        <option <?php Mage::helper('nwdrevslider/framework')->selected($video_speed, '1');?> value="1"><?php echo $this->__('1'); ?></option>
                                        <option <?php Mage::helper('nwdrevslider/framework')->selected($video_speed, '1.5');?> value="1.5"><?php echo $this->__('1.5'); ?></option>
                                        <option <?php Mage::helper('nwdrevslider/framework')->selected($video_speed, '2');?> value="2"><?php echo $this->__('2'); ?></option>
                                    </select>
                                </div>
                                <div>
                                    <label><?php echo $this->__('Arguments YouTube:'); ?></label>
                                    <input type="text" id="video_arguments" name="video_arguments" style="width:350px;" value="<?php echo Mage::helper('nwdrevslider/framework')->esc_attr($video_arguments); ?>">
                                </div>
                            </span>
                            <div id="vid-rev-vimeo-options">
                                <label><?php echo $this->__('Arguments Vimeo:'); ?></label>
                                <input type="text" id="video_arguments_vim" name="video_arguments_vim" style="width:350px;" value="<?php echo Mage::helper('nwdrevslider/framework')->esc_attr($video_arguments_vim); ?>">
                            </div>
                        </div>
						
                        <div id="bg-setting-wrap">
                            <div id="bg-setting-bgfit-wrap">
                                <label for="slide_bg_fit"><?php echo $this->__('Background Fit:'); ?></label>
                                <select name="bg_fit" id="slide_bg_fit" style="margin-right:20px">
                                    <option value="cover"<?php Mage::helper('nwdrevslider/framework')->selected($bgFit, 'cover'); ?>>cover</option>
                                    <option value="contain"<?php Mage::helper('nwdrevslider/framework')->selected($bgFit, 'contain'); ?>>contain</option>
                                    <option value="percentage"<?php Mage::helper('nwdrevslider/framework')->selected($bgFit, 'percentage'); ?>>(%, %)</option>
                                    <option value="normal"<?php Mage::helper('nwdrevslider/framework')->selected($bgFit, 'normal'); ?>>normal</option>
                                </select>
                                <input type="text" name="bg_fit_x" style="min-width:54px;width:54px; <?php if($bgFit != 'percentage') echo 'display: none; '; ?> width:60px;margin-right:10px" value="<?php echo $bgFitX; ?>" />
                                <input type="text" name="bg_fit_y" style="min-width:54px;width:54px;  <?php if($bgFit != 'percentage') echo 'display: none; '; ?> width:60px;margin-right:10px"  value="<?php echo $bgFitY; ?>" />
                            </div>
                            <div id="bg-setting-bgpos-def-wrap">
                                <div id="bg-setting-bgpos-wrap">
                                <label for="slide_bg_position" id="bg-position-lbl"><?php echo $this->__('Background Position:'); ?></label>
                                <span id="bg-start-position-wrapper">
                                    <select name="bg_position" id="slide_bg_position">
                                        <option value="center top"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'center top'); ?>>center top</option>
                                        <option value="center right"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'center right'); ?>>center right</option>
                                        <option value="center bottom"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'center bottom'); ?>>center bottom</option>
                                        <option value="center center"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'center center'); ?>>center center</option>
                                        <option value="left top"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'left top'); ?>>left top</option>
                                        <option value="left center"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'left center'); ?>>left center</option>
                                        <option value="left bottom"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'left bottom'); ?>>left bottom</option>
                                        <option value="right top"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'right top'); ?>>right top</option>
                                        <option value="right center"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'right center'); ?>>right center</option>
                                        <option value="right bottom"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'right bottom'); ?>>right bottom</option>
                                        <option value="percentage"<?php Mage::helper('nwdrevslider/framework')->selected($bgPosition, 'percentage'); ?>>(x%, y%)</option>
                                    </select>
                                    <input type="text" name="bg_position_x" style="min-width:54px;width:54px; <?php if($bgPosition != 'percentage') echo 'display: none;'; ?>width:60px;margin-right:10px" value="<?php echo $bgPositionX; ?>" />
                                    <input type="text" name="bg_position_y" style="min-width:54px;width:54px; <?php if($bgPosition != 'percentage') echo 'display: none;'; ?>width:60px;margin-right:10px" value="<?php echo $bgPositionY; ?>" />
                                </span>
                            </div>
                            </div>
                            <div id="bg-setting-bgrep-wrap">
                                <label><?php echo $this->__("Background Repeat:")?></label>
                                <span>
                                    <select name="bg_repeat" id="slide_bg_repeat" style="margin-right:20px">
                                        <option value="no-repeat"<?php Mage::helper('nwdrevslider/framework')->selected($bgRepeat, 'no-repeat'); ?>>no-repeat</option>
                                        <option value="repeat"<?php Mage::helper('nwdrevslider/framework')->selected($bgRepeat, 'repeat'); ?>>repeat</option>
                                        <option value="repeat-x"<?php Mage::helper('nwdrevslider/framework')->selected($bgRepeat, 'repeat-x'); ?>>repeat-x</option>
                                        <option value="repeat-y"<?php Mage::helper('nwdrevslider/framework')->selected($bgRepeat, 'repeat-y'); ?>>repeat-y</option>
                                    </select>
                                </span>
                            </div>
                        </div>
						
                    </div>

                    <span id="mainbg-sub-parallax" style="display:none">
                        <p>
                            <?php 
                            if ($use_parallax=="off") {                        
                                echo '<i style="color:#c0392b">';
                                echo $this->__("Parallax Feature in Slider Settings is deactivated, parallax will be ignored.");
                                echo '</i>';
                            } else {
                            
                                    if ($parallaxisddd=="off") { 
                                ?>
                                <label><?php echo $this->__("Parallax Level:"); ?></label>
                                <select name="slide_parallax_level" id="slide_parallax_level">
                                    <option value="-" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '-'); ?>><?php echo $this->__('No Parallax'); ?></option>
                                    <option value="1" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '1'); ?>>1 - (<?php echo $parallax_level[0]; ?>%)</option>
                                    <option value="2" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '2'); ?>>2 - (<?php echo $parallax_level[1]; ?>%)</option>
                                    <option value="3" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '3'); ?>>3 - (<?php echo $parallax_level[2]; ?>%)</option>
                                    <option value="4" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '4'); ?>>4 - (<?php echo $parallax_level[3]; ?>%)</option>
                                    <option value="5" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '5'); ?>>5 - (<?php echo $parallax_level[4]; ?>%)</option>
                                    <option value="6" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '6'); ?>>6 - (<?php echo $parallax_level[5]; ?>%)</option>
                                    <option value="7" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '7'); ?>>7 - (<?php echo $parallax_level[6]; ?>%)</option>
                                    <option value="8" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '8'); ?>>8 - (<?php echo $parallax_level[7]; ?>%)</option>
                                    <option value="9" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '9'); ?>>9 - (<?php echo $parallax_level[8]; ?>%)</option>
                                    <option value="10" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '10'); ?>>10 - (<?php echo $parallax_level[9]; ?>%)</option>
                                    <option value="11" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '11'); ?>>11 - (<?php echo $parallax_level[10]; ?>%)</option>
                                    <option value="12" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '12'); ?>>12 - (<?php echo $parallax_level[11]; ?>%)</option>
                                    <option value="13" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '13'); ?>>13 - (<?php echo $parallax_level[12]; ?>%)</option>
                                    <option value="14" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '14'); ?>>14 - (<?php echo $parallax_level[13]; ?>%)</option>
                                    <option value="15" <?php Mage::helper('nwdrevslider/framework')->selected($slide_parallax_level, '15'); ?>>15 - (<?php echo $parallax_level[14]; ?>%)</option>                            
                                </select>
                                <?php } else {
                                    if ($parallaxbgfreeze=="off") {
                                ?>                            
                                    <label><?php echo $this->__("Selected 3D Depth:"); ?></label>
                                    <input style="min-width:54px;width:54px" type="text" disabled value="<?php echo $parallax_level[15];?>%" />                            
                                    <span><i><?php echo $this->__('3D Parallax is Enabled via Slider Settings !'); ?></i></span>
                                <?php
                                    } else {                                
                                        ?>
                                            <label><?php echo $this->__("Background 3D is Disabled"); ?></label>
                                            <span style="display: inline-block;vertical-align: middle;line-height:32px"><i><?php echo $this->__('To Enable 3D Parallax for Background please change the Option "BG 3D Disabled" to "OFF" via the Slider Settings !'); ?></i></span>
                                        <?php
                                    }
                                }
                            }
                            ?>
						</p>
						
                    </span>
                    <span id="mainbg-sub-filters" style="display:none">
                        <div style="display:none; margin-bottom: 10px;">
                            <select id="media-filter-type" name="media-filter-type">
                                <option value="none"><?php echo $this->__('No Filter'); ?></option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, '_1977'); ?> value="_1977">1977</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'aden'); ?> value="aden">Aden</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'brooklyn'); ?> value="brooklyn">Brooklyn</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'clarendon'); ?> value="clarendon">Clarendon</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'earlybird'); ?> value="earlybird">Earlybird</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'gingham'); ?> value="gingham">Gingham</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'hudson'); ?> value="hudson">Hudson</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'inkwell'); ?> value="inkwell">Inkwell</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'lark'); ?> value="lark">Lark</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'lofi'); ?> value="lofi">Lo-Fi</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'mayfair'); ?> value="mayfair">Mayfair</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'moon'); ?> value="moon">Moon</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'nashville'); ?> value="nashville">Nashville</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'perpetua'); ?> value="perpetua">Perpetua</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'reyes'); ?> value="reyes">Reyes</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'rise'); ?> value="rise">Rise</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'slumber'); ?> value="slumber">Slumber</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'toaster'); ?> value="toaster">Toaster</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'walden'); ?> value="walden">Walden</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'willow'); ?> value="willow">Willow</option>
                                <option <?php Mage::helper('nwdrevslider/framework')->selected($mediafilter, 'xpro2'); ?> value="xpro2">X-pro II</option>
                            </select>
                        </div>
                        <div id="inst-filter-grid">
							<div data-type="none" class="filter_none inst-filter-griditem selected"><div class="ifgname"><?php echo $this->__('No Filter'); ?></div><div class="inst-filter-griditem-img none" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="_1977" class="filter__1977 inst-filter-griditem "><div class="ifgname">1977</div><div class="inst-filter-griditem-img _1977" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="aden" class="filter_aden inst-filter-griditem "><div class="ifgname">Aden</div><div class="inst-filter-griditem-img aden" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="brooklyn" class="filter_brooklyn inst-filter-griditem "><div class="ifgname">Brooklyn</div><div class="inst-filter-griditem-img brooklyn" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="clarendon" class="filter_clarendon inst-filter-griditem "><div class="ifgname">Clarendon</div><div class="inst-filter-griditem-img clarendon" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="earlybird" class="filter_earlybird inst-filter-griditem "><div class="ifgname">Earlybird</div><div class="inst-filter-griditem-img earlybird" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="gingham" class="filter_gingham inst-filter-griditem "><div class="ifgname">Gingham</div><div class="inst-filter-griditem-img gingham"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="hudson" class="filter_hudson inst-filter-griditem "><div class="ifgname">Hudson</div><div class="inst-filter-griditem-img hudson"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="inkwell" class="filter_inkwell inst-filter-griditem "><div class="ifgname">Inkwell</div><div class="inst-filter-griditem-img inkwell"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="lark" class="filter_lark inst-filter-griditem "><div class="ifgname">Lark</div><div class="inst-filter-griditem-img lark" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="lofi" class="filter_lofi inst-filter-griditem "><div class="ifgname">Lo-Fi</div><div class="inst-filter-griditem-img lofi" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="mayfair" class="filter_mayfair inst-filter-griditem "><div class="ifgname">Mayfair</div><div class="inst-filter-griditem-img mayfair" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="moon" class="filter_moon inst-filter-griditem "><div class="ifgname">Moon</div><div class="inst-filter-griditem-img moon"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="nashville" class="filter_nashville inst-filter-griditem "><div class="ifgname">Nashville</div><div class="inst-filter-griditem-img nashville"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="perpetua" class="filter_perpetua inst-filter-griditem "><div class="ifgname">Perpetua</div><div class="inst-filter-griditem-img perpetua" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="reyes" class="filter_reyes inst-filter-griditem "><div class="ifgname">Reyes</div><div class="inst-filter-griditem-img reyes" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="rise" class="filter_rise inst-filter-griditem "><div class="ifgname">Rise</div><div class="inst-filter-griditem-img rise" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="slumber" class="filter_slumber inst-filter-griditem "><div class="ifgname">Slumber</div><div class="inst-filter-griditem-img slumber" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="toaster" class="filter_toaster inst-filter-griditem "><div class="ifgname">Toaster</div><div class="inst-filter-griditem-img toaster" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="walden" class="filter_walden inst-filter-griditem "><div class="ifgname">Walden</div><div class="inst-filter-griditem-img walden" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="willow" class="filter_willow inst-filter-griditem "><div class="ifgname">Willow</div><div class="inst-filter-griditem-img willow" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                            <div data-type="xpro2" class="filter_xpro2 inst-filter-griditem "><div class="ifgname">X-pro II</div><div class="inst-filter-griditem-img xpro2" style="visibility: inherit; opacity: 1;"></div><div class="inst-filter-griditem-img-noeff"></div></div>
                        </div>
                    </span>
                    <div id="mainbg-sub-kenburns" style="display:none; position:relative">
                        <div>
                            <label><?php echo $this->__('Ken Burns / Pan Zoom:'); ?></label>
                            <input type="checkbox" class="tp-moderncheckbox withlabel" id="kenburn_effect" name="kenburn_effect" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($kenburn_effect, 'on'); ?>>
                        </div>
                        <div id="kenburn_wrapper" <?php echo ($kenburn_effect == 'off') ? 'style="display: none;"' : ''; ?>>
                            <div id="ken_burn_example_wrapper">
                                <div id="kenburn-playpause-wrapper"><i class="eg-icon-play"></i><span><?php echo $this->__('PLAY'); ?></span></div><div id="kenburn-backtoidle"></div>
                                <div id="ken_burn_example">
                                    <div id="ken_burn_slot_example" class="tp-bgimg defaultimg">
                                    </div>
                                </div>
                            </div>

                            <p>
                                <label><?php echo $this->__('Scale: (in %):'); ?></label>
                                <label style="min-width:40px"><?php echo $this->__('From'); ?></label>
                                <input style="min-width:54px;width:54px" class="kb_input_values" type="text" name="kb_start_fit" id="kb_start_fit" value="<?php echo intval($kb_start_fit); ?>" />
                                <label style="min-width:20px"><?php echo $this->__('To')?></label>
                                <input style="min-width:54px;width:54px" class="kb_input_values" type="text" name="kb_end_fit" id="kb_end_fit" value="<?php echo intval($kb_end_fit); ?>" />
                            </p>
                            
                            <p>
                                <label><?php echo $this->__('Horizontal Offsets (+/-):')?></label>
                                <label style="min-width:40px"><?php echo $this->__('From'); ?></label>
                                <input style="min-width:54px;width:54px" type="text" name="kb_start_offset_x" value="<?php echo $kbStartOffsetX; ?>" />
                                <label style="min-width:20px"><?php echo $this->__('To')?></label>
                                <input style="min-width:54px;width:54px" type="text" name="kb_end_offset_x" value="<?php echo $kbEndOffsetX; ?>" />
                                <span><i><?php echo $this->__('Use Negative and Positive Values to offset from the Center !'); ?></i></span>
                            </p>

							<p>
                                <label><?php echo $this->__('Vertical Offsets:')?></label>
                                <label style="min-width:40px"><?php echo $this->__('From'); ?></label>
                                <input style="min-width:54px;width:54px" type="text" name="kb_start_offset_y" value="<?php echo $kbStartOffsetY; ?>" />
                                <label style="min-width:20px"><?php echo $this->__('To')?></label>
                                <input style="min-width:54px;width:54px" type="text" name="kb_end_offset_y" value="<?php echo $kbEndOffsetY; ?>" />
                                <span><i><?php echo $this->__('Use Negative and Positive Values to offset from the Center !'); ?></i></span>
                            </p>
                            
                            <p>
                                <label><?php echo $this->__('Rotation:')?></label>
                                <label style="min-width:40px"><?php echo $this->__('From'); ?></label>
                                <input style="min-width:54px;width:54px" class="kb_input_values" type="text" name="kb_start_offset_x" id="kb_start_offset_x" value="<?php echo $kbStartOffsetX; ?>" />
                                <input style="min-width:54px;width:54px" class="kb_input_values" type="text" name="kb_end_offset_x" id="kb_end_offset_x" value="<?php echo $kbEndOffsetX; ?>" />
                            </p>
                            
                            <p>
                                <label><?php echo $this->__('Blur Filter:')?></label>
                                <label style="min-width:40px"><?php echo $this->__('From'); ?></label>
                                <input style="min-width:54px;width:54px" class="kb_input_values" type="text" name="kb_blur_start" id="kb_blur_start" value="<?php echo $kbBlurStart; ?>" />
                                <label style="min-width:20px"><?php echo $this->__('To')?></label>
                                <input style="min-width:54px;width:54px" class="kb_input_values" type="text" name="kb_blur_end" id="kb_blur_end" value="<?php echo $kbBlurEnd; ?>" />
                            </p>

                            <p>
                                <label><?php echo $this->__('Easing:'); ?></label>
                                <select name="kb_easing" id="kb_easing" class="kb_input_values" >
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Linear.easeNone'); ?> value="Linear.easeNone">Linear.easeNone</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power0.easeIn'); ?> value="Power0.easeIn">Power0.easeIn  (linear)</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power0.easeInOut'); ?> value="Power0.easeInOut">Power0.easeInOut  (linear)</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power0.easeOut'); ?> value="Power0.easeOut">Power0.easeOut  (linear)</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power1.easeIn'); ?> value="Power1.easeIn">Power1.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power1.easeInOut'); ?> value="Power1.easeInOut">Power1.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power1.easeOut'); ?> value="Power1.easeOut">Power1.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power2.easeIn'); ?> value="Power2.easeIn">Power2.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power2.easeInOut'); ?> value="Power2.easeInOut">Power2.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power2.easeOut'); ?> value="Power2.easeOut">Power2.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power3.easeIn'); ?> value="Power3.easeIn">Power3.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power3.easeInOut'); ?> value="Power3.easeInOut">Power3.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power3.easeOut'); ?> value="Power3.easeOut">Power3.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power4.easeIn'); ?> value="Power4.easeIn">Power4.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power4.easeInOut'); ?> value="Power4.easeInOut">Power4.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Power4.easeOut'); ?> value="Power4.easeOut">Power4.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Back.easeIn'); ?> value="Back.easeIn">Back.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Back.easeInOut'); ?> value="Back.easeInOut">Back.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Back.easeOut'); ?> value="Back.easeOut">Back.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Bounce.easeIn'); ?> value="Bounce.easeIn">Bounce.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Bounce.easeInOut'); ?> value="Bounce.easeInOut">Bounce.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Bounce.easeOut'); ?> value="Bounce.easeOut">Bounce.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Circ.easeIn'); ?> value="Circ.easeIn">Circ.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Circ.easeInOut'); ?> value="Circ.easeInOut">Circ.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Circ.easeOut'); ?> value="Circ.easeOut">Circ.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Elastic.easeIn'); ?> value="Elastic.easeIn">Elastic.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Elastic.easeInOut'); ?> value="Elastic.easeInOut">Elastic.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Elastic.easeOut'); ?> value="Elastic.easeOut">Elastic.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Expo.easeIn'); ?> value="Expo.easeIn">Expo.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Expo.easeInOut'); ?> value="Expo.easeInOut">Expo.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Expo.easeOut'); ?> value="Expo.easeOut">Expo.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Sine.easeIn'); ?> value="Sine.easeIn">Sine.easeIn</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Sine.easeInOut'); ?> value="Sine.easeInOut">Sine.easeInOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'Sine.easeOut'); ?> value="Sine.easeOut">Sine.easeOut</option>
                                    <option <?php Mage::helper('nwdrevslider/framework')->selected($kb_easing, 'SlowMo.ease'); ?> value="SlowMo.ease">SlowMo.ease</option>
								</select>
							</p>
							<p>
                                <label><?php echo $this->__('Duration (in ms):')?></label>
                                <input type="text" name="kb_duration" class="kb_input_values"  id="kb_duration" value="<?php echo intval($kb_duration); ?>" />
							</p>
                        </div>
                    </div>
					
                    <input type="hidden" id="image_url" name="image_url" value="<?php echo $imageUrl; ?>" />
                    <input type="hidden" id="image_id" name="image_id" value="<?php echo $imageID; ?>" />
                </div>
            <?php
            }
            ?>    
                <div id="slide-general-settings-content" style="<?php echo (!$slide->isStaticSlide()) ? ' display:none' : ''; ?>">
                    <?php 
                    if(!$slide->isStaticSlide()){
                    ?>
                        <!-- SLIDE TITLE -->
                        <p style="display:none">
                            <?php $title = Mage::helper('nwdrevslider/framework')->esc_attr(stripslashes(RevSliderFunctions::getVal($slideParams, 'title','Slide'))); ?>
                            <label><?php echo $this->__("Slide Title"); ?></label>
                            <input type="text" class="medium" id="title" disabled="disabled" name="title" value="<?php echo $title; ?>">
                            <span class="description"><?php echo $this->__("The title of the slide, will be shown in the slides list."); ?></span>
						</p>

                        <!-- SLIDE DELAY -->
						<p>
                            <?php $delay = RevSliderFunctions::getVal($slideParams, 'delay',''); ?>
                            <label><?php echo $this->__('Slide "Delay":'); ?></label>
                            <input type="text" class="small-text" id="delay" name="delay" value="<?php echo intval($delay); ?>">
                            <span class="description"><?php echo $this->__("A new delay value for the Slide. If no delay defined per slide, the delay defined via Options (9000ms) will be used."); ?></span>
						</p>

                        <!-- SLIDE PAUSE ON PURPOSE -->
						<p>
                            <?php $stoponpurpose = RevSliderFunctions::getVal($slideParams, 'stoponpurpose','published'); ?>
                            <label><?php echo $this->__("Pause Slider:"); ?></label>
                            <select id="stoponpurpose" name="stoponpurpose">
                                <option value="false"<?php Mage::helper('nwdrevslider/framework')->selected($stoponpurpose, 'false'); ?>><?php echo $this->__("Default"); ?></option>
                                <option value="true"<?php Mage::helper('nwdrevslider/framework')->selected($stoponpurpose, 'true'); ?>><?php echo $this->__("Stop Slider Progress"); ?></option>
                            </select>
                            <span class="description"><?php echo $this->__("Stop Slider Progress on this slider or use Slider Settings Defaults"); ?></span>
						</p>


                        <!-- SLIDE PAUSE ON PURPOSE -->
						<p>
                            <?php $invisibleslide = RevSliderFunctions::getVal($slideParams, 'invisibleslide','published'); ?>
                            <label><?php echo $this->__("Slide in Navigation (invisible):"); ?></label>
                            <select id="invisibleslide" name="invisibleslide">
                                <option value="false"<?php Mage::helper('nwdrevslider/framework')->selected($invisibleslide, 'false'); ?>><?php echo $this->__("Show Always"); ?></option>
                                <option value="true"<?php Mage::helper('nwdrevslider/framework')->selected($invisibleslide, 'true'); ?>><?php echo $this->__("Only Via Actions"); ?></option>
                            </select>
                            <span class="description"><?php echo $this->__("Show Slide always or only on Action calls. Invisible slides are not available due Navigation Elements."); ?></span>
						</p>


                        <!-- SLIDE STATE -->
						<p>
                            <?php $state = RevSliderFunctions::getVal($slideParams, 'state','published'); ?>
                            <label><?php echo $this->__("Slide State:"); ?></label>
                            <select id="state" name="state">
                                <option value="published"<?php Mage::helper('nwdrevslider/framework')->selected($state, 'published'); ?>><?php echo $this->__("Published"); ?></option>
                                <option value="unpublished"<?php Mage::helper('nwdrevslider/framework')->selected($state, 'unpublished'); ?>><?php echo $this->__("Unpublished"); ?></option>
                            </select>
                            <span class="description"><?php echo $this->__("The state of the slide. The unpublished slide will be excluded from the slider."); ?></span>
						</p>

        				<!-- STORE VIEW --->
        				<p>
        					<?php $storeId = explode(',', RevSliderFunctions::getVal($slideParams, 'store_id',0)); ?>
                            <label><?php echo $this->__("Store View:"); ?></label>
        					<select id="store_id" name="store_id" multiple="multiple" size="7">
                                <?php foreach (Mage::helper('nwdrevslider')->getStoreOptions() as $_option) : ?>
                                    <?php if ($_option['value'] === 'option_disabled') : ?>
                                        <option disabled="disabled"><?php echo $_option['label']; ?></option>
                                    <?php else : ?>
                                        <option value="<?php echo $_option['value']; ?>" <?php Mage::helper('nwdrevslider/framework')->selected(in_array($_option['value'], $storeId), true); ?>><?php echo $_option['label']; ?></option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
        					</select>
        					<span class="description"><?php echo $this->__("Slide will be visible on selected Store Views."); ?></span>
        				</p>

                        <!-- SLIDE HIDE AFTER LOOP -->
						<p>
                            <?php $hideslideafter = RevSliderFunctions::getVal($slideParams, 'hideslideafter',0); ?>
                            <label><?php echo $this->__('Hide Slide After Loop:'); ?></label>
                            <input type="text" class="small-text" id="hideslideafter" name="hideslideafter" value="<?php echo $hideslideafter; ?>">
                            <span class="description"><?php echo $this->__("After how many Loops should the Slide be hidden ? 0 = Slide is never hidden."); ?></span>
						</p>

                        <!-- HIDE SLIDE ON MOBILE -->
						<p>
                            <?php $hideslideonmobile = RevSliderFunctions::getVal($slideParams, 'hideslideonmobile', 'off'); ?>
                            <label><?php echo $this->__('Hide Slide On Mobile:'); ?></label>
                            <span style="display:inline-block; width:200px; margin-right:20px;line-height:27px">
                                <input type="checkbox" class="tp-moderncheckbox" id="hideslideonmobile" name="hideslideonmobile" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($hideslideonmobile, 'on'); ?>>
                            </span>
                            <span class="description"><?php echo $this->__("Show/Hide this Slide if Slider loaded on Mobile Device."); ?></span>
						</p>

                        <!-- SLIDE VISIBLE FROM -->
						<p>
                            <?php $date_from = RevSliderFunctions::getVal($slideParams, 'date_from',''); ?>
                            <label><?php echo $this->__("Visible from:"); ?></label>
                            <input type="text" class="inputDatePicker" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                            <span class="description"><?php echo $this->__("If set, slide will be visible after the date is reached."); ?></span>
						</p>

                        <!-- SLIDE VISIBLE UNTIL -->
						<p>
                            <?php $date_to = RevSliderFunctions::getVal($slideParams, 'date_to',''); ?>
                            <label><?php echo $this->__("Visible until:"); ?></label>
                            <input type="text" class="inputDatePicker" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                            <span class="description"><?php echo $this->__("If set, slide will be visible till the date is reached."); ?></span>
						</p>

                        
                        <!-- SLIDE VISIBLE FROM -->
                        <p style="display:none">
                            <?php $save_performance = RevSliderFunctions::getVal($slideParams, 'save_performance','off'); ?>
                            <label><?php echo $this->__("Save Performance:"); ?></label>
                            <span style="display:inline-block; width:200px; margin-right:20px;">
                                <input type="checkbox" class="tp-moderncheckbox withlabel" id="save_performance" name="save_performance" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked( $save_performance, "on" ); ?>>
                            </span>
                            <span class="description"><?php echo $this->__("Slide End Transition will first start when last Layer has been removed."); ?></span>
                        </p>
                    <?php
                    } else {
                    ?>
                        <!-- STATIC LAYER OVERFLOW (ON/OFF) -->
						<p>
                            <?php $staticoverflow = RevSliderFunctions::getVal($slideParams, 'staticoverflow','published'); ?>
                            <label><?php echo $this->__("Static Layers Overflow:"); ?></label>
                            <select id="staticoverflow" name="staticoverflow">
                                <option value="visible"<?php Mage::helper('nwdrevslider/framework')->selected($staticoverflow, 'visible'); ?>><?php echo $this->__("Visible"); ?></option>
                                <option value="hidden"<?php Mage::helper('nwdrevslider/framework')->selected($staticoverflow, 'hidden'); ?>><?php echo $this->__("Hidden"); ?></option>
							</select>
                            <span class="description"><?php echo $this->__("Set the Overflow of Static Layers to Visible or Hidden."); ?></span>
						</p>
                        
                        <!-- STATIC LAYER POSITION (FRONT/BACK) -->
                        <p>
                            <?php $staticlayersposition = RevSliderFunctions::getVal($slideParams, 'staticlayersposition','front'); ?>
                            <label><?php echo $this->__("Static Layers Position:"); ?></label>
                            <select id="staticlayersposition" name="staticlayersposition">
                                <option value="front"<?php Mage::helper('nwdrevslider/framework')->selected($staticlayersposition, 'front'); ?>><?php echo $this->__("Front"); ?></option>
                                <option value="back"<?php Mage::helper('nwdrevslider/framework')->selected($staticlayersposition, 'back'); ?>><?php echo $this->__("Back"); ?></option>                        
                            </select>
                            <span class="description"><?php echo $this->__("Choose if Static Layers should appear above or behind Slide content (<a href='https://www.themepunch.com/revslider-doc/main-background/?tab=transparentcolor' target='_blank'>transparent bg's required</a>)"); ?></span>
                        </p>
                        
                    <?php
					}
                    ?>
                </div>
            <?php
            if(!$slide->isStaticSlide()){
				?>

				<!-- THUMBNAIL SETTINGS -->
                <div id="slide-thumbnail-settings-content" style="display:none">
                    <!-- THUMBNAIL SETTINGS -->
                    <div style="margin-top:10px">
                        <?php $slide_thumb = RevSliderFunctions::getVal($slideParams, 'slide_thumb',''); ?>
                        <span style="display:inline-block; vertical-align: top;">
                            <label><?php echo $this->__("Thumbnail:"); ?></label>
                        </span>
                        <div style="display:inline-block; vertical-align: top;">
                            <div id="slide_thumb_button_preview" class="setting-image-preview"><?php
                            if(intval($slide_thumb) > 0){
                                ?>
                                <div style="width:100px;height:70px;background:url('<?php echo Mage::helper('nwdrevslider/framework')->admin_url( 'admin-ajax.php' ); ?>?action=revslider_show_image&amp;img=<?php echo $slide_thumb; ?>&amp;w=100&amp;h=70&amp;t=exact'); background-position:center center; background-size:cover;"></div>
                                <?php
                            }elseif($slide_thumb !== ''){
                                ?>
                                <div style="width:100px;height:70px;background:url('<?php echo Mage::helper('nwdrevslider/images')->imageUrl($slide_thumb); ?>'); background-position:center center; background-size:cover;"></div>
                                <?php
                            }
                            ?></div>
                            <input type="hidden" id="slide_thumb" name="slide_thumb" value="<?php echo $slide_thumb; ?>">
                            <span style="clear:both;display:block"></span>
                            <input type="button" id="slide_thumb_button" style="width:110px !important; display:inline-block;" class="button-image-select button-primary revblue" value="Choose Image" original-title="">
                            <input type="button" id="slide_thumb_button_remove" style="margin-right:20px !important; width:85px !important; display:inline-block;" class="button-image-remove button-primary revred"  value="Remove" original-title="">
                            <span class="description"><?php echo $this->__("Slide Thumbnail. If not set - it will be taken from the slide image."); ?></span>
                        </div>
					</div>
                    <?php $thumb_dimension = RevSliderFunctions::getVal($slideParams, 'thumb_dimension', 'slider'); ?>
                    <?php $thumb_for_admin = RevSliderFunctions::getVal($slideParams, 'thumb_for_admin', 'off'); ?>

                    <p>
                        <span style="display:inline-block; vertical-align: top;">
                            <label><?php echo $this->__("Thumbnail Dimensions:"); ?></label>
                        </span>
                        <select name="thumb_dimension">
                            <option value="slider" <?php Mage::helper('nwdrevslider/framework')->selected($thumb_dimension, 'slider'); ?>><?php echo $this->__('From Slider Settings'); ?></option>
                            <option value="orig" <?php Mage::helper('nwdrevslider/framework')->selected($thumb_dimension, 'orig'); ?>><?php echo $this->__('Original Size'); ?></option>
                        </select>
                        <span class="description"><?php echo $this->__("Width and height of thumbnails can be changed in the Slider Settings -> Navigation -> Thumbs tab."); ?></span>
                    </p>

                    <p style="display:none;" class="show_on_thumbnail_exist">
                        <span style="display:inline-block; vertical-align: top;">
                            <label><?php echo $this->__("Thumbnail Admin Purpose:"); ?></label>
                        </span>
                        <span style="display:inline-block; width:200px; margin-right:20px;line-height:27px">
                            <input type="checkbox" class="tp-moderncheckbox" id="thumb_for_admin" name="thumb_for_admin" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($thumb_for_admin, 'on'); ?>>
                        </span>
                        <span class="description"><?php echo $this->__("Use the Thumbnail also for Admin purposes. This will use the selected Thumbnail to represent the Slide in all Slider Admin area."); ?></span>
                    </p>
                </div>

                <!-- SLIDE ANIMATIONS -->
                <div id="slide-animation-settings-content" style="display:none">

                    <!-- ANIMATION / TRANSITION -->
                    <div id="slide_transition_row">
						<?php
                            $slide_transition = RevSliderFunctions::getVal($slideParams, 'slide_transition',$def_transition);
                            if(!is_array($slide_transition))
                                $slide_transition = explode(',', $slide_transition);
                            
                            if(!is_array($slide_transition)) $slide_transition = array($slide_transition);
                            $transitions = $operations->getArrTransition();
						?>
                        <?php $slot_amount = (array) RevSliderFunctions::getVal($slideParams, 'slot_amount','default'); ?>
                        <?php $transition_rotation = (array) RevSliderFunctions::getVal($slideParams, 'transition_rotation','0'); ?>
                        <?php $transition_duration = (array) RevSliderFunctions::getVal($slideParams, 'transition_duration',$def_transition_duration); ?>
                        <?php $transition_ease_in = (array) RevSliderFunctions::getVal($slideParams, 'transition_ease_in','default'); ?>
                        <?php $transition_ease_out = (array) RevSliderFunctions::getVal($slideParams, 'transition_ease_out','default'); ?>
                        <script type="text/javascript">
                            var choosen_slide_transition = [];
                            <?php
                            $tr_count = count($slide_transition);
                            foreach($slide_transition as $tr){
                                echo 'choosen_slide_transition.push("'.$tr.'");'."\n";
							}
                            ?>
                            var transition_settings = {
                                'slot': [],
                                'rotation': [],
                                'duration': [],
                                'ease_in': [],
                                'ease_out': []
                                };
                            <?php
                            foreach($slot_amount as $sa){
                                echo 'transition_settings["slot"].push("'.$sa.'");'."\n";
							}
                            $sac = count($slot_amount);
                            if($sac < $tr_count){
                                while($sac < $tr_count){
                                    $sac++;
                                    echo 'transition_settings["slot"].push("'.$slot_amount[0].'");'."\n";
                                }
							}
                            
                            foreach($transition_rotation as $sa){
                                echo 'transition_settings["rotation"].push("'.$sa.'");'."\n";
							}
                            $sac = count($transition_rotation);
                            if($sac < $tr_count){
                                while($sac < $tr_count){
                                    $sac++;
                                    echo 'transition_settings["rotation"].push("'.$transition_rotation[0].'");'."\n";
                                }
                            }
                            
                            foreach($transition_duration as $sa){
                                echo 'transition_settings["duration"].push("'.$sa.'");'."\n";
                            }
                            $sac = count($transition_duration);
                            if($sac < $tr_count){
                                while($sac < $tr_count){
                                    $sac++;
                                    echo 'transition_settings["duration"].push("'.$transition_duration[0].'");'."\n";
                                }
                            }
                            
                            foreach($transition_ease_in as $sa){
                                echo 'transition_settings["ease_in"].push("'.$sa.'");'."\n";
                            }
                            $sac = count($transition_ease_in);
                            if($sac < $tr_count){
                                while($sac < $tr_count){
                                    $sac++;
                                    echo 'transition_settings["ease_in"].push("'.$transition_ease_in[0].'");'."\n";
                                }
							}
							
                            foreach($transition_ease_out as $sa){
                                echo 'transition_settings["ease_out"].push("'.$sa.'");'."\n";
                            }
                            $sac = count($transition_ease_out);
                            if($sac < $tr_count){
                                while($sac < $tr_count){
                                    $sac++;
                                    echo 'transition_settings["ease_out"].push("'.$transition_ease_out[0].'");'."\n";
                                }
                            }
                            
                            ?>
                        </script>
                        <div id="slide_transition"  multiple="" size="1" style="z-index: 100;">
                            <?php
                            if(!empty($transitions) && is_array($transitions)){
                                $counter = 0;
                                $optgroupexist = false;
                                $transmenu = '<ul class="slide-trans-menu">';
                                $lastclass = '';
                                $transchecks ='';
                                $listoftrans = '<div class="slide-trans-lists">';
                                
                                foreach($transitions as $tran_handle => $tran_name){

                                    $sel = (in_array($tran_handle, $slide_transition)) ? ' checked="checked"' : '';

                                    if (strpos($tran_handle, 'notselectable') !== false) {
                                        $listoftrans = $listoftrans.$transchecks;
                                        $lastclass = "slide-trans-".$tran_handle;
                                        $transmenu = $transmenu.'<li class="slide-trans-menu-element" data-reference="'.$lastclass.'">'.$tran_name.'</li>';
                                        $transchecks ='';        

                                    }
                                    else
                                        $transchecks = $transchecks.'<div class="slide-trans-checkelement '.$lastclass.'"><input name="slide_transition[]" type="checkbox" data-useval="true" data-name="'.$tran_name.'" value="'.$tran_handle.'"'.$sel.'>'.$tran_name.'</div>';
								}

                                $listoftrans = $listoftrans.$transchecks;
                                $transmenu = $transmenu."</ul>";
                                $listoftrans = $listoftrans."</div>";
                                echo $transmenu;
                                echo $listoftrans;                            
                            }
                            ?>
                            
                            <div class="slide-trans-example">
                                <div class="slide-trans-example-inner">
                                    <div class="oldslotholder" style="overflow:hidden;width:100%;height:100%;position:absolute;top:0px;left:0px;z-index:1">
                                        <div class="tp-bgimg defaultimg slide-transition-example"></div>
                                    </div>
                                    <div class="slotholder" style="overflow:hidden;width:100%;height:100%;position:absolute;top:0px;left:0px;z-index:1">
                                        <div class="tp-bgimg defaultimg slide-transition-example"></div>
                                    </div>
								</div>
							</div>
                            <div class="slide-trans-cur-selected">
                                <p><?php echo $this->__("Used Transitions (Order in Loops)"); ?></p>
                                <ul class="slide-trans-cur-ul">
                                </ul>
                            </div>
                            <div class="slide-trans-cur-selected-settings">
                                <!-- SLOT AMOUNT -->
                                
                                <label><?php echo $this->__("Slot / Box Amount:"); ?></label>
                                <input type="text" class="small-text input-deepselects" id="slot_amount" name="slot_amount" value="<?php echo $slot_amount[0]; ?>" data-selects="1||Random||Custom||Default" data-svalues ="1||random||3||default" data-icons="thumbs-up||shuffle||wrench||key">
                                <span class="tp-clearfix"></span>
                                <span class="description"><?php echo $this->__("# of slots/boxes the slide is divided into or divided by."); ?></span>
                                <span class="tp-clearfix"></span>
                                
                                <!-- ROTATION -->
                                
                                <label><?php echo $this->__("Slot Rotation:"); ?></label>
                                <input type="text" class="small-text input-deepselects" id="transition_rotation" name="transition_rotation" value="<?php echo $transition_rotation[0]; ?>" data-selects="0||Random||Custom||Default||45||90||180||270||360" data-svalues ="0||random||-75||default||45||90||180||270||360" data-icons="thumbs-up||shuffle||wrench||key||star-empty||star-empty||star-empty||star-empty||star-empty">
                                <span class="tp-clearfix"></span>
                                <span class="description"><?php echo $this->__("Start Rotation of Transition (deg)."); ?></span>
                                <span class="tp-clearfix"></span>

                                <!-- DURATION -->
                                
                                <label><?php echo $this->__("Animation Duration:"); ?></label>
                                <input type="text" class="small-text input-deepselects" id="transition_duration" name="transition_duration" value="<?php echo $transition_duration[0]; ?>" data-selects="300||Random||Custom||Default" data-svalues ="500||random||650||default" data-icons="thumbs-up||shuffle||wrench||key">
                                <span class="tp-clearfix"></span>
                                <span class="description"><?php echo $this->__("The duration of the transition."); ?></span>
                                <span class="tp-clearfix"></span>

                                <!-- IN EASE -->
                                
                                <label><?php echo $this->__("Easing In:"); ?></label>
                                <select name="transition_ease_in">
                                        <option value="default">Default</option>
                                        <option value="Linear.easeNone">Linear.easeNone</option>
                                        <option value="Power0.easeIn">Power0.easeIn  (linear)</option>
                                        <option value="Power0.easeInOut">Power0.easeInOut  (linear)</option>
                                        <option value="Power0.easeOut">Power0.easeOut  (linear)</option>
                                        <option value="Power1.easeIn">Power1.easeIn</option>
                                        <option value="Power1.easeInOut">Power1.easeInOut</option>
                                        <option value="Power1.easeOut">Power1.easeOut</option>
                                        <option value="Power2.easeIn">Power2.easeIn</option>
                                        <option value="Power2.easeInOut">Power2.easeInOut</option>
                                        <option value="Power2.easeOut">Power2.easeOut</option>
                                        <option value="Power3.easeIn">Power3.easeIn</option>
                                        <option value="Power3.easeInOut">Power3.easeInOut</option>
                                        <option value="Power3.easeOut">Power3.easeOut</option>
                                        <option value="Power4.easeIn">Power4.easeIn</option>
                                        <option value="Power4.easeInOut">Power4.easeInOut</option>
                                        <option value="Power4.easeOut">Power4.easeOut</option>
                                        <option value="Back.easeIn">Back.easeIn</option>
                                        <option value="Back.easeInOut">Back.easeInOut</option>
                                        <option value="Back.easeOut">Back.easeOut</option>
                                        <option value="Bounce.easeIn">Bounce.easeIn</option>
                                        <option value="Bounce.easeInOut">Bounce.easeInOut</option>
                                        <option value="Bounce.easeOut">Bounce.easeOut</option>
                                        <option value="Circ.easeIn">Circ.easeIn</option>
                                        <option value="Circ.easeInOut">Circ.easeInOut</option>
                                        <option value="Circ.easeOut">Circ.easeOut</option>
                                        <option value="Elastic.easeIn">Elastic.easeIn</option>
                                        <option value="Elastic.easeInOut">Elastic.easeInOut</option>
                                        <option value="Elastic.easeOut">Elastic.easeOut</option>
                                        <option value="Expo.easeIn">Expo.easeIn</option>
                                        <option value="Expo.easeInOut">Expo.easeInOut</option>
                                        <option value="Expo.easeOut">Expo.easeOut</option>
                                        <option value="Sine.easeIn">Sine.easeIn</option>
                                        <option value="Sine.easeInOut">Sine.easeInOut</option>
                                        <option value="Sine.easeOut">Sine.easeOut</option>
                                        <option value="SlowMo.ease">SlowMo.ease</option>
                                </select>
                                <span class="tp-clearfix"></span>
                                <span class="description"><?php echo $this->__("The easing of Appearing transition."); ?></span>
                                <span class="tp-clearfix"></span>

                                <!-- OUT EASE -->
                                
                                <label><?php echo $this->__("Easing Out:"); ?></label>
                                <select name="transition_ease_out">
                                        <option value="default">Default</option>
                                        <option value="Linear.easeNone">Linear.easeNone</option>
                                        <option value="Power0.easeIn">Power0.easeIn  (linear)</option>
                                        <option value="Power0.easeInOut">Power0.easeInOut  (linear)</option>
                                        <option value="Power0.easeOut">Power0.easeOut  (linear)</option>
                                        <option value="Power1.easeIn">Power1.easeIn</option>
                                        <option value="Power1.easeInOut">Power1.easeInOut</option>
                                        <option value="Power1.easeOut">Power1.easeOut</option>
                                        <option value="Power2.easeIn">Power2.easeIn</option>
                                        <option value="Power2.easeInOut">Power2.easeInOut</option>
                                        <option value="Power2.easeOut">Power2.easeOut</option>
                                        <option value="Power3.easeIn">Power3.easeIn</option>
                                        <option value="Power3.easeInOut">Power3.easeInOut</option>
                                        <option value="Power3.easeOut">Power3.easeOut</option>
                                        <option value="Power4.easeIn">Power4.easeIn</option>
                                        <option value="Power4.easeInOut">Power4.easeInOut</option>
                                        <option value="Power4.easeOut">Power4.easeOut</option>
                                        <option value="Back.easeIn">Back.easeIn</option>
                                        <option value="Back.easeInOut">Back.easeInOut</option>
                                        <option value="Back.easeOut">Back.easeOut</option>
                                        <option value="Bounce.easeIn">Bounce.easeIn</option>
                                        <option value="Bounce.easeInOut">Bounce.easeInOut</option>
                                        <option value="Bounce.easeOut">Bounce.easeOut</option>
                                        <option value="Circ.easeIn">Circ.easeIn</option>
                                        <option value="Circ.easeInOut">Circ.easeInOut</option>
                                        <option value="Circ.easeOut">Circ.easeOut</option>
                                        <option value="Elastic.easeIn">Elastic.easeIn</option>
                                        <option value="Elastic.easeInOut">Elastic.easeInOut</option>
                                        <option value="Elastic.easeOut">Elastic.easeOut</option>
                                        <option value="Expo.easeIn">Expo.easeIn</option>
                                        <option value="Expo.easeInOut">Expo.easeInOut</option>
                                        <option value="Expo.easeOut">Expo.easeOut</option>
                                        <option value="Sine.easeIn">Sine.easeIn</option>
                                        <option value="Sine.easeInOut">Sine.easeInOut</option>
                                        <option value="Sine.easeOut">Sine.easeOut</option>
                                        <option value="SlowMo.ease">SlowMo.ease</option>
                                </select>
                                <span class="tp-clearfix"></span>
                                <span class="description"><?php echo $this->__("The easing of Disappearing transition."); ?></span>
                                
                            </div>

						</div>
                        
                    </div>

                    
                </div>
                
                <!-- SLIDE BASIC INFORMATION -->
                <div id="slide-nav-settings-content" style="display:none">        
                    <ul class="rs-layer-nav-settings-tabs" style="display:inline-block; ">
                        <li id="custom-nav-arrows-tab-selector" data-content="arrows" class="selected"><?php echo $this->__('Arrows'); ?></li>
                        <li id="custom-nav-bullets-tab-selector" data-content="bullets"><?php echo $this->__('Bullets'); ?></li>
                        <li id="custom-nav-tabs-tab-selector" data-content="tabs"><?php echo $this->__('Tabs'); ?></li>
                        <li id="custom-nav-thumbs-tab-selector" data-content="thumbs"><?php echo $this->__('Thumbnails'); ?></li>
                    </ul>

                    <div class="tp-clearfix"></div>



                    <ul id="navigation-placeholder-wrapper">                    
                        <?php
                        $ph_types = array('navigation_arrow_style' => 'arrows', 'navigation_bullets_style' => 'bullets', 'tabs_style' => 'tabs', 'thumbnails_style' => 'thumbs');
                        foreach($ph_types as $phname => $pht){
							
                            $ph_arr_type = $slider->getParam($phname,'');
							
                            $ph_init = array();
                            foreach($arr_navigations as $nav){
                                if($nav['handle'] == $ph_arr_type){ //check for settings, placeholders
                                    if(isset($nav['settings']) && isset($nav['settings']['placeholders'])){
                                        foreach($nav['settings']['placeholders'] as $placeholder){
                                            if(empty($placeholder)) continue;
                                            
                                            $ph_vals = array();
                                            
                                            //$placeholder['type']
                                            foreach($placeholder['data'] as $k => $d){
                                                $get_from = RevSliderFunctions::getVal($slideParams, 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-'.$k.'-slide', 'off');
                                                if($get_from == 'on'){ //get from Slide
													$ph_vals[$k] = stripslashes(RevSliderFunctions::getVal($slideParams, 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-'.$k, $d));											
                                                }else{ ////get from Slider
													$ph_vals[$k] = stripslashes($slider->getParam('ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-'.$k, $d));											
                                                }
                                            }                                        
                                            ?>
                                            <?php if ($placeholder['nav-type'] === $pht) { ?>
                                                <li class="custom-nav-types nav-type-<?php echo $placeholder['nav-type']; ?>">                                        
                                                <?php
                                                switch($placeholder['type']){
                                                    case 'color':
                                                        $get_from = RevSliderFunctions::getVal($slideParams, 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-color-slide', 'off');
                                                        ?>
                                                        <label><?php echo $placeholder['title']; ?></label>
                                                        <input type="checkbox" class="tp-moderncheckbox" id="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-color-slide'; ?>" name="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-color-slide'; ?>" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($get_from, 'on'); ?>>
                                                        <input type="text" name="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-color'; ?>" class="my-alphacolor-field" value="<?php echo $ph_vals['color']; ?>">                                                                                                
                                                        <?php
                                                    break;

                                                    case 'color-rgba':
                                                        $get_from = RevSliderFunctions::getVal($slideParams, 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-color-rgba-slide', 'off');
                                                        ?>
                                                        <label><?php echo $placeholder['title']; ?></label>
                                                        <input type="checkbox" class="tp-moderncheckbox" id="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-color-rgba-slide'; ?>" name="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-color-rgba-slide'; ?>" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($get_from, 'on'); ?>>
                                                        <input type="text" name="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-color-rgba'; ?>" class="my-alphacolor-field" value="<?php echo $ph_vals['color-rgba']; ?>">                                                                                                
                                                        <?php
                                                    break;
                                                    case 'font-family':
                                                        $get_from_font_family = RevSliderFunctions::getVal($slideParams, 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-font_family-slide', 'off');
                                                        ?>
                                                        <label><?php echo $placeholder['title']; ?></label>
                                                        <input type="checkbox" class="tp-moderncheckbox" id="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-font_family-slide'; ?>" name="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-font_family-slide'; ?>" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($get_from_font_family, 'on'); ?>>
                                                        <select style="width: 140px;" name="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-font_family'; ?>">
                                                        <?php
                                                        $font_families = $operations->getArrFontFamilys();
                                                        foreach($font_families as $handle => $name){
                                                            if($name['label'] == 'Dont Show Me') continue;
                                                            
                                                            echo '<option value="'. Mage::helper('nwdrevslider/framework')->esc_attr($name['label']) .'"';
                                                            if($ph_vals['font_family'] == Mage::helper('nwdrevslider/framework')->esc_attr($name['label'])){
                                                                echo ' selected="selected"';
                                                            }
                                                            echo '>'. Mage::helper('nwdrevslider/framework')->esc_attr($name['label']) .'</option>';                                                    
                                                        }
                                                        ?>
                                                        </select>                                                
                                                        <?php
                                                    break;
                                                    case 'custom':
                                                        $get_from_custom = RevSliderFunctions::getVal($slideParams, 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-custom-slide', 'off');
                                                        ?>
                                                        <label><?php echo $placeholder['title']; ?></label>
                                                        <input type="checkbox" class="tp-moderncheckbox" id="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-custom-slide'; ?>" name="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-custom-slide'; ?>" data-unchecked="off" <?php Mage::helper('nwdrevslider/framework')->checked($get_from_custom, 'on'); ?>>
                                                        <input type="text" name="<?php echo 'ph-'.$ph_arr_type.'-'.$pht.'-'.$placeholder['handle'].'-custom'; ?>" value="<?php echo $ph_vals['custom']; ?>">                                                
                                                        <?php
                                                    break;
                                                }
                                                ?>
                                                </li>
                                            <?php
                                            }
                                            ?>
                                            <?php
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                        ?>
                        
                        
                        
                    </ul>
                    <p style="margin-top:25px"><i><?php echo $this->__("The Custom Settings are always depending on the current selected Navigation Elements in Slider Settings, and will only be active on the current Slide."); ?></i></p>
                    <script type="text/javascript">
                    (function(jQuery) {
                        jQuery(document).ready(function() {
                            if (jQuery('.custom-nav-types.nav-type-arrows').length==0)
                                jQuery('#custom-nav-arrows-tab-selector').remove();

                            if (jQuery('.custom-nav-types.nav-type-bullets').length==0)
                                jQuery('#custom-nav-bullets-tab-selector').remove();

                            if (jQuery('.custom-nav-types.nav-type-tabs').length==0)
                                jQuery('#custom-nav-tabs-tab-selector').remove();

                            if (jQuery('.custom-nav-types.nav-type-thumbs').length==0)
                                jQuery('#custom-nav-thumbs-tab-selector').remove();

                            if (jQuery('#navigation-placeholder-wrapper li').length==0)
                                jQuery('#main-menu-nav-settings-li').remove();
							
                        
                            jQuery('document').ready(function() {
                                jQuery('.rs-layer-nav-settings-tabs li').click(function() {                            
                                    var tn = jQuery(this);                            
                                    jQuery('.custom-nav-types').hide();
                                    jQuery('.custom-nav-types.nav-type-'+tn.data('content')).show();
                                    jQuery('.rs-layer-nav-settings-tabs .selected').removeClass("selected");
                                    tn.addClass("selected");                            
                                });
                            });
                            setTimeout(function() {
                                jQuery('.rs-layer-nav-settings-tabs li:nth-child(1)').click();
                            },100)
							
                        });
                    })($nwd_jQuery);
                    </script>
				</div>
                <?php
            }
            ?>
            <!-- SLIDE ADDON WRAP -->
            <div id="slide-addon-wrapper" style="margin:-15px; display:none">
                <div id="rs-addon-wrapper-button-row">
                    <span class="rs-layer-toolbar-box" style="padding:5px 20px"><?php echo $this->__('Select Add-on'); ?></span>
					<?php
                    if(!empty($slide_general_addon)){
                        foreach($slide_general_addon as $rs_addon_handle => $rs_addon){
                            ?>
                            <span class="rs-layer-toolbar-box">
                                <span id="rs-addon-settings-trigger-<?php echo Mage::helper('nwdrevslider/framework')->esc_attr($rs_addon_handle); ?>" class="rs-addon-settings-trigger"><?php echo Mage::helper('nwdrevslider/framework')->esc_attr($rs_addon['title']); ?></span>
                            </span>
                            <?php
                        }
                    }
                    ?>
                </div>
                <div style="border-top:1px solid #ddd;">
                    <?php
                    if(!empty($slide_general_addon)){
                        foreach($slide_general_addon as $rs_addon_handle => $rs_addon){
                            ?>
                            <div id="rs-addon-settings-trigger-<?php echo Mage::helper('nwdrevslider/framework')->esc_attr($rs_addon_handle); ?>-settings" class="rs-addon-settings-wrapper-settings" style="display: none;">
                                <?php echo $rs_addon['markup']; ?>
                                <script type="text/javascript">
                                    <?php echo $rs_addon['javascript']; ?>
                                </script>
                            </div>
                            <?php
                        }
					}
					?>
                    <script type="text/javascript">
                        (function(jQuery) {
                        jQuery('.rs-addon-settings-trigger').click(function(){
                            var show_addon = jQuery(this).attr('id');
                            jQuery('.rs-addon-settings-trigger').removeClass("selected");
                            jQuery(this).addClass("selected");
                            jQuery('.rs-addon-settings-wrapper-settings').hide();
                            jQuery('#'+show_addon+'-settings').show();
                        });
                        })($nwd_jQuery);
                    </script>
                </div>
			</div>
            <?php 
            if(!$slide->isStaticSlide()){
                ?>
            
                <!-- SLIDE BASIC INFORMATION -->
                <div id="slide-info-settings-content" style="display:none">
                    <ul>
                        <?php
                        for($i=1;$i<=10;$i++){
                            ?>
                            <li>
                                <label><?php echo $this->__('Parameter'); echo ' '.$i; ?></label> <input type="text" name="params_<?php echo $i; ?>" value="<?php echo stripslashes(Mage::helper('nwdrevslider/framework')->esc_attr(RevSliderFunctions::getVal($slideParams, 'params_'.$i,''))); ?>">
                                <?php echo $this->__('Max. Chars'); ?> <input type="text" style="width: 50px; min-width: 50px;" name="params_<?php echo $i; ?>_chars" value="<?php echo Mage::helper('nwdrevslider/framework')->esc_attr(RevSliderFunctions::getVal($slideParams, 'params_'.$i.'_chars',10, RevSlider::FORCE_NUMERIC)); ?>">
                                <?php if($slider_type !== 'gallery'){ ?><i class="eg-icon-pencil rs-param-meta-open" data-curid="<?php echo $i; ?>"></i><?php } ?>
                            </li>
                            <?php
                        }
                        ?>
                    </ul>
                    
                    <!-- BASIC DESCRIPTION -->
                    <p>
                        <?php $slide_description = stripslashes(RevSliderFunctions::getVal($slideParams, 'slide_description', '')); ?>
                        <label><?php echo $this->__("Description of Slider:"); ?></label>

                        <textarea name="slide_description" style="height: 425px; width: 100%"><?php echo $slide_description; ?></textarea>
                        <span class="description"><?php echo $this->__('Define a description here to show at the navigation if enabled in Slider Settings'); ?></span>
                    </p>
                </div>

                <!-- SLIDE SEO INFORMATION -->
                <div id="slide-seo-settings-content" style="display:none">
                    <!-- CLASS -->
					<p>
                        <?php $class_attr = RevSliderFunctions::getVal($slideParams, 'class_attr',''); ?>
                        <label><?php echo $this->__("Class:"); ?></label>
                        <input type="text" class="" id="class_attr" name="class_attr" value="<?php echo $class_attr; ?>">
                        <span class="description"><?php echo $this->__('Adds a unique class to the li of the Slide like class="rev_special_class" (add only the classnames, seperated by space)'); ?></span>
					</p>

                    <!-- ID -->
                    <p>
                        <?php $id_attr = RevSliderFunctions::getVal($slideParams, 'id_attr',''); ?>
                        <label><?php echo $this->__("ID:"); ?></label>
                        <input type="text" class="" id="id_attr" name="id_attr" value="<?php echo $id_attr; ?>">
                        <span class="description"><?php echo $this->__('Adds a unique ID to the li of the Slide like id="rev_special_id" (add only the id)'); ?></span>
                    </p>

                    <!-- CUSTOM FIELDS -->
                    <p>
                        <?php $data_attr = stripslashes(RevSliderFunctions::getVal($slideParams, 'data_attr','')); ?>
                        <label><?php echo $this->__("Custom Fields:"); ?></label>
                        <textarea id="data_attr" name="data_attr"><?php echo $data_attr; ?></textarea>
                        <span class="description"><?php echo $this->__('Add as many attributes as you wish here. (i.e.: data-layer="firstlayer" data-custom="somevalue").'); ?></span>
                    </p>

                    <!-- Enable Link -->
                    <p>
                        <?php $enable_link = RevSliderFunctions::getVal($slideParams, 'enable_link','false'); ?>
                        <label><?php echo $this->__("Enable Link:"); ?></label>
                        <select id="enable_link" name="enable_link">
                            <option value="true"<?php Mage::helper('nwdrevslider/framework')->selected($enable_link, 'true'); ?>><?php echo $this->__("Enable"); ?></option>
                            <option value="false"<?php Mage::helper('nwdrevslider/framework')->selected($enable_link, 'false'); ?>><?php echo $this->__("Disable"); ?></option>
                        </select>
                        <span class="description"><?php echo $this->__('Link the Full Slide to an URL or Action.'); ?></span>
                    </p>
					
                    <div class="rs-slide-link-setting-wrapper">
                        <!-- Link Type -->
						<p>
                            <?php $enable_link = RevSliderFunctions::getVal($slideParams, 'link_type','regular'); ?>
                            <label><?php echo $this->__("Link Type:"); ?></label>
                            <span style="display:inline-block; width:200px; margin-right:20px;">
                                <input type="radio" id="link_type_1" value="regular" name="link_type"<?php Mage::helper('nwdrevslider/framework')->checked($enable_link, 'regular'); ?>><span style="line-height:30px; vertical-align: middle; margin:0px 20px 0px 10px;"><?php echo $this->__('Regular'); ?></span>
                                <input type="radio" id="link_type_2" value="slide" name="link_type"<?php Mage::helper('nwdrevslider/framework')->checked($enable_link, 'slide'); ?>><span style="line-height:30px; vertical-align: middle; margin:0px 20px 0px 10px;"><?php echo $this->__('To Slide'); ?></span>
                            </span>
                            <span class="description"><?php echo $this->__('Regular - Link to URL,  To Slide - Call a Slide Action'); ?></span>
						</p>

                        <div class="rs-regular-link-setting-wrap">
                            <!-- SLIDE LINK -->
                            <p>
                                <?php $val_link = RevSliderFunctions::getVal($slideParams, 'link',''); ?>
                                <label><?php echo $this->__("Slide Link:"); ?></label>
                                <input type="text" id="rev_link" name="link" value="<?php echo $val_link; ?>">
                                <span class="description"><?php echo $this->__('A link on the whole slide pic (use {{link}} or {{meta:somemegatag}} in template sliders to link to a post or some other meta)'); ?></span>
                            </p>
                        
                            <!-- LINK TARGET -->
                            <p>
                                <?php $link_open_in = RevSliderFunctions::getVal($slideParams, 'link_open_in','same'); ?>
                                <label><?php echo $this->__("Link Target:"); ?></label>
                                <select id="link_open_in" name="link_open_in">
                                    <option value="same"<?php Mage::helper('nwdrevslider/framework')->selected($link_open_in, 'same'); ?>><?php echo $this->__('Same Window'); ?></option>
                                    <option value="new"<?php Mage::helper('nwdrevslider/framework')->selected($link_open_in, 'new'); ?>><?php echo $this->__('New Window'); ?></option>
                                </select>
                                <span class="description"><?php echo $this->__('The target of the slide link.'); ?></span>
                            </p>
                        </div>
                        <!-- LINK TO SLIDE -->
                        <p class="rs-slide-to-slide">
                            <?php $slide_link = RevSliderFunctions::getVal($slideParams, 'slide_link','nothing');
                            //num_slide_link
                            $arrSlideLink = array();
                            $arrSlideLink["nothing"] = __("-- Not Chosen --");
                            $arrSlideLink["next"] = __("-- Next Slide --");
                            $arrSlideLink["prev"] = __("-- Previous Slide --");

                            $arrSlideLinkLayers = $arrSlideLink;
                            $arrSlideLinkLayers["scroll_under"] = __("-- Scroll Below Slider --");
                            $arrSlideNames = array();
                            if(isset($slider) && $slider->isInited())
                                $arrSlideNames = $slider->getArrSlideNames();
                            if(!empty($arrSlideNames) && is_array($arrSlideNames)){
                                foreach($arrSlideNames as $slideNameID=>$arr){
									$slideName = Mage::helper('nwdrevslider/framework')->esc_attr(stripslashes($arr["title"]));
                                    $arrSlideLink[$slideNameID] = $slideName;
                                    $arrSlideLinkLayers[$slideNameID] = $slideName;
								}
							}
							?>
                            <label><?php echo $this->__("Link To Slide:"); ?></label>
                            <select id="slide_link" name="slide_link">
                                <?php
                                if(!empty($arrSlideLinkLayers) && is_array($arrSlideLinkLayers)){
                                    foreach($arrSlideLinkLayers as $link_handle => $link_name){
                                        $sel = ($link_handle == $slide_link) ? ' selected="selected"' : '';
                                        echo '<option value="'.$link_handle.'"'.$sel.'>'.$link_name.'</option>';
                                    }
                                }
                                ?>
                            </select>
                            <span class="description"><?php echo $this->__('Call Slide Action'); ?></span>
                        </p>
                        <!-- Link POSITION -->
                        <p>
                            <?php $link_pos = RevSliderFunctions::getVal($slideParams, 'link_pos','front'); ?>
                            <label><?php echo $this->__("Link Sensibility:"); ?></label>
                            <span style="display:inline-block; width:200px; margin-right:20px;">
                                <input type="radio" id="link_pos_1" value="front" name="link_pos"<?php Mage::helper('nwdrevslider/framework')->checked($link_pos, 'front'); ?>><span style="line-height:30px; vertical-align: middle; margin:0px 20px 0px 10px;"><?php echo $this->__('Front'); ?></span>
                                <input type="radio" id="link_pos_2" value="back" name="link_pos"<?php Mage::helper('nwdrevslider/framework')->checked($link_pos, 'back'); ?>><span style="line-height:30px; vertical-align: middle; margin:0px 20px 0px 10px;"><?php echo $this->__('Back'); ?></span>
                            </span>
                            <span class="description"><?php echo $this->__('The z-index position of the link related to layers'); ?></span>
                        </p>
                    </div>
				</div>
            <?php
            }
            ?>

		</form>

	</div>
</div>
<script type="text/javascript">
	var rs_plugin_url = '<?php echo Nwdthemes_Revslider_Helper_Framework::$RS_PLUGIN_URL; ?>';
	
	(function(jQuery) {
	jQuery('document').ready(function() {

        jQuery('.my-alphacolor-field').tpColorPicker({
            mode:'single',
            wrapper:'<span class="rev-colorpickerspan"></span>'
        });

        jQuery('#enable_link').change(function(){
			if(jQuery(this).val() == 'true'){
				jQuery('.rs-slide-link-setting-wrapper').show();
			}else{
				jQuery('.rs-slide-link-setting-wrapper').hide();
			}
		});
		jQuery('#enable_link option:selected').change();
		
		jQuery('input[name="link_type"]').change(function(){
			if(jQuery(this).val() == 'regular'){
				jQuery('.rs-regular-link-setting-wrap').show();
				jQuery('.rs-slide-to-slide').hide();
			}else{
				jQuery('.rs-regular-link-setting-wrap').hide();
				jQuery('.rs-slide-to-slide').show();
			}
		});
		jQuery('input[name="link_type"]:checked').change();
		
	});
	})($nwd_jQuery);
</script>