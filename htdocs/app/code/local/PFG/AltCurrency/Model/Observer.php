<?php
class PFG_AltCurrency_Model_Observer
{
    protected $templates = [
        [
            // Updated regex to match actual Bulgarian currency formatting without <sup> tags
            // Matches: <span class="price">12,34 лв.</span> or <span class="price">12,34&nbsp;лв.</span>
            'template' => '/(<span class="regular-price" id="product-price-(\d+)">.*?<span class="price">(\d+),(\d+)(?:&nbsp;|\s)*лв\.<\/span>.*?<span class="z-measure">\s*(.*?)\s*<\/span>.*?<\/span>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '<span class="regular-price alt-currency" id="product-price-eur-{{product_id}}">
    <span class="price">{{price}}&nbsp;{{symbol}}</span>
    <span class="z-measure">{{unit}}</span>
</span>',
        ],
        [
            // Fallback regex for the original format with <sup> tags (in case some products still use this)
            'template' => '/(<span class="regular-price" id="product-price-(\d+)">.*?<span class="price">(\d+),<sup>(\d+)<\/sup>(?:&nbsp;|\s)*лв\.<\/span>.*?<span class="z-measure">\s*(.*?)\s*<\/span>.*?<\/span>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '<span class="regular-price alt-currency" id="product-price-eur-{{product_id}}">
    <span class="price">{{price}}&nbsp;{{symbol}}</span>
    <span class="z-measure">{{unit}}</span>
</span>',
        ],
        [
            // Very flexible regex to catch any price format with лв. currency
            'template' => '/(<span class="regular-price" id="product-price-(\d+)">.*?<span class="price">([^<]*?)лв\.([^<]*?)<\/span>.*?<span class="z-measure">\s*(.*?)\s*<\/span>.*?<\/span>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '<span class="regular-price alt-currency" id="product-price-eur-{{product_id}}">
    <span class="price">{{price}}&nbsp;{{symbol}}</span>
    <span class="z-measure">{{unit}}</span>
</span>',
        ],
        [
            // Ultra-flexible regex - just look for any span with product-price id that contains лв.
            'template' => '/(<span[^>]*id="product-price-(\d+)"[^>]*>.*?лв\..*?<span class="z-measure">\s*(.*?)\s*<\/span>.*?<\/span>)/is',
            'replacement' => '__ORIGINAL____CONVERTED__',
            'converted_template' => '<span class="regular-price alt-currency" id="product-price-eur-{{product_id}}">
    <span class="price">{{price}}&nbsp;{{symbol}}</span>
    <span class="z-measure">{{unit}}</span>
</span>',
        ],
    ];

    public function appendConvertedPrice(Varien_Event_Observer $observer)
    {
        Mage::log("==== Observer triggered at " . date('Y-m-d H:i:s') . " ====", null, 'altcurrency_debug.log', true);

        try {
            $transport = $observer->getTransport();
            if (!$transport) {
                Mage::log("❌ Transport is missing.", null, 'altcurrency_debug.log', true);
                return;
            }

            $html = html_entity_decode($transport->getHtml(), ENT_QUOTES, 'UTF-8');
            $html = str_replace(['\\<', '\\>'], ['<', '>'], $html);

            if (!$html) {
                Mage::log("❌ HTML is empty.", null, 'altcurrency_debug.log', true);
                return;
            }

            if (strpos($html, 'alt-currency') !== false) {
                Mage::log("⚠️ alt-currency tag already present. Skipping.", null, 'altcurrency_debug.log', true);
                return;
            }

            if (strlen($html) < 10) {
                Mage::log("⚠️ HTML too short. Skipping.", null, 'altcurrency_debug.log', true);
                return;
            }

            // Only process HTML that contains price-related elements
            if (strpos($html, 'regular-price') === false && strpos($html, 'product-price') === false) {
                Mage::log("⚠️ HTML doesn't contain price elements. Skipping.", null, 'altcurrency_debug.log', true);
                return;
            }

            $rate = 1.95583;
            $symbol = '€';

            $originalHtml = $html;

            // Log a sample of the HTML to help debug the structure
            $htmlSample = substr($html, 0, 500);
            Mage::log("📄 HTML sample (first 500 chars): " . $htmlSample, null, 'altcurrency_debug.log', true);

            // Try each template until one matches
            foreach ($this->templates as $templateIndex => $template) {
                Mage::log("✅ Trying template #$templateIndex: " . $template['template'], null, 'altcurrency_debug.log', true);

                $tempHtml = preg_replace_callback($template['template'], function ($matches) use ($rate, $symbol, $template, $templateIndex) {
                    Mage::log("🔄 Match found with template #$templateIndex!", null, 'altcurrency_debug.log', true);
                    Mage::log(print_r($matches, true), null, 'altcurrency_debug.log', true);

                    $fullMatch = $matches[1];
                    $productId = $matches[2];

                    // Handle different regex patterns
                    if ($templateIndex == 2) { // Flexible regex pattern
                        // Extract price from the full price text
                        $priceText = $matches[3];
                        if (preg_match('/(\d+)[,.](\d+)/', $priceText, $priceMatches)) {
                            $majorPart = $priceMatches[1];
                            $minorPart = $priceMatches[2];
                        } else {
                            Mage::log("⚠️ Could not extract price from: $priceText", null, 'altcurrency_debug.log', true);
                            return $matches[0]; // Return original if we can't parse
                        }
                        $unit = trim($matches[5]);
                    } else { // Standard regex patterns
                        $majorPart = $matches[3];
                        $minorPart = $matches[4];
                        $unit = trim($matches[5]);
                    }

                    $priceBGN = floatval($majorPart . '.' . $minorPart);
                    $priceEUR = $priceBGN / $rate;
                    $formattedEUR = number_format($priceEUR, 2, ',', ' ');

                    Mage::log("💰 BGN: $priceBGN, EUR: $formattedEUR, ID: $productId, UNIT: $unit", null, 'altcurrency_debug.log', true);

                    // Build converted HTML using the template
                    $convertedHtml = $template['converted_template'];
                    $convertedHtml = str_replace(
                        ['{{product_id}}', '{{price}}', '{{symbol}}', '{{unit}}'],
                        [htmlspecialchars($productId, ENT_QUOTES, 'UTF-8'), $formattedEUR, $symbol, htmlspecialchars($unit, ENT_QUOTES, 'UTF-8')],
                        $convertedHtml
                    );

                    Mage::log("🧱 Injected converted block:\n" . $convertedHtml, null, 'altcurrency_debug.log', true);

                    return $fullMatch . "\n" . $convertedHtml;
                }, $html);

                if ($tempHtml !== $html) {
                    Mage::log("✅ HTML successfully converted with template #$templateIndex.", null, 'altcurrency_debug.log', true);
                    $html = $tempHtml;
                    break; // Exit the loop once we find a match
                } else {
                    Mage::log("⚠️ Template #$templateIndex: No matches found.", null, 'altcurrency_debug.log', true);
                }
            }

            if ($html === $originalHtml) {
                Mage::log("⚠️ No templates matched — HTML unchanged.", null, 'altcurrency_debug.log', true);
            }

            $transport->setHtml($html);

        } catch (Exception $e) {
            Mage::log('❌ Exception in appendConvertedPrice: ' . $e->getMessage(), null, 'altcurrency_error.log', true);
        }

        Mage::log("==== Observer completed ====\n", null, 'altcurrency_debug.log', true);
    }
}

