<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Admin
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_Admin>
            <version>1.6.1.3</version>
        </Mage_Admin>
    </modules>
    <global>
        <models>
            <admin>
                <class>Mage_Admin_Model</class>
                <resourceModel>admin_resource</resourceModel>
            </admin>
            <admin_resource>
                <class>Mage_Admin_Model_Resource</class>
                <deprecatedNode>admin_mysql4</deprecatedNode>
                <entities>
                    <user>
                        <table>admin_user</table>
                    </user>
                    <role>
                        <table>admin_role</table>
                    </role>
                    <rule>
                        <table>admin_rule</table>
                    </rule>
                    <permission_variable>
                        <table>permission_variable</table>
                    </permission_variable>
                    <permission_block>
                        <table>permission_block</table>
                    </permission_block>
                    <assert>
                        <table>admin_assert</table>
                    </assert>
                </entities>
            </admin_resource>
        </models>
        <helpers>
            <admin>
                <class>Mage_Admin_Helper</class>
            </admin>
        </helpers>
        <resources>
            <admin_setup>
                <setup>
                    <module>Mage_Admin</module>
                </setup>
            </admin_setup>
        </resources>
        <blocks>
            <admin>
                <class>Mage_Admin_Block</class>
            </admin>
        </blocks>
        <events>
            <admin_user_authenticate_after>
                <observers>
                    <admin_user_login>
                        <class>Mage_Admin_Model_Observer</class>
                        <method>actionAdminAuthenticate</method>
                    </admin_user_login>
                </observers>
            </admin_user_authenticate_after>
        </events>
    </global>
    <default>
        <admin>
            <emails>
                <forgot_email_template>admin_emails_forgot_email_template</forgot_email_template>
                <admin_notification_email_template>admin_emails_admin_notification_email_template</admin_notification_email_template>
                <forgot_email_identity>general</forgot_email_identity>
                <password_reset_link_expiration_period>2</password_reset_link_expiration_period>
            </emails>
        </admin>
    </default>
    <adminhtml>
        <layout>
            <updates>
                <admin>
                    <file>admin.xml</file>
                </admin>
            </updates>
        </layout>
    </adminhtml>
</config>
